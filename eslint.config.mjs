import typescript from "@typescript-eslint/eslint-plugin";
import typescriptParser from "@typescript-eslint/parser";

export default [
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    ignores: [
      // Auto-generated directories
      "node_modules/",
      ".next/",
      ".vercel/",
      "dist/",
      "build/",
      
      // Prisma auto-generated files
      "src/lib/zod/",
      "prisma/generated/",
      
      // shadcn/ui auto-generated components
      "src/components/ui/",
      
      // Configuration files
      "*.config.js",
      "*.config.mjs", 
      "*.config.ts",
      "tailwind.config.js",
      "next.config.js",
      
      // Next.js auto-generated files
      "next-env.d.ts",
      
      // Database and infrastructure scripts
      "prisma/sample-*.ts",
      "prisma/seed.ts",
      "supabase/scripts/",
      "supabase/functions/",
      
      // Other auto-generated or third-party files
      "public/",
      "coverage/"
    ],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
        project: "./tsconfig.json"
      }
    },
    plugins: {
      "@typescript-eslint": typescript
    },
    rules: {
      // Code Style Standards (MANDATORY) - matching existing codebase
      "quotes": ["error", "double"],
      "semi": ["error", "always"],
      
      // TypeScript Strict Standards (MANDATORY)
      "@typescript-eslint/no-explicit-any": "error",
      "@typescript-eslint/no-non-null-assertion": "error",
      "@typescript-eslint/prefer-nullish-coalescing": "warn",
      "@typescript-eslint/prefer-optional-chain": "warn",
      
      // Code Quality
      "@typescript-eslint/no-unused-vars": ["warn", { 
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_" 
      }],
      "no-console": ["warn", { "allow": ["warn", "error"] }]
    }
  },
  {
    // More lenient for development and auth routes (legacy code)
    files: [
      "src/app/(public)/**/*",
      "src/app/api/**/*"
    ],
    rules: {
      "no-console": "off",
      "@typescript-eslint/no-non-null-assertion": "warn"
    }
  },
  {
    // Strict enforcement for new DRY architecture files
    files: [
      "src/lib/api/**/*",
      "src/lib/services/**/*"
    ],
    rules: {
      "@typescript-eslint/no-explicit-any": "error",
      "@typescript-eslint/no-non-null-assertion": "error",
      "no-console": ["error", { "allow": ["warn", "error"] }]
    }
  },
  {
    // Completely skip linting for auto-generated content
    files: [
      "src/components/ui/**/*",
      "src/lib/zod/**/*",
      "prisma/generated/**/*"
    ],
    rules: {
      // Disable all rules for auto-generated files
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-non-null-assertion": "off",
      "@typescript-eslint/no-unused-vars": "off",
      "quotes": "off",
      "semi": "off",
      "no-console": "off"
    }
  }
];