import { z } from "zod";
import { RouteContext } from "./route-factory";
import { PolicyStateEnum, AuctionStateEnum } from "@prisma/client";

/**
 * Advanced validation middleware for complex business rules
 * Extends basic Zod validation with custom security checks
 */

/**
 * Security validation middleware
 * Addresses the race conditions and input sanitization issues we identified
 */
export class ValidationMiddleware {
  /**
   * Validate UUIDs in params/body to prevent injection
   */
  static async validateUUIDs(context: RouteContext<unknown, unknown>): Promise<void> {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

    // Check params
    if (context.params) {
      Object.entries(context.params).forEach(([key, value]) => {
        if (key.toLowerCase().includes("id") && !uuidRegex.test(value)) {
          throw new Error(`Invalid UUID format for parameter: ${key}`);
        }
      });
    }

    // Check body for ID fields
    if (context.body && typeof context.body === "object") {
      Object.entries(context.body).forEach(([key, value]) => {
        if (key.toLowerCase().includes("id") && typeof value === "string" && !uuidRegex.test(value)) {
          throw new Error(`Invalid UUID format for field: ${key}`);
        }
      });
    }
  }

  /**
   * Validate array inputs (addresses the selectedBidIds vulnerability)
   */
  static async validateArrayInputs(
    context: RouteContext<unknown, unknown>,
    fieldName: string,
    options: {
      maxLength?: number
      minLength?: number
      itemValidator?: z.ZodSchema
    } = {}
  ): Promise<void> {
    const { maxLength = 10, minLength = 1, itemValidator } = options;
    
    if (!context.body || typeof context.body !== "object") {
      return;
    }

    const fieldValue = (context.body as Record<string, unknown>)[fieldName];
    
    if (fieldValue !== undefined) {
      if (!Array.isArray(fieldValue)) {
        throw new Error(`${fieldName} must be an array`);
      }

      if (fieldValue.length < minLength || fieldValue.length > maxLength) {
        throw new Error(`${fieldName} must have between ${minLength} and ${maxLength} items`);
      }

      if (itemValidator) {
        fieldValue.forEach((item, index) => {
          try {
            itemValidator.parse(item);
          } catch {
            throw new Error(`Invalid item at index ${index} in ${fieldName}`);
          }
        });
      }
    }
  }

  /**
   * Prevent race conditions by validating entity state
   */
  static async validateEntityState<T>(
    entityGetter: () => Promise<T | null>,
    stateValidator: (entity: T) => boolean,
    errorMessage: string
  ): Promise<T> {
    const entity = await entityGetter();
    
    if (!entity) {
      throw new Error("Entity not found");
    }

    if (!stateValidator(entity)) {
      throw new Error(errorMessage);
    }

    return entity;
  }

  /**
   * Rate limiting validation (basic implementation)
   */
  static async validateRateLimit(
    _context: RouteContext<unknown, unknown>,
    _key: string,
    _maxRequests: number = 10,
    _windowMs: number = 60000
  ): Promise<void> {
    // In a real implementation, you'd use Redis or in-memory cache
    // For MVP, we'll skip this but the structure is here
    
    // const userId = context.user.id
    // const cacheKey = `rate_limit:${key}:${userId}`
    // 
    // Check rate limit logic here...
    
    // Rate limit implementation would go here
  }
}

/**
 * Common validation patterns for your business domain
 */
export const BusinessValidators = {
  /**
   * Validate auction winner selection
   */
  auctionWinnerSelection: async (context: RouteContext<unknown, unknown>) => {
    await ValidationMiddleware.validateArrayInputs(context, "selectedBidIds", {
      minLength: 1,
      maxLength: 3,
      itemValidator: z.string().uuid()
    });

    // Validate auction state atomically to prevent race conditions
    if (context.params?.id) {
      await ValidationMiddleware.validateEntityState(
        async () => {
          const { db } = await import("@/lib/db");
          const { AuctionStateService } = await import("@/lib/services/auction-state.service");
          const auction = await db.auction.findUnique({
            where: { id: context.params?.id ?? "" },
            select: { accountHolderId: true }
          });
          if (!auction) return null;
          const currentState = await AuctionStateService.getCurrentState(context.params?.id ?? "");
          return { ...auction, status: currentState };
        },
        (auction) => auction.status === AuctionStateEnum.CLOSED && auction.accountHolderId === context.user.id,
        "Auction must be closed and owned by user to select winners"
      );
    }
  },

  /**
   * Validate policy state update
   */
  policyStatusUpdate: async (context: RouteContext<unknown, unknown>) => {
    const validStates = Object.values(PolicyStateEnum);
    const body = context.body as Record<string, unknown>;
    const status = body?.status as string;
    
    if (!validStates.includes(status as PolicyStateEnum)) {
      throw new Error(`Invalid policy status: ${status}`);
    }

    // Atomic state validation
    if (context.params?.id) {
      await ValidationMiddleware.validateEntityState(
        async () => {
          const { db } = await import("@/lib/db");
          const { PolicyStateService } = await import("@/lib/services/policy-state.service");
          const policy = await db.policy.findUnique({
            where: { id: context.params?.id ?? "" },
            select: { id: true }
          });
          if (!policy) return null;
          const currentState = await PolicyStateService.getCurrentState(context.params?.id ?? "");
          return { ...policy, status: currentState };
        },
        (_policy) => {
          // Add business rules for valid status transitions
          return true; // Simplified for MVP
        },
        "Invalid policy status transition"
      );
    }
  },

  /**
   * Validate file upload
   */
  fileUpload: async (context: RouteContext<unknown, unknown>) => {
    // This would integrate with your existing file validation
    // but add extra security checks
    
    await ValidationMiddleware.validateRateLimit(context, "file_upload", 5, 300000); // 5 uploads per 5 minutes
  },

  /**
   * Validate document access permissions
   */
  documentAccess: async (context: RouteContext<unknown, unknown>) => {
    const query = context.query as Record<string, unknown>;
    const key = query?.key as string;
    
    if (!key) {
      throw new Error("Document key is required");
    }

    // Validate document access atomically
    await ValidationMiddleware.validateEntityState(
      async () => {
        const { db } = await import("@/lib/db");
        return db.documentation.findFirst({
          where: {
            url: key,
            OR: [
              {
                accountHolder: {
                  userId: context.user.id
                }
              },
              {
                broker: {
                  userId: context.user.id
                }
              }
            ]
          },
          select: { id: true, url: true }
        });
      },
      (document) => !!document,
      "Document not found or access denied"
    );
  }
};

/**
 * Factory for creating custom validators
 */
export function createValidator(
  validationFn: (context: RouteContext<unknown, unknown>) => Promise<void>
) {
  return validationFn;
}