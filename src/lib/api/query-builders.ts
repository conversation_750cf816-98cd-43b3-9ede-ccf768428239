import { db } from "@/lib/db";
import { PolicyStateEnum, AuctionStateEnum, Policy, PolicyState } from "@prisma/client";
// import { normalizeForSearch } from "@/lib/text-utils"; // Currently unused
import { createInsurerCompanySearchFilter, createNestedInsurerCompanySearchFilter } from "@/lib/utils/insurer-search";

/**
 * DRY Database Query Builders
 * Eliminates repetitive Prisma queries and includes
 */

export interface QueryOptions {
  page?: number
  limit?: number
  search?: string
  status?: string
  assetType?: string
  insurerCompany?: string
  filters?: Record<string, unknown>
}

export interface QueryResult<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

/**
 * Type for policy with state history included
 */
type PolicyWithStateHistory = Policy & {
  stateHistory?: PolicyState[]
}

/**
 * Base query builder with common patterns
 */
export class BaseQueryBuilder<T extends Record<string, unknown>> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  protected model: any;
  protected defaultIncludes: Record<string, unknown> = {};
  protected searchableFields: string[] = [];
  protected filterableFields: string[] = [];

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  constructor(model: any) {
    this.model = model;
  }

  /**
   * Set default includes for queries
   */
  withIncludes(includes: Record<string, unknown>) {
    this.defaultIncludes = includes;
    return this;
  }

  /**
   * Set searchable fields
   */
  searchIn(...fields: string[]) {
    this.searchableFields = fields;
    return this;
  }

  /**
   * Set filterable fields
   */
  filterBy(...fields: string[]) {
    this.filterableFields = fields;
    return this;
  }

  /**
   * Build where clause with search and filters
   */
  protected buildWhere(options: QueryOptions, baseWhere: Record<string, unknown> = {}): Record<string, unknown> {
    const where: Record<string, unknown> = { ...baseWhere };

    // Add search conditions
    if (options.search && this.searchableFields.length > 0) {
      const searchConditions = this.searchableFields.map(field => {
        if (field.includes(".")) {
          // Nested field search (e.g., 'user.firstName')
          const parts = field.split(".");
          const relation = parts[0];
          const subField = parts[1];
          if (!relation || !subField) return {};
          
          const condition: Record<string, unknown> = {};
          condition[relation] = {
            [subField]: {
              contains: options.search,
              mode: "insensitive"
            }
          };
          return condition;
        } else {
          // Direct field search
          const condition: Record<string, unknown> = {};
          condition[field] = {
            contains: options.search,
            mode: "insensitive"
          };
          return condition;
        }
      });
      where.OR = searchConditions;
    }

    // Status filtering is now handled post-query using PolicyState relationship
    // since getting the latest state in Prisma where clause is complex

    // Add custom filters
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (this.filterableFields.includes(key)) {
            where[key] = value;
          }
        }
      });
    }

    return where;
  }

  /**
   * Advanced search with accent normalization and premium matching
   */
  protected buildAdvancedSearch(options: QueryOptions, baseWhere: Record<string, unknown> = {}): Record<string, unknown> {
    const where: Record<string, unknown> = { ...baseWhere };

    if (!options.search) {
      return where;
    }

    const search = options.search;
    // const _normalizedSearch = normalizeForSearch(search); // Currently unused
    const searchAsNumber = parseFloat(search);
    const isNumericSearch = !isNaN(searchAsNumber);

    const orConditions: Record<string, unknown>[] = [
      // Basic text searches
      {
        policyNumber: {
          contains: search,
          mode: "insensitive"
        }
      },
      {
        productName: {
          contains: search,
          mode: "insensitive"
        }
      },
      // Vehicle search
      {
        asset: {
          vehicleDetails: {
            OR: [
              {
                brand: {
                  contains: search,
                  mode: "insensitive"
                }
              },
              {
                model: {
                  contains: search,
                  mode: "insensitive"
                }
              }
            ]
          }
        }
      },
      // Insured party search
      {
        insuredParties: {
          some: {
            insuredParty: {
              OR: [
                {
                  firstName: {
                    contains: search,
                    mode: "insensitive"
                  }
                },
                {
                  lastName: {
                    contains: search,
                    mode: "insensitive"
                  }
                },
                {
                  displayName: {
                    contains: search,
                    mode: "insensitive"
                  }
                }
              ]
            }
          }
        }
      },
      // Insurer company search using centralized utility
      createInsurerCompanySearchFilter(search)
    ];

    // Add premium search if numeric
    if (isNumericSearch) {
      const premiumExactCondition: Record<string, unknown> = {
        premium: {
          equals: searchAsNumber
        }
      };
      const premiumRangeCondition: Record<string, unknown> = {
        premium: {
          gte: searchAsNumber * 0.99,
          lte: searchAsNumber * 1.01
        }
      };
      orConditions.push(premiumExactCondition);
      orConditions.push(premiumRangeCondition);
    }

    where.OR = orConditions;

    return where;
  }

  /**
   * Execute paginated query
   */
  async query(options: QueryOptions, baseWhere: Record<string, unknown> = {}): Promise<QueryResult<T>> {
    const { page = 1, limit = 10 } = options;
    const skip = (page - 1) * limit;
    
    const where = this.buildWhere(options, baseWhere);

    const [data, total] = await Promise.all([
      this.model.findMany({
        where,
        include: this.defaultIncludes,
        skip,
        take: limit,
        orderBy: { updatedAt: "desc" }
      }),
      this.model.count({ where })
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: data as T[],
      total,
      page,
      limit,
      totalPages
    };
  }
}

/**
 * Policy query builder with common patterns
 */
export class PolicyQueryBuilder extends BaseQueryBuilder<Record<string, unknown>> {
  constructor() {
    super(db.policy);
    
    this.withIncludes({
      asset: {
        include: {
          vehicleDetails: true
        }
      },
      accountHolder: {
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      },
      document: {
        select: {
          id: true,
          fileName: true,
          fileSize: true,
          mimeType: true,
          url: true,
          uploadedAt: true
        }
      },
      coverages: true,
      insuredParties: {
        include: {
          insuredParty: true
        }
      },
      stateHistory: {
        orderBy: {
          createdAt: "desc"
        }
      }
    });

    this.searchIn(
      "policyNumber",
      "productName",
      "asset.vehicleDetails.brand",
      "asset.vehicleDetails.model",
      "insuredParties.insuredParty.firstName",
      "insuredParties.insuredParty.lastName"
    );

    this.filterBy("assetType", "insurerCompany"); // Removed "status" as it's now handled differently
  }

  /**
   * Get policies for specific account holder with advanced search
   */
  async forAccountHolder(accountHolderId: string, options: QueryOptions) {
    const baseWhere: Record<string, unknown> = { accountHolderId };
    
    // Extract status for post-query filtering and remove it from options
    const statusFilter = options.status;
    const optionsWithoutStatus = { ...options };
    delete optionsWithoutStatus.status;
    
    // Add asset type filter
    if (options.assetType && options.assetType !== "all") {
      baseWhere.asset = {
        assetType: options.assetType
      };
    }

    // Add insurer company filter
    if (options.insurerCompany && options.insurerCompany !== "all") {
      baseWhere.insurerCompany = options.insurerCompany;
    }

    const where = this.buildAdvancedSearch(optionsWithoutStatus, baseWhere);
    
    // Handle status filter using PolicyState relationship
    // Note: For now, we'll filter after the query since getting the latest state in Prisma is complex
    // TODO: Optimize this with a raw query or computed field if performance becomes an issue

    const { page = 1, limit = 10 } = options;
    const skip = (page - 1) * limit;

    const [allData, total] = await Promise.all([
      this.model.findMany({
        where,
        include: this.defaultIncludes,
        skip,
        take: limit,
        orderBy: { updatedAt: "desc" }
      }),
      this.model.count({ where })
    ]);

    // Filter by status after fetching (since status is derived from state history)
    let data = allData;
    if (statusFilter && statusFilter !== "all") {
      data = allData.filter((policy: PolicyWithStateHistory) => {
        // Get the most recent state
        const latestState = policy.stateHistory?.[0]?.state;
        
        if (statusFilter === "attention") {
          return latestState === PolicyStateEnum.RENEW_SOON || latestState === PolicyStateEnum.EXPIRED;
        }
        return latestState === statusFilter;
      });
    }

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages
    };
  }

  /**
   * Get policies visible to brokers (policies they bid on or won)
   */
  async forBroker(brokerId: string, options: QueryOptions) {
    // Brokers can see policies from auctions they participated in
    const baseWhere: Record<string, unknown> = {
      auctions: {
        some: {
          bids: {
            some: {
              brokerId: brokerId
            }
          }
        }
      }
    };
    
    return this.query(options, baseWhere);
  }

  /**
   * Get policies visible to admin (all policies)
   */
  async forAdmin(options: QueryOptions) {
    return this.query(options);
  }
}

/**
 * Auction query builder with enhanced policy-related search
 */
export class AuctionQueryBuilder extends BaseQueryBuilder<Record<string, unknown>> {
    constructor() {
      super(db.auction);
    
    this.withIncludes({
      policy: {
        include: {
          asset: {
            include: {
              vehicleDetails: true
            }
          }
        }
      },
      accountHolder: {
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true
            }
          }
        }
      },
      bids: {
        include: {
          broker: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        }
      },
      winners: {
        include: {
          broker: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            }
          }
        }
      }
    });

    this.searchIn(
      "policy.asset.vehicleDetails.brand",
      "policy.asset.vehicleDetails.model",
      "policy.asset.vehicleDetails.licensePlate"
    );

    this.filterBy("status");
  }

  /**
   * Enhanced search for auctions with policy data
   */
  protected buildAuctionSearch(options: QueryOptions, baseWhere: Record<string, unknown> = {}): Record<string, unknown> {
    const where: Record<string, unknown> = { ...baseWhere };

    if (!options.search) {
      return where;
    }

    const search = options.search;
    // const _normalizedSearch = normalizeForSearch(search); // Currently unused
    const searchAsNumber = parseFloat(search);
    const isNumericSearch = !isNaN(searchAsNumber);

    const orConditions: Record<string, unknown>[] = [
      // Vehicle search through policy
      {
        policy: {
          asset: {
            vehicleDetails: {
              OR: [
                {
                  brand: {
                    contains: search,
                    mode: "insensitive"
                  }
                },
                {
                  model: {
                    contains: search,
                    mode: "insensitive"
                  }
                }
              ]
            }
          }
        }
      },
      // Insurer company search through policy using centralized utility
      createNestedInsurerCompanySearchFilter(search)
    ];

    // Add premium search if numeric
    if (isNumericSearch) {
      const policyPremiumExactCondition: Record<string, unknown> = {
        policy: {
          premium: {
            equals: searchAsNumber
          }
        }
      };
      const policyPremiumRangeCondition: Record<string, unknown> = {
        policy: {
          premium: {
            gte: searchAsNumber * 0.99,
            lte: searchAsNumber * 1.01
          }
        }
      };
      orConditions.push(policyPremiumExactCondition);
      orConditions.push(policyPremiumRangeCondition);
    }

    where.OR = orConditions;

    return where;
  }

  /**
   * Get auctions for specific account holder with enhanced search
   */
  async forAccountHolder(accountHolderId: string, options: QueryOptions) {
    const baseWhere: Record<string, unknown> = { accountHolderId };
    
    // Add asset type filter through policy
    if (options.assetType && options.assetType !== "all") {
      baseWhere.policy = {
        asset: {
          assetType: options.assetType
        }
      };
    }

    const where = this.buildAuctionSearch(options, baseWhere);
    
    // Handle status filter with comma-separated support
    if (options.status && options.status !== "all") {
      const statuses = options.status.includes(",") ? options.status.split(",") : [options.status];
      where.status = { in: statuses };
    }

    const { page = 1, limit = 10 } = options;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.model.findMany({
        where,
        include: this.defaultIncludes,
        skip,
        take: limit,
        orderBy: { updatedAt: "desc" }
      }),
      this.model.count({ where })
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages
    };
  }

  /**
   * Get auctions for brokers (auctions they can bid on or have bid on)
   */
  async forBroker(brokerId: string, options: QueryOptions) {
    // Brokers can see active auctions and auctions they've participated in
    const baseWhere: Record<string, unknown> = {
      OR: [
        {
          status: AuctionStateEnum.OPEN,
          endDate: { gte: new Date() }
        },
        {
          bids: {
            some: {
              brokerId: brokerId
            }
          }
        }
      ]
    };
    
    return this.query(options, baseWhere);
  }

  /**
   * Get auctions for admin (all auctions)
   */
  async forAdmin(options: QueryOptions) {
    return this.query(options);
  }

  /**
   * Get active auctions for brokers to bid on
   */
  async activeAuctions(options: QueryOptions) {
    return this.query(options, { 
      status: AuctionStateEnum.OPEN,
      endDate: { gte: new Date() }
    });
  }
}

/**
 * Factory functions for common query builders
 */
export const QueryBuilders = {
  policies: () => new PolicyQueryBuilder(),
  auctions: () => new AuctionQueryBuilder(),
  
  /**
   * Generic builder for other models
   */
  for: <T extends Record<string, unknown>>(model: unknown) => new BaseQueryBuilder<T>(model)
};