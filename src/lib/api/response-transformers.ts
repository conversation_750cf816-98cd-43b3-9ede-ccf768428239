import { Policy, Auction, User, AccountHolderProfile, Asset, Vehicle, Documentation, Coverage, InsuredParty, PolicyInsuredParty, Bid, PolicyStateEnum, AuctionStateEnum } from "@prisma/client";
import { formatInsurerCompany } from "@/lib/format-insurer";
import { transformPolicyInsuredPartiesData } from "@/features/policies/utils/insured-party-transformer";
import { PolicyStateService } from "@/lib/services/policy-state.service";
import { AuctionStateService } from "@/lib/services/auction-state.service";

/**
 * Response Transformation Utilities
 * 
 * Centralizes data transformation logic for API responses
 * Eliminates duplication across multiple route files
 */

/**
 * Policy with all relations type
 */
export type PolicyWithRelations = Policy & {
  asset?: (Asset & {
    vehicleDetails?: Vehicle | null
  }) | null
  accountHolder?: (AccountHolderProfile & {
    user: Pick<User, "firstName" | "lastName">
  }) | null
  document?: Pick<Documentation, "id" | "fileName" | "fileSize" | "mimeType" | "url" | "uploadedAt"> | null
  coverages?: Coverage[]
  insuredParties?: (PolicyInsuredParty & {
    insuredParty: InsuredParty | null
  })[]
}

/**
 * Auction with all relations type
 */
export type AuctionWithRelations = Auction & {
  policy?: (Policy & {
    asset?: (Asset & {
      vehicleDetails?: Vehicle | null
    }) | null
  }) | null
  accountHolder?: (AccountHolderProfile & {
    user: Pick<User, "firstName" | "lastName">
  }) | null
  bids?: Bid[]
  winners?: unknown[] // TODO: Define proper winner type
}

/**
 * Transform policy data for list responses
 */
export async function transformPolicyForList(policy: PolicyWithRelations) {
  // Get current state from state history
  let currentState: PolicyStateEnum;
  try {
    currentState = await PolicyStateService.getCurrentState(policy.id);
  } catch {
    // Fallback for policies without state history
    currentState = PolicyStateEnum.DRAFT;
  }

  return {
    id: policy.id,
    policyNumber: policy.policyNumber,
    status: currentState,
    type: policy.asset?.assetType ?? "UNKNOWN",
    startDate: policy.startDate,
    endDate: policy.endDate,
    premium: policy.premium,
    productName: policy.productName,
    insurerCompany: policy.insurerCompany ? formatInsurerCompany(policy.insurerCompany) : null,
    asset: policy.asset ? {
      id: policy.asset.id,
      assetType: policy.asset.assetType,
      description: policy.asset.description,
      value: policy.asset.value,
      vehicleDetails: policy.asset.vehicleDetails ? {
        licensePlate: policy.asset.vehicleDetails.licensePlate,
        firstRegistrationDate: policy.asset.vehicleDetails.firstRegistrationDate,
        brand: policy.asset.vehicleDetails.brand,
        model: policy.asset.vehicleDetails.model,
        version: policy.asset.vehicleDetails.version,
        year: policy.asset.vehicleDetails.year,
        fuelType: policy.asset.vehicleDetails.fuelType,
        chassisNumber: policy.asset.vehicleDetails.chassisNumber,
        powerCv: policy.asset.vehicleDetails.powerCv,
        seats: policy.asset.vehicleDetails.seats,
        usageType: policy.asset.vehicleDetails.usageType,
        garageType: policy.asset.vehicleDetails.garageType,
        kmPerYear: policy.asset.vehicleDetails.kmPerYear,
        isLeased: policy.asset.vehicleDetails.isLeased
      } : null
    } : null,
    insuredParties: policy.insuredParties ? transformPolicyInsuredPartiesData(policy.insuredParties) : [],
    accountHolder: policy.accountHolder ? {
      id: policy.accountHolder.id,
      userId: policy.accountHolder.userId,
      firstName: policy.accountHolder.user.firstName,
      lastName: policy.accountHolder.user.lastName
    } : null,
    document: policy.document ? {
      id: policy.document.id,
      fileName: policy.document.fileName,
      fileSize: policy.document.fileSize,
      mimeType: policy.document.mimeType,
      url: policy.document.url,
      uploadedAt: policy.document.uploadedAt.toISOString()
    } : null,
    coverages: policy.coverages?.map(coverage => ({
      id: coverage.id,
      title: coverage.customName ?? coverage.type,
      type: coverage.type,
      guaranteeType: coverage.type,
      customName: coverage.customName,
      limit: coverage.limit ? Number(coverage.limit) : null,
      deductible: coverage.deductible ? Number(coverage.deductible) : null,
      description: coverage.description,
      // Enhanced Coverage model fields for rich display
      limitIsUnlimited: coverage.limitIsUnlimited,
      limitIsFullCost: coverage.limitIsFullCost,
      limitPerDay: coverage.limitPerDay ? Number(coverage.limitPerDay) : null,
      limitMaxDays: coverage.limitMaxDays,
      limitMaxMonths: coverage.limitMaxMonths,
      liabilityBodilyCap: coverage.liabilityBodilyCap ? Number(coverage.liabilityBodilyCap) : null,
      liabilityPropertyCap: coverage.liabilityPropertyCap ? Number(coverage.liabilityPropertyCap) : null,
      deductiblePercent: coverage.deductiblePercent ? Number(coverage.deductiblePercent) : null,
    })) ?? []
  };
}

/**
 * Transform auction data for list responses
 */
export async function transformAuctionForList(auction: AuctionWithRelations) {
  const id = auction.id;
  
  // Validate auction has required fields
  if (!id || typeof id !== 'string') {
    throw new Error(`Invalid auction data: missing or invalid ID for auction ${JSON.stringify(auction)}`);
  }
  
  const identifier = `ZEE-AU-${id.slice(-6).toUpperCase()}`;
  const premium = auction.policy?.premium?.toNumber() ?? 0;
  const insurer = auction.policy?.insurerCompany ?? null;

  // Get current state from state history
  let currentState: AuctionStateEnum;
  try {
    currentState = await AuctionStateService.getCurrentState(auction.id);
  } catch {
    // Fallback for auctions without state history
    currentState = AuctionStateEnum.OPEN;
  }

  // Asset display name logic
  let assetDisplayName = "Sin información del activo";
  const asset = auction.policy?.asset;
  if (asset?.vehicleDetails) {
    const v = asset.vehicleDetails;
    const brand = v.brand ?? "Sin marca";
    const model = v.model ?? "Sin modelo";
    const year = v.year ?? "Sin año";
    assetDisplayName = `${brand} ${model} (${year})`;
  } else if (asset?.description) {
    assetDisplayName = asset.description;
  }

  return {
    id,
    identifier,
    status: currentState,
    currentState: currentState, // Add currentState field that frontend expects
    startDate: auction.startDate?.toISOString?.() ?? null,
    endsAt: auction.endDate?.toISOString?.() ?? new Date().toISOString(),
    annualPremium: premium,
    currency: "EUR",
    currentInsurer: insurer ? formatInsurerCompany(insurer) : null,
    assetDisplayName,
    assetType: asset?.assetType ?? null,
    quotesReceived: auction.bids?.length ?? 0,
  };
}

/**
 * Transform asset data with vehicle details
 */
export function transformAssetData(asset: Asset & { vehicleDetails?: Vehicle | null } | null) {
  if (!asset) return null;
  
  return {
    id: asset.id,
    assetType: asset.assetType,
    description: asset.description,
    value: asset.value,
    vehicleDetails: asset.vehicleDetails ? {
      licensePlate: asset.vehicleDetails.licensePlate,
      firstRegistrationDate: asset.vehicleDetails.firstRegistrationDate,
      brand: asset.vehicleDetails.brand,
      model: asset.vehicleDetails.model,
      version: asset.vehicleDetails.version,
      year: asset.vehicleDetails.year,
      fuelType: asset.vehicleDetails.fuelType,
      chassisNumber: asset.vehicleDetails.chassisNumber,
      powerCv: asset.vehicleDetails.powerCv,
      seats: asset.vehicleDetails.seats,
      usageType: asset.vehicleDetails.usageType,
      garageType: asset.vehicleDetails.garageType,
      kmPerYear: asset.vehicleDetails.kmPerYear,
      isLeased: asset.vehicleDetails.isLeased
    } : null
  };
}

/**
 * Transform account holder data
 */
export function transformAccountHolderData(accountHolder: AccountHolderProfile & { user: Pick<User, "firstName" | "lastName"> } | null) {
  if (!accountHolder) return null;
  
  return {
    id: accountHolder.id,
    userId: accountHolder.userId,
    firstName: accountHolder.user.firstName,
    lastName: accountHolder.user.lastName
  };
}

/**
 * Transform document data
 */
export function transformDocumentData(document: Pick<Documentation, "id" | "fileName" | "fileSize" | "mimeType" | "url" | "uploadedAt"> | null) {
  if (!document) return null;
  
  return {
    id: document.id,
    fileName: document.fileName,
    fileSize: document.fileSize,
    mimeType: document.mimeType,
    url: document.url,
    uploadedAt: document.uploadedAt instanceof Date ? document.uploadedAt.toISOString() : new Date(document.uploadedAt).toISOString()
  };
}

/**
 * Transform coverage data
 */
export function transformCoverageData(coverages: Coverage[] | null | undefined) {
  if (!coverages) return [];
  
  return coverages.map(coverage => ({
    id: coverage.id,
    title: coverage.customName ?? coverage.type,
    type: coverage.type,
    guaranteeType: coverage.type,
    customName: coverage.customName,
    limit: coverage.limit ? Number(coverage.limit) : null,
    deductible: coverage.deductible ? Number(coverage.deductible) : null,
    description: coverage.description,
    // Enhanced Coverage model fields for rich display
    limitIsUnlimited: coverage.limitIsUnlimited,
    limitIsFullCost: coverage.limitIsFullCost,
    limitPerDay: coverage.limitPerDay ? Number(coverage.limitPerDay) : null,
    limitMaxDays: coverage.limitMaxDays,
    limitMaxMonths: coverage.limitMaxMonths,
    liabilityBodilyCap: coverage.liabilityBodilyCap ? Number(coverage.liabilityBodilyCap) : null,
    liabilityPropertyCap: coverage.liabilityPropertyCap ? Number(coverage.liabilityPropertyCap) : null,
    deductiblePercent: coverage.deductiblePercent ? Number(coverage.deductiblePercent) : null,
  }));
}

/**
 * Common transformation utilities for auction identifiers
 */
export const AuctionUtils = {
  /**
   * Generate auction identifier from ID
   */
  toIdentifier: (id: string): string => `ZEE-AU-${id.slice(-6).toUpperCase()}`,
  
  /**
   * Generate asset display name from asset data
   */
  getAssetDisplayName: (asset: Asset & { vehicleDetails?: Vehicle | null } | null | undefined): string => {
    if (!asset) return "Sin información del activo";
    
    if (asset.vehicleDetails) {
      const v = asset.vehicleDetails;
      const brand = v.brand ?? "Sin marca";
      const model = v.model ?? "Sin modelo";
      const year = v.year ?? "Sin año";
      return `${brand} ${model} (${year})`;
    }
    
    if (asset.description) {
      return asset.description;
    }
    
    return "Sin información del activo";
  }
};

/**
 * Simplified transformers for basic use cases
 */
export const SimpleTransformers = {
  /**
   * Basic policy transformation without heavy relations
   */
  policy: async (policy: Policy) => {
    let currentState: PolicyStateEnum;
    try {
      currentState = await PolicyStateService.getCurrentState(policy.id);
    } catch {
      currentState = PolicyStateEnum.DRAFT;
    }

    return {
      id: policy.id,
      policyNumber: policy.policyNumber,
      status: currentState,
      startDate: policy.startDate,
      endDate: policy.endDate,
      premium: policy.premium,
      productName: policy.productName,
      insurerCompany: policy.insurerCompany ? formatInsurerCompany(policy.insurerCompany) : null
    };
  },
  
  /**
   * Basic auction transformation without heavy relations
   */
  auction: async (auction: Auction) => {
    let currentState: AuctionStateEnum;
    try {
      currentState = await AuctionStateService.getCurrentState(auction.id);
    } catch {
      currentState = AuctionStateEnum.OPEN;
    }

    return {
      id: auction.id,
      identifier: AuctionUtils.toIdentifier(auction.id),
      status: currentState,
      startDate: auction.startDate?.toISOString() ?? null,
      endDate: auction.endDate?.toISOString() ?? null
    };
  }
};