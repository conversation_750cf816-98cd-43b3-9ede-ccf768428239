import { NextRequest } from "next/server";
import { getCurrentUser, getUserRole, requireRole } from "@/lib/api-auth";
import { handleApiError, createSuccessResponse, createPaginatedResponse } from "@/lib/api-responses";
import { ZodSchema, ZodObject, z, ZodRawShape } from "zod";
import { User } from "@supabase/supabase-js";
import { Role } from "@prisma/client";

/**
 * DRY API Route Factory for consistent endpoint creation
 * Eliminates boilerplate while maintaining type safety and security
 */

export interface RouteContext<QueryType = unknown, BodyType = unknown> {
  user: User
  role: string | null
  query?: QueryType
  body?: BodyType
  params?: Record<string, string>
  request: NextRequest
  pagination?: PaginationOptions
}

export interface PaginationOptions {
  page: number
  limit: number
  skip: number
}

export interface PaginationResult<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

class ApiRouteBuilder<QueryType = unknown, BodyType = unknown> {
  private authRole?: string;
  private requiresAuth = false;
  private querySchema?: ZodSchema<QueryType>;
  private bodySchema?: ZodSchema<BodyType>;
  private paginationEnabled = false;
  private customValidation?: (context: RouteContext<unknown, unknown>) => Promise<void>;

  /**
   * Require authentication with optional role validation
   */
  auth(role?: Role) {
    this.requiresAuth = true;
    this.authRole = role;
    return this;
  }

  /**
   * Validate query parameters with Zod schema
   */
  validateQuery<T>(schema: ZodSchema<T>) {
    this.querySchema = schema as unknown as ZodSchema<QueryType>;
    return this as unknown as ApiRouteBuilder<T, BodyType>;
  }

  /**
   * Validate request body with Zod schema
   */
  validateBody<T>(schema: ZodSchema<T>) {
    this.bodySchema = schema as unknown as ZodSchema<BodyType>;
    return this as unknown as ApiRouteBuilder<QueryType, T>;
  }

  /**
   * Enable automatic pagination support
   */
  paginate(defaultLimit = 10, maxLimit = 100) {
    this.paginationEnabled = true;
    
    // Extend query schema to include pagination params
    const paginationSchema = z.object({
      page: z.string().optional().default("1").transform(Number),
      limit: z.string().optional().default(defaultLimit.toString()).transform(val => {
        const num = Number(val);
        return num > maxLimit ? maxLimit : num;
      }),
    });

    if (this.querySchema) {
      // Handle merging for ZodObject types
      if (this.querySchema instanceof ZodObject) {
        this.querySchema = (this.querySchema as unknown as ZodObject<ZodRawShape>).merge(paginationSchema) as unknown as ZodSchema<QueryType>;
      } else {
        // For non-object schemas, create a new object that includes both
        this.querySchema = z.object({}).extend({}).merge(paginationSchema) as unknown as ZodSchema<QueryType>;
      }
    } else {
      this.querySchema = paginationSchema as unknown as ZodSchema<QueryType>;
    }

    return this;
  }

  /**
   * Add custom validation logic
   */
  validate(validator: (context: RouteContext<unknown, unknown>) => Promise<void>) {
    this.customValidation = validator;
    return this;
  }

  /**
   * Build the final route handler
   */
  handler<ReturnType = unknown>(
    handlerFn: (context: RouteContext<QueryType, BodyType>) => Promise<ReturnType>
  ) {
    return async (request: NextRequest, context: { params?: Promise<Record<string, string>> | Record<string, string> } = {}) => {
      try {
        // 1. Authentication
        let user: User | null = null;
        let role: string | null = null;

        if (this.requiresAuth) {
          user = await getCurrentUser(request);
          role = await getUserRole(user);
          
          if (this.authRole) {
            await requireRole(user, this.authRole);
          }
        }

        // 1.5. Handle params (compatible with Next.js 15)
        const params = context?.params 
          ? (context.params instanceof Promise ? await context.params : context.params)
          : undefined;

        // 2. Parse and validate query parameters
        let query: QueryType | undefined;
        if (this.querySchema) {
          const { searchParams } = new URL(request.url);
          const queryParams = Object.fromEntries(searchParams.entries());
          query = this.querySchema.parse(queryParams);
        }

        // 3. Parse and validate body (for POST/PUT/PATCH)
        let body: BodyType | undefined;
        if (this.bodySchema && ["POST", "PUT", "PATCH"].includes(request.method)) {
          const requestBody = await request.json();
          body = this.bodySchema.parse(requestBody);
        }

        // 4. Setup pagination if enabled
        let pagination: PaginationOptions | undefined;
        if (this.paginationEnabled && query) {
          const queryObj = query as { page?: number; limit?: number };
          const page = queryObj.page ?? 1;
          const limit = queryObj.limit ?? 10;
          pagination = {
            page,
            limit,
            skip: (page - 1) * limit
          };
        }

        // 5. Build context
        const handlerContext: RouteContext<QueryType, BodyType> = {
          user: user as User,
          role,
          query,
          body: body as BodyType,
          params,
          request,
          pagination
        };

        // 6. Custom validation
        if (this.customValidation) {
          await this.customValidation(handlerContext);
        }

        // 7. Execute handler
        const result = await handlerFn(handlerContext);

        // 8. Return appropriate response
        if (this.paginationEnabled && result && typeof result === "object" && "data" in result) {
          const paginatedResult = result as unknown as PaginationResult<unknown>;
          return createPaginatedResponse(
            paginatedResult.data,
            paginatedResult.page,
            paginatedResult.limit,
            paginatedResult.total
          );
        }

        return createSuccessResponse(result);

      } catch (error) {
        return handleApiError(error);
      }
    };
  }
}

/**
 * Create a new API route builder
 */
export function createApiRoute() {
  return new ApiRouteBuilder();
}

// Schema definitions have been consolidated to @/lib/schemas/index.ts
// All API routes should import CommonSchemas from @/lib/schemas for consistency

/**
 * Type-safe parameter validation
 */
export function validateParams<T>(params: unknown, schema: ZodSchema<T>): T {
  return schema.parse(params);
}