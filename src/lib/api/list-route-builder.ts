import { z } from "zod";
import { PolicyStateEnum, AuctionStateEnum, AssetType, InsurerCompany } from "@prisma/client";

/**
 * Centralized List Route Builder Utilities
 * 
 * Eliminates duplication across list endpoints for:
 * - Pagination schemas
 * - Filtering options
 * - Search parameters
 * - Common response transformations
 */

/**
 * Base search schema - reusable across all list endpoints
 */
export const BaseSearchSchema = z.object({
  search: z.string().optional()
});

/**
 * Status filter schemas using Prisma enums
 */
export const PolicyStatusFilterSchema = z.object({
  status: z.enum(["all", "attention"]).or(z.nativeEnum(PolicyStateEnum)).optional()
});

export const AuctionStateFilterSchema = z.object({
  status: z.enum(["all"]).or(z.nativeEnum(AuctionStateEnum)).optional()
});

/**
 * Asset type filter schema
 */
export const AssetTypeFilterSchema = z.object({
  assetType: z.nativeEnum(AssetType).optional()
});

/**
 * Insurer company filter schema
 */
export const InsurerCompanyFilterSchema = z.object({
  insurerCompany: z.nativeEnum(InsurerCompany).optional()
});

/**
 * Complete policy list schema
 */
export const PolicyListSchema = BaseSearchSchema
  .merge(PolicyStatusFilterSchema)
  .merge(AssetTypeFilterSchema)
  .merge(InsurerCompanyFilterSchema);

/**
 * Complete auction list schema
 */
export const AuctionListSchema = BaseSearchSchema
  .merge(AuctionStateFilterSchema)
  .merge(AssetTypeFilterSchema);

/**
 * Policy list with comma-separated status support
 */
export const AuctionListWithMultiStatusSchema = BaseSearchSchema
  .merge(z.object({
    status: z.string().optional() // Allow comma-separated values
  }))
  .merge(AssetTypeFilterSchema);

/**
 * Generic list response interface
 */
export interface ListResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

/**
 * Create paginated response helper
 */
export function createListResponse<T>(
  data: T[],
  total: number,
  page: number,
  limit: number
): ListResponse<T> {
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPreviousPage = page > 1;

  return {
    data,
    total,
    page,
    limit,
    totalPages,
    hasNextPage,
    hasPreviousPage
  };
}

/**
 * Extract pagination from query parameters
 */
export function extractPagination(query: Record<string, unknown>) {
  const page = Number(query.page ?? 1);
  const limit = Number(query.limit ?? 10);
  const skip = (page - 1) * limit;

  return {
    page,
    limit,
    skip
  };
}

/**
 * Handle status filter with special cases
 */
export function handlePolicyStatusFilter(status: string | undefined) {
  if (!status || status === "all") return undefined;
  
  if (status === "attention") {
    return {
      status: {
        in: [PolicyStateEnum.RENEW_SOON, PolicyStateEnum.EXPIRED]
      }
    };
  }
  
  return {
    status: status as PolicyStateEnum
  };
}

/**
 * Handle auction status filter with comma-separated support
 */
export function handleAuctionStatusFilter(status: string | undefined) {
  if (!status || status === "all") return undefined;
  
  // Support comma-separated statuses
  const statuses = status.includes(",") 
    ? status.split(",").map(s => s.trim() as AuctionStateEnum)
    : [status as AuctionStateEnum];
  
  return {
    status: {
      in: statuses
    }
  };
}

/**
 * Handle asset type filter
 */
export function handleAssetTypeFilter(assetType: string | undefined) {
  if (!assetType || assetType === "all") return undefined;
  
  return {
    asset: {
      assetType: assetType as AssetType
    }
  };
}

/**
 * Handle insurer company filter
 */
export function handleInsurerCompanyFilter(insurerCompany: string | undefined) {
  if (!insurerCompany || insurerCompany === "all") return undefined;
  
  return {
    insurerCompany: insurerCompany as InsurerCompany
  };
}

/**
 * Common list route options interface
 */
export interface ListRouteOptions {
  search?: string
  status?: string
  assetType?: string
  insurerCompany?: string
  page: number
  limit: number
  skip: number
}

/**
 * Parse and validate list route query parameters
 */
export function parseListRouteQuery<T extends z.ZodSchema>(
  searchParams: URLSearchParams,
  schema: T
): z.infer<T> & ListRouteOptions {
  const queryParams = Object.fromEntries(searchParams.entries());
  const validated = schema.parse(queryParams);
  const pagination = extractPagination(validated);
  
  return {
    ...validated,
    ...pagination
  };
}

/**
 * Schema factory for creating custom list schemas
 */
export function createCustomListSchema(additionalFields: z.ZodRawShape = {}) {
  return BaseSearchSchema
    .extend(additionalFields);
}

/**
 * Type helpers
 */
export type PolicyListQuery = z.infer<typeof PolicyListSchema>
export type AuctionListQuery = z.infer<typeof AuctionListSchema>
export type AuctionListWithMultiStatusQuery = z.infer<typeof AuctionListWithMultiStatusSchema>