/**
 * Centralized API utilities index
 * Single import for all DRY patterns
 */

// Core factory and builders
export { createApiRoute, validateParams, type RouteContext, type PaginationOptions } from "./route-factory";
export { QueryBuilders, BaseQueryBuilder, PolicyQueryBuilder, AuctionQueryBuilder, type QueryOptions, type QueryResult } from "./query-builders";

// Centralized schemas (recommended for all new implementations)
export { 
  CommonSchemas, 
  PolicySchemas, 
  AuctionSchemas, 
  AuthSchemas, 
  AdminSchemas,
  type PolicyStateEnum,
  type Role,
  type AuctionStateEnum 
} from "@/lib/schemas";

// Validation and security
export { ValidationMiddleware, BusinessValidators, createValidator } from "./validation-middleware";

// Re-export existing utilities for convenience
export { 
  getCurrentUser, 
  requireRole, 
  getUserRole, 
  hasAnyRole, 
  authenticateRequest,
  type ApiAuthError 
} from "@/lib/api-auth";

export { 
  createSuccessResponse,
  createErrorResponse,
  createPaginatedResponse,
  handleApiError,
  ApiResponses,
  type ApiSuccessResponse,
  type ApiErrorResponse
} from "@/lib/api-responses";

export { 
  validatePolicyDocument,
  FileValidationError,
  FILE_VALIDATION_CONFIGS,
  type FileValidationConfig
} from "@/lib/file-validation";

/**
 * Usage examples:
 * 
 * // Simple endpoint
 * export const GET = createApiRoute()
 *   .auth('ADMIN')
 *   .handler(async ({ user }) => {
 *     return { message: 'Hello admin!' }
 *   })
 * 
 * // Complex paginated endpoint
 * export const GET = createApiRoute()
 *   .auth('ACCOUNT_HOLDER')
 *   .validateQuery(z.object({ search: z.string().optional() }))
 *   .paginate()
 *   .validate(BusinessValidators.customValidator)
 *   .handler(async ({ user, query, pagination }) => {
 *     const result = await QueryBuilders.policies()
 *       .forAccountHolder(user.id, { ...query, ...pagination })
 *     return result
 *   })
 */