import { PrismaClient } from "@prisma/client";

// Use process.env directly to avoid potential issues with env.ts
const NODE_ENV = process.env.NODE_ENV ?? "development";

// Create a global variable to prevent multiple instances during hot reloading
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Initialize Prisma client with proper logging
export const db =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: NODE_ENV === "development" ? ["query", "error", "warn"] : ["error"],
  });

// Only save the instance in development to prevent memory leaks in production
if (NODE_ENV !== "production") globalForPrisma.prisma = db;
