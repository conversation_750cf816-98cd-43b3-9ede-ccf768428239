/**
 * Generic Brevo Email Service
 * Handles email notifications using Brevo Templates API
 * Templates are managed in the Brevo dashboard and referenced by ID
 */

import { logger } from "@/lib/logger";

export interface EmailRecipient {
  email: string;
  name?: string;
}

export interface TemplateParams {
  [key: string]: string | number | boolean | undefined;
}

export class BrevoEmailService {
  private apiKey: string;
  private senderEmail: string;
  private senderName: string;
  private baseUrl = "https://api.brevo.com/v3";

  constructor() {
    this.apiKey = process.env.BREVO_API_KEY ?? "";
    this.senderEmail = process.env.BREVO_SENDER_EMAIL ?? "<EMAIL>";
    this.senderName = process.env.BREVO_SENDER_NAME ?? "Zeeguros";

    if (!this.apiKey) {
      throw new Error("BREVO_API_KEY environment variable is required");
    }
  }

  /**
   * Send email using Brevo Template API
   * @param to - Single recipient or array of recipients
   * @param templateId - Brevo template ID
   * @param params - Key-value parameters to pass to the template
   * @param replyTo - Optional reply-to address
   */
  async sendTemplateEmail(
    to: EmailRecipient | EmailRecipient[],
    templateId: number,
    params: TemplateParams = {},
    replyTo?: EmailRecipient
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Normalize to array - handle both single recipient and array
      const recipients = Array.isArray(to) ? to : [to];

      const payload = {
        sender: {
          name: this.senderName,
          email: this.senderEmail,
        },
        to: recipients.map(recipient => ({
          email: recipient.email,
          name: recipient.name ?? recipient.email,
        })),
        templateId,
        params,
        replyTo: replyTo ? {
          email: replyTo.email,
          name: replyTo.name ?? replyTo.email,
        } : undefined,
      };

      const response = await fetch(`${this.baseUrl}/smtp/email`, {
        method: "POST",
        headers: {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "api-key": this.apiKey,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Brevo API error: ${response.status} - ${errorData.message ?? "Unknown error"}`);
      }

      const result = await response.json();
      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error) {
      logger.error("Failed to send template email via Brevo", error, {
        templateId,
        recipientCount: Array.isArray(to) ? to.length : 1
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }


  /**
   * Test email connectivity
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/account`, {
        method: "GET",
        headers: {
          "Accept": "application/json",
          "api-key": this.apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`Brevo API test failed: ${response.status}`);
      }

      return { success: true };
    } catch (error) {
      logger.error("Brevo API connection test failed", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
}

// Export singleton instance
export const brevoEmailService = new BrevoEmailService();

/**
 * Usage Examples:
 * 
 * // In an API route or server action:
 * import { brevoEmailService } from "@/lib/email/brevo.service";
 * 
 * // Send to single recipient
 * await brevoEmailService.sendTemplateEmail(
 *   { email: "<EMAIL>", name: "John Doe" },
 *   1, // Template ID from Brevo dashboard
 *   {
 *     accountHolderName: "John Doe",
 *     policyNumber: "POL123",
 *     auctionUrl: "https://example.com/auction/123"
 *   }
 * );
 * 
 * // Send to multiple recipients
 * await brevoEmailService.sendTemplateEmail(
 *   [
 *     { email: "<EMAIL>", name: "John" },
 *     { email: "<EMAIL>", name: "Jane" },
 *     { email: "<EMAIL>", name: "Admin" }
 *   ],
 *   2, // Template ID
 *   { eventType: "AUCTION_CLOSED", customData: "Any value" }
 * );
 */
