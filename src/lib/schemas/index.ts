/**
 * Centralized Schema Architecture - Prisma First Design
 * 
 * This file provides a systematic approach to schema management:
 * 1. Uses Prisma enums as single source of truth
 * 2. Creates composite schemas for common UI and API patterns
 * 3. Ensures architectural consistency across the entire codebase
 */

import { z } from "zod";

// Dynamic import for phone validation (server-side safe)
let isValidPhoneNumber: ((phone: string) => boolean) | undefined;

// Only import on client-side or when needed
const getPhoneValidator = async () => {
  if (typeof window !== 'undefined' && !isValidPhoneNumber) {
    const { isValidPhoneNumber: validator } = await import('react-phone-number-input');
    isValidPhoneNumber = validator;
  }
  return isValidPhoneNumber;
};

// Server-side fallback validation
const serverSidePhoneValidation = (phone: string): boolean => {
  // Basic international phone number validation for server-side
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
  return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
};

// Unified phone validation that works on both client and server
const validatePhoneNumber = (phone: string): boolean => {
  if (typeof window !== 'undefined' && isValidPhoneNumber) {
    return isValidPhoneNumber(phone);
  }
  return serverSidePhoneValidation(phone);
};

// Import Prisma types and enums directly (SINGLE SOURCE OF TRUTH)
import {
  PolicyStateEnum,
  AssetType,
  Role,
  AuctionStateEnum,
  GuaranteeType,
  InsurerCompany,
  PaymentPeriod,
  FuelType,
  UsageType,
  KmRange,
  GarageType,
  PartyRole
} from "@prisma/client";

// Create Zod schemas from Prisma enums (PRISMA AS SINGLE SOURCE OF TRUTH)
export const PolicyStateSchema = z.nativeEnum(PolicyStateEnum);
export const AssetTypeSchema = z.nativeEnum(AssetType);
export const RoleSchema = z.nativeEnum(Role);
export const AuctionStateSchema = z.nativeEnum(AuctionStateEnum);
export const GuaranteeTypeSchema = z.nativeEnum(GuaranteeType);
export const InsurerCompanySchema = z.nativeEnum(InsurerCompany);
export const PaymentPeriodSchema = z.nativeEnum(PaymentPeriod);
export const FuelTypeSchema = z.nativeEnum(FuelType);
export const UsageTypeSchema = z.nativeEnum(UsageType);
export const KmRangeSchema = z.nativeEnum(KmRange);
export const GarageTypeSchema = z.nativeEnum(GarageType);
export const PartyRoleSchema = z.nativeEnum(PartyRole);

// Re-export Prisma types for type-safe usage
export type { 
  PolicyStateEnum, 
  AssetType, 
  Role, 
  AuctionStateEnum, 
  GuaranteeType,
  InsurerCompany,
  PaymentPeriod,
  FuelType,
  UsageType,
  KmRange,
  GarageType,
  PartyRole
};

/**
 * Common validation patterns using Prisma schemas
 */
export const CommonSchemas = {
  /**
   * UUID validation schema
   */
  UUID: z.string().uuid("Invalid UUID format"),
  
  /**
   * Basic pagination schema
   */
  Pagination: z.object({
    page: z.string().optional().default("1").transform(Number),
    limit: z.string().optional().default("10").transform(Number),
  }),

  /**
   * ID parameter validation
   */
  IDParam: z.object({
    id: z.string().uuid("Invalid ID format")
  }),

  /**
   * Search and filter schema
   */
  SearchAndFilter: z.object({
    search: z.string().optional(),
    status: z.string().optional(),
    page: z.string().optional().default("1").transform(Number),
    limit: z.string().optional().default("10").transform(Number),
  }),

  /**
   * Document download query schema
   */
  DocumentDownload: z.object({
    key: z.string().min(1, "Document key is required")
  }),

  /**
   * Status update pattern for policies and auctions
   */
  StatusUpdate: z.object({
    status: z.union([PolicyStateSchema, AuctionStateSchema])
  }),
};

/**
 * Policy-specific schema builders
 */
const PolicyStateFilter = z.enum(["all", "attention"]).or(PolicyStateSchema);

const PolicyCreateForm = z.object({
  type: AssetTypeSchema.refine(
    (val) => val === "CAR" || val === "MOTORCYCLE",
    { message: "Asset type must be CAR or MOTORCYCLE" }
  ),
  file: z.unknown().optional(),
  termsAccepted: z.boolean().refine(
    (val) => val === true,
    { message: "Terms and conditions must be accepted" }
  )
});

const PolicyListQuery = CommonSchemas.SearchAndFilter.extend({
  status: PolicyStateFilter.optional(),
  assetType: AssetTypeSchema.optional(),
});

/**
 * Policy-specific schemas combining Prisma schemas
 */
export const PolicySchemas = {
  CreateForm: PolicyCreateForm,
  StatusFilter: PolicyStateFilter,
  ListQuery: PolicyListQuery,
  StatusUpdate: z.object({
    status: PolicyStateSchema
  }),
};

/**
 * Auction-specific schemas
 */
export const AuctionSchemas = {
  /**
   * Bid creation schema (send offer)
   */
  CreateBid: z.object({
    policyId: CommonSchemas.UUID,
    annualPremium: z.number().positive("Annual premium must be positive"),
    fileUrl: z.string().url("Invalid file URL").optional(),
  }),

  /**
   * Bid placement form schema for brokers
   */
  BidForm: z.object({
    bidAmount: z
      .string()
      .min(1, "El monto de la oferta es requerido")
      .refine((val) => !isNaN(Number(val)) && Number(val) > 0, "Debe ser un número válido mayor a 0")
      .refine((val) => Number(val) >= 50, "El monto mínimo es €50")
      .refine((val) => Number(val) <= 10000, "El monto máximo es €10,000"),
    coverageImprovements: z
      .string()
      .min(10, "Describe al menos 10 caracteres sobre las mejoras de cobertura")
      .max(500, "Máximo 500 caracteres"),
    proposedPremium: z
      .string()
      .min(1, "La prima propuesta es requerida")
      .refine((val) => !isNaN(Number(val)) && Number(val) > 0, "Debe ser un número válido mayor a 0")
      .refine((val) => Number(val) >= 100, "La prima mínima es €100")
      .refine((val) => Number(val) <= 50000, "La prima máxima es €50,000"),
    message: z
      .string()
      .max(1000, "Máximo 1000 caracteres")
      .optional(),
  }),

  /**
   * Winner selection schema
   */
  SelectWinners: z.object({
    selectedBidIds: z.array(CommonSchemas.UUID).min(1).max(3),
  }),
};

/**
 * Authentication schemas
 */
export const AuthSchemas = {
  /**
   * User role validation
   */
  UserRole: RoleSchema,

  /**
   * Basic profile schema
   */
  ProfileUpdate: z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    phone: z.string().optional(),
  }),

  /**
   * Password update schema for users with existing passwords
   */
  PasswordUpdate: z.object({
    currentPassword: z.string().min(8, "La contraseña actual debe tener al menos 8 caracteres."),
    newPassword: z.string()
      .min(8, "La contraseña debe tener al menos 8 caracteres.")
      .regex(/[A-Z]/, "La contraseña debe contener al menos una letra mayúscula.")
      .regex(/[a-z]/, "La contraseña debe contener al menos una letra minúscula.")
      .regex(/[0-9]/, "La contraseña debe contener al menos un número."),
  }),

  /**
   * Initial password setup schema for users without passwords
   */
  PasswordSetInitial: z.object({
    newPassword: z.string()
      .min(8, "La contraseña debe tener al menos 8 caracteres.")
      .regex(/[A-Z]/, "La contraseña debe contener al menos una letra mayúscula.")
      .regex(/[a-z]/, "La contraseña debe contener al menos una letra minúscula.")
      .regex(/[0-9]/, "La contraseña debe contener al menos un número."),
  }),

  /**
   * Client-side phone validation using react-phone-number-input
   */
  ClientPhoneValidation: z
    .string()
    .refine(validatePhoneNumber, { message: "El número de teléfono no es válido." }),

  /**
   * Client-side phone schema object
   */
  ClientPhoneSchema: z.object({
    phone: z.string().refine(validatePhoneNumber, { message: "El número de teléfono no es válido." }),
  }),

  /**
   * Server-side phone validation with basic regex
   */
  ServerPhoneSchema: z.object({
    phone: z.string()
      .min(8, { message: "El número de teléfono parece demasiado corto." })
      .regex(/^[\d\s()+-]+$/, { message: "El número de teléfono contiene caracteres no válidos." }),
  }),

  /**
   * Client-side signup form schema
   */
  SignupForm: z.object({
    firstName: z.string().min(1, { message: "El nombre es obligatorio." }),
    lastName: z.string().min(1, { message: "Los apellidos son obligatorios." }),
    phone: z.string().refine(validatePhoneNumber, { message: "El número de teléfono no es válido." }),
    email: z.string().email({ message: "Por favor, introduce un correo electrónico válido." }),
    password: z.string().min(8, { message: "La contraseña debe tener al menos 8 caracteres." }),
    confirmPassword: z.string(),
  }).refine(data => data.password === data.confirmPassword, {
    message: "Las contraseñas no coinciden.",
    path: ["confirmPassword"],
  }),

  /**
   * Server-side signup validation with E.164 phone format
   */
  ServerSignupForm: z.object({
    firstName: z.string().min(1),
    lastName: z.string().min(1),
    phone: z.string().regex(/^\+?[1-9]\d{1,14}$/),
    email: z.string().email(),
    password: z.string().min(8),
    confirmPassword: z.string(),
  }).refine(data => data.password === data.confirmPassword, {
    path: ["confirmPassword"],
  }),
};

/**
 * Admin-specific schemas for complex data management
 */
export const AdminSchemas = {
  /**
   * Comprehensive policy data form schema for admin review/editing
   * Used in PolicyDataForm component for complex policy management
   */
  PolicyDataForm: z
    .object({
      policyNumber: z.string().min(1, "Número de póliza es requerido"),
      insurerName: z.string().min(1, "Aseguradora es requerida"),
      policyType: z.string().nullable(), // Manual string for now until PolicyType schema is available
      productName: z.string().nullable(),
      startDate: z.coerce.date().nullable(),
      endDate: z.coerce.date().nullable(),
      paymentPeriod: PaymentPeriodSchema.nullable(),
      premium: z.number().nullable(),
      insuredParties: z.array(
        z.object({
          personId: z.string().optional(),
          fullName: z.string().min(1, "Nombre es requerido"),
          dni: z.string().min(1, "DNI/NIE/NIF es requerido"),
          roles: z.array(PartyRoleSchema),
        })
      ),
      brand: z.string().min(1, "Marca es requerida"),
      model: z.string().min(1, "Modelo es requerido"),
      year: z.number().nullable(),
      version: z.string().nullable(),
      chassisNumber: z.string().min(1, "Número de bastidor es requerido"),
      licensePlate: z.string().min(1, "Matrícula es requerida"),
      firstRegistrationDate: z.coerce.date().nullable(),
      type: AssetTypeSchema.nullable(), // Uses AssetType schema
      fuelType: FuelTypeSchema.nullable(),
      usageType: UsageTypeSchema.nullable(),
      kmPerYear: KmRangeSchema.nullable(),
      garageType: GarageTypeSchema.nullable(),
      seats: z.number().nullable(),
      powerCv: z.number().nullable(),
      isLeased: z.boolean(),
      coverages: z.array(
        z.object({
          id: z.string().optional(),
          type: GuaranteeTypeSchema,
          customName: z.string().optional(),
          limit: z.number().optional(),
          deductible: z.number().optional(),
          description: z.string().optional(),
        })
      ),
    })
    .refine(
      (data) => {
        if (data.startDate && data.endDate) {
          return data.endDate > data.startDate;
        }
        return true;
      },
      {
        message: "Fecha de vencimiento debe ser posterior a la fecha de inicio",
        path: ["endDate"],
      }
    ),
};

/**
 * Type exports for auth schemas
 */
export type SignupFormValues = z.infer<typeof AuthSchemas.SignupForm>;
export type PhoneFormValues = z.infer<typeof AuthSchemas.ClientPhoneSchema>;
export type ServerSignupFormValues = z.infer<typeof AuthSchemas.ServerSignupForm>;