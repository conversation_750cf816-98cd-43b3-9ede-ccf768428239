/**
 * Structured logging service for the application
 * Provides different log levels and handles development vs production logging
 */

interface LogContext {
  [key: string]: unknown;
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === "development";

  /**
   * Debug logging - only shown in development
   */
  debug(message: string, context?: LogContext) {
    if (this.isDevelopment) {
      // eslint-disable-next-line no-console
      console.log(`[DEBUG] ${message}`, context ?? "");
    }
  }

  /**
   * Info logging - general application flow
   */
  info(message: string, context?: LogContext) {
    // eslint-disable-next-line no-console
    console.info(`[INFO] ${message}`, context ?? "");
  }

  /**
   * Warning logging - potential issues that don't break functionality
   */
  warn(message: string, context?: LogContext) {
    // eslint-disable-next-line no-console
    console.warn(`[WARN] ${message}`, context ?? "");
  }

  /**
   * Error logging - errors that need attention
   * In production, these should be sent to monitoring services
   */
  error(message: string, error?: unknown, context?: LogContext) {
    // eslint-disable-next-line no-console
    console.error(`[ERROR] ${message}`, error, context ?? "");
    
    // TODO: In production, send to monitoring service (e.g., Sentry, LogRocket)
    // if (process.env.NODE_ENV === 'production') {
    //   sendToMonitoringService({ level: 'error', message, error, context });
    // }
  }

  /**
   * Business event logging - important business logic events
   */
  business(message: string, context?: LogContext) {
    // eslint-disable-next-line no-console
    console.info(`[BUSINESS] ${message}`, context ?? "");
    
    // TODO: In production, send to analytics/audit service
    // if (process.env.NODE_ENV === 'production') {
    //   sendToAuditService({ message, context });
    // }
  }
}

export const logger = new Logger();