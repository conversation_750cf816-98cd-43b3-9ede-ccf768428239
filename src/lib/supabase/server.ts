import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";

/**
 * Creates a server-side Supabase client using the anon key for user-facing operations.
 * This client respects RLS policies and should be used for operations where you want
 * to enforce user permissions.
 *
 * Use this client for:
 * - User authentication in server components
 * - Database operations that should respect RLS policies
 * - Operations where you want to enforce user-level permissions
 *
 * For admin operations that need to bypass RLS, use createServerAdminClient instead.
 */
export async function createClient() {
  try {
    const cookieStore = await cookies();

    // Ensure environment variables are defined
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error("Missing Supabase environment variables");
    }

    return createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          get(name) {
            return cookieStore.get(name)?.value;
          },
          set(name, value, options) {
            cookieStore.set(name, value, options);
          },
          remove(name, options) {
            cookieStore.set(name, "", { ...options, maxAge: 0 });
          },
        },
      }
    );
  } catch (error) {
    throw error;
  }
}
