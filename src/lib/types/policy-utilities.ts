import { 
  Policy, 
  Coverage, 
  BidCoverage,
  PolicyStateEnum, 
  AssetType, 
  GuaranteeType,
  Documentation,
  Bid 
} from "@prisma/client";

/**
 * Policy utility types following CLAUDE.md schema architecture
 * Uses Prisma types as single source of truth
 */

// Re-export Prisma types for backward compatibility and clear naming
export type { PolicyStateEnum as PolicyStatus, AssetType, GuaranteeType };

/**
 * Policy with related data for detailed views
 * Uses Prisma types with proper relationships
 */
export type PolicyWithRelations = Policy & {
  coverages: Coverage[];
  document?: Documentation | null;
};

/**
 * Extended policy data structure for UI transformations
 * Maintains backward compatibility with existing PolicyData interface
 * while using Prisma types as foundation
 */
export interface PolicyUIData {
  // Core Policy fields from Prisma
  id: string;
  policyNumber: string;
  status: PolicyStateEnum;
  
  // UI-specific fields (computed/transformed)
  insurer: string;
  product: string;
  policyType: string;
  validity: string;
  annualPremium: string;
  holderName: string;
  birthDate: string;
  gender: string;
  phone: string;
  email: string;
  
  // Vehicle fields for display
  vehiclePlate: string;
  vehicleFirstRegistrationDate: string;
  vehicleBrand: string;
  vehicleModel: string;
  vehicleVersion: string;
  vehicleManufacturingYear: string;
  vehicleType: string;
  vehicleFuelType: string;
  vehicleVin: string;
  vehiclePower: string;
  vehicleSeats: string;
  vehicleUsageType: string;
  vehicleGarageType: string;
  vehicleKmPerYear: string;
  vehicleIsLeased: string;
  
  // Related data
  coverages: PolicyCoverageUI[];
  document?: {
    id: string;
    fileName: string | null;
    fileSize: number | null;
    mimeType: string | null;
    url: string;
    uploadedAt: string;
  } | null;
}

/**
 * UI-friendly coverage interface that bridges Prisma Coverage and UI needs
 */
export interface PolicyCoverageUI {
  // Basic fields for legacy compatibility
  title: string;
  limit: number | null;
  description: string;
  guaranteeType: GuaranteeType;

  // Enhanced fields from Prisma Coverage model  
  id?: string;
  customName?: string | null;
  limitIsUnlimited?: boolean;
  limitIsFullCost?: boolean;
  limitPerDay?: number | null;
  limitMaxDays?: number | null;
  limitMaxMonths?: number | null;
  liabilityBodilyCap?: number | null;
  liabilityPropertyCap?: number | null;
  deductible?: number | null;
  deductiblePercent?: number | null;
}

/**
 * Bid comparison data using Prisma types
 */
export type BidComparisonData = {
  id: string;
  annualPremium: number;
  brokerName: string;
  brokerCompany: string;
  createdAt: string;
  hasDocument: boolean;
  bidCoverages?: unknown[]; // Keep as unknown for backward compatibility with existing transformations
};

/**
 * Contact data for closed auctions
 */
export interface ContactData {
  brokerName: string;
  brokerIdentifier: string;
  brokerCompany: string;
  brokerPhone: string;
  brokerEmail: string;
  quoteDocument?: {
    id: string;
    fileName: string | null;
    fileSize: number | null;
    uploadedAt: string;
    url: string;
  };
}

/**
 * Props for PolicyDetailsDrawer component
 */
export interface PolicyDetailsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  mode?: "broker" | "account-holder" | "comparison" | "contact" | "contact-only";
  policyData?: PolicyUIData;
  comparisonData?: {
    bid: BidComparisonData;
    showComparison: boolean;
  };
  contactData?: ContactData;
}

// Legacy compatibility exports - gradually phase these out
export type PolicyData = PolicyUIData;
export type PolicyCoverage = PolicyCoverageUI;