/**
 * Winner Selection Service
 * Implements automatic winner selection algorithm for auction closure
 */

export interface BidWithBroker {
  id: string;
  auctionId: string;
  brokerId: string;
  amount: number;
  coverageDetails?: string;
  createdAt: Date;
  broker: {
    id: string;
    user: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      displayName?: string;
      phone?: string;
    };
    companyName?: string;
  };
}

export interface SelectedWinner {
  bidId: string;
  brokerId: string;
  position: number;
  bidAmount: number;
  brokerName: string;
  brokerContact: {
    name: string;
    email: string;
    phone?: string;
  };
  selectionScore: number;
  selectionCriteria: string;
}

export interface WinnerSelectionResult {
  winners: SelectedWinner[];
  totalBids: number;
  selectionCriteria: string;
  processingDuration: number;
}

export class WinnerSelectionService {
  /**
   * Automatically select top 3 winners based on business criteria
   * Primary: Lowest price offered
   * Secondary: Most comprehensive coverage (based on coverage details length and quality)
   */
  async selectWinners(bids: BidWithBroker[]): Promise<WinnerSelectionResult> {
    const startTime = Date.now();
    
    if (bids.length === 0) {
      return {
        winners: [],
        totalBids: 0,
        selectionCriteria: "No bids available for selection",
        processingDuration: Date.now() - startTime,
      };
    }

    // Score each bid based on our criteria
    const scoredBids = bids.map(bid => {
      const priceScore = this.calculatePriceScore(bid, bids);
      const coverageScore = this.calculateCoverageScore(bid);
      
      // Weighted scoring: 70% price, 30% coverage
      const totalScore = (priceScore * 0.7) + (coverageScore * 0.3);
      
      return {
        ...bid,
        priceScore,
        coverageScore,
        totalScore,
      };
    });

    // Sort by total score (highest first) and take top 3
    const sortedBids = scoredBids
      .sort((a, b) => b.totalScore - a.totalScore)
      .slice(0, 3);

    // Convert to winner format
    const winners: SelectedWinner[] = sortedBids.map((bid, index) => ({
      bidId: bid.id,
      brokerId: bid.brokerId,
      position: index + 1,
      bidAmount: bid.amount,
      brokerName: this.getBrokerDisplayName(bid.broker),
      brokerContact: {
        name: this.getBrokerDisplayName(bid.broker),
        email: bid.broker.user.email,
        phone: bid.broker.user.phone,
      },
      selectionScore: bid.totalScore,
      selectionCriteria: `Price Score: ${bid.priceScore.toFixed(2)}, Coverage Score: ${bid.coverageScore.toFixed(2)}`,
    }));

    const processingDuration = Date.now() - startTime;

    return {
      winners,
      totalBids: bids.length,
      selectionCriteria: "Automatic selection based on lowest price (70%) and most comprehensive coverage (30%)",
      processingDuration,
    };
  }

  /**
   * Calculate price score (lower price = higher score)
   * Uses inverse scoring where the lowest price gets the highest score
   */
  private calculatePriceScore(bid: BidWithBroker, allBids: BidWithBroker[]): number {
    if (allBids.length === 1) return 100;

    const prices = allBids.map(b => b.amount);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    
    if (minPrice === maxPrice) return 100; // All bids same price
    
    // Inverse scoring: lower price gets higher score
    // Score ranges from 0 to 100
    const normalizedScore = ((maxPrice - bid.amount) / (maxPrice - minPrice)) * 100;
    return Math.max(0, Math.min(100, normalizedScore));
  }

  /**
   * Calculate coverage score based on coverage details quality
   * More comprehensive coverage details = higher score
   */
  private calculateCoverageScore(bid: BidWithBroker): number {
    if (!bid.coverageDetails || bid.coverageDetails.trim().length === 0) {
      return 0; // No coverage details provided
    }

    const details = bid.coverageDetails.trim();
    let score = 0;

    // Base score for having coverage details
    score += 20;

    // Length-based scoring (more detailed = better)
    if (details.length > 50) score += 20;
    if (details.length > 150) score += 20;
    if (details.length > 300) score += 20;

    // Quality indicators (keywords that suggest comprehensive coverage)
    const qualityKeywords = [
      "cobertura completa", "todo riesgo", "responsabilidad civil",
      "daños propios", "robo", "incendio", "cristales", "asistencia",
      "defensa jurídica", "conductor", "ocupantes", "franquicia",
      "prima", "descuento", "bonus", "experiencia", "siniestros"
    ];

    const foundKeywords = qualityKeywords.filter(keyword => 
      details.toLowerCase().includes(keyword.toLowerCase())
    );

    // Add points for quality keywords (max 20 points)
    score += Math.min(20, foundKeywords.length * 2);

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Get display name for broker
   */
  private getBrokerDisplayName(broker: BidWithBroker["broker"]): string {
    if (broker.companyName) {
      return broker.companyName;
    }
    
    if (broker.user.displayName) {
      return broker.user.displayName;
    }
    
    const fullName = `${broker.user.firstName} ${broker.user.lastName}`.trim();
    return fullName || broker.user.email;
  }

  /**
   * Validate bid data before processing
   */
  validateBids(bids: BidWithBroker[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!Array.isArray(bids)) {
      errors.push("Bids must be an array");
      return { valid: false, errors };
    }

    bids.forEach((bid, index) => {
      if (!bid.id) errors.push(`Bid ${index}: Missing bid ID`);
      if (!bid.brokerId) errors.push(`Bid ${index}: Missing broker ID`);
      if (typeof bid.amount !== "number" || bid.amount <= 0) {
        errors.push(`Bid ${index}: Invalid bid amount`);
      }
      if (!bid.broker?.user?.email) {
        errors.push(`Bid ${index}: Missing broker contact information`);
      }
    });

    return { valid: errors.length === 0, errors };
  }
}
