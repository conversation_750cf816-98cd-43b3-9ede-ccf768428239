import { PolicyStateEnum, PolicyState } from "@prisma/client";
import { db } from "../db";

// Valid state transitions for policy state machine
const VALID_POLICY_TRANSITIONS: Record<PolicyStateEnum, PolicyStateEnum[]> = {
  [PolicyStateEnum.DRAFT]: [PolicyStateEnum.ACTIVE, PolicyStateEnum.REJECTED],
  [PolicyStateEnum.ACTIVE]: [PolicyStateEnum.RENEW_SOON, PolicyStateEnum.EXPIRED],
  [PolicyStateEnum.RENEW_SOON]: [PolicyStateEnum.EXPIRED, PolicyStateEnum.ACTIVE],
  [PolicyStateEnum.EXPIRED]: [], // Terminal state
  [PolicyStateEnum.REJECTED]: [], // Terminal state
};

export class PolicyStateService {
  /**
   * Create initial state for a new policy
   */
  static async createState(policyId: string, state: PolicyStateEnum): Promise<PolicyState> {
    return await db.policyState.create({
      data: {
        policyId,
        state,
      },
    });
  }

  /**
   * Get the current state of a policy
   */
  static async getCurrentState(policyId: string): Promise<PolicyStateEnum> {
    const latestState = await db.policyState.findFirst({
      where: { policyId },
      orderBy: { createdAt: 'desc' },
      select: { state: true },
    });

    if (!latestState) {
      throw new Error(`No state found for policy ${policyId}`);
    }

    return latestState.state;
  }

  /**
   * Get complete state history for a policy
   */
  static async getStateHistory(policyId: string): Promise<PolicyState[]> {
    return await db.policyState.findMany({
      where: { policyId },
      orderBy: { createdAt: 'asc' },
    });
  }

  /**
   * Transition policy to a new state with validation
   */
  static async transitionToState(
    policyId: string, 
    newState: PolicyStateEnum
  ): Promise<PolicyState> {
    // Get current state for validation
    const currentState = await this.getCurrentState(policyId);

    // Validate transition
    const validTransitions = VALID_POLICY_TRANSITIONS[currentState];
    if (!validTransitions.includes(newState)) {
      throw new Error(
        `Invalid state transition from ${currentState} to ${newState} for policy ${policyId}`
      );
    }

    // Create new state entry
    return await this.createState(policyId, newState);
  }

  /**
   * Check if a transition is valid
   */
  static isValidTransition(currentState: PolicyStateEnum, newState: PolicyStateEnum): boolean {
    return VALID_POLICY_TRANSITIONS[currentState]?.includes(newState) ?? false;
  }

  /**
   * Get policies by current state
   */
  static async getPoliciesByCurrentState(state: PolicyStateEnum): Promise<string[]> {
    // Get latest state for each policy
    const latestStates = await db.policyState.findMany({
      select: {
        policyId: true,
        state: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Group by policy ID and get the latest state for each
    const policyStateMap = new Map<string, PolicyStateEnum>();
    
    for (const stateRecord of latestStates) {
      if (!policyStateMap.has(stateRecord.policyId)) {
        policyStateMap.set(stateRecord.policyId, stateRecord.state);
      }
    }

    // Filter policies with the specified state
    return Array.from(policyStateMap.entries())
      .filter(([_, currentState]) => currentState === state)
      .map(([policyId, _]) => policyId);
  }

  /**
   * Get time spent in each state for a policy
   */
  static async getStateTimings(policyId: string): Promise<Array<{
    state: PolicyStateEnum;
    startTime: Date;
    endTime: Date | null;
    durationMs: number | null;
  }>> {
    const stateHistory = await this.getStateHistory(policyId);
    
    return stateHistory.map((state, index) => {
      const nextState = stateHistory[index + 1];
      const endTime = nextState?.createdAt ?? null;
      const durationMs = endTime 
        ? endTime.getTime() - state.createdAt.getTime()
        : null;

      return {
        state: state.state,
        startTime: state.createdAt,
        endTime,
        durationMs,
      };
    });
  }

  /**
   * Get policies approaching renewal (ACTIVE -> RENEW_SOON transition candidates)
   */
  static async getPoliciesApproachingRenewal(daysBeforeExpiration: number = 30): Promise<string[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() + daysBeforeExpiration);

    // Find active policies with end dates approaching
    const activePolicies = await db.policy.findMany({
      where: {
        endDate: {
          lte: cutoffDate,
        },
      },
      select: {
        id: true,
      },
    });

    // Filter to only include policies currently in ACTIVE state
    const activePolicyIds = activePolicies.map(p => p.id);
    const currentActivePolicies = [];

    for (const policyId of activePolicyIds) {
      try {
        const currentState = await this.getCurrentState(policyId);
        if (currentState === PolicyStateEnum.ACTIVE) {
          currentActivePolicies.push(policyId);
        }
      } catch {
        // Skip policies without state history
        continue;
      }
    }

    return currentActivePolicies;
  }
}