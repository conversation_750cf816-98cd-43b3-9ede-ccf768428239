import { AuctionStateEnum, AuctionState } from "@prisma/client";
import { db } from "../db";

// Valid state transitions for auction state machine
const VALID_AUCTION_TRANSITIONS: Record<AuctionStateEnum, AuctionStateEnum[]> = {
  [AuctionStateEnum.OPEN]: [AuctionStateEnum.CLOSED, AuctionStateEnum.CANCELED],
  [AuctionStateEnum.CLOSED]: [AuctionStateEnum.SIGNED_POLICY, AuctionStateEnum.EXPIRED],
  [AuctionStateEnum.SIGNED_POLICY]: [], // Terminal state
  [AuctionStateEnum.CANCELED]: [], // Terminal state
  [AuctionStateEnum.EXPIRED]: [], // Terminal state
};

export class AuctionStateService {
  /**
   * Create initial state for a new auction
   */
  static async createState(auctionId: string, state: AuctionStateEnum): Promise<AuctionState> {
    return await db.auctionState.create({
      data: {
        auctionId,
        state,
      },
    });
  }

  /**
   * Get the current state of an auction
   */
  static async getCurrentState(auctionId: string): Promise<AuctionStateEnum> {
    const latestState = await db.auctionState.findFirst({
      where: { auctionId },
      orderBy: { createdAt: 'desc' },
      select: { state: true },
    });

    if (!latestState) {
      throw new Error(`No state found for auction ${auctionId}`);
    }

    return latestState.state;
  }

  /**
   * Get complete state history for an auction
   */
  static async getStateHistory(auctionId: string): Promise<AuctionState[]> {
    return await db.auctionState.findMany({
      where: { auctionId },
      orderBy: { createdAt: 'asc' },
    });
  }

  /**
   * Transition auction to a new state with validation
   */
  static async transitionToState(
    auctionId: string, 
    newState: AuctionStateEnum
  ): Promise<AuctionState> {
    // Get current state for validation
    const currentState = await this.getCurrentState(auctionId);

    // Validate transition
    const validTransitions = VALID_AUCTION_TRANSITIONS[currentState];
    if (!validTransitions.includes(newState)) {
      throw new Error(
        `Invalid state transition from ${currentState} to ${newState} for auction ${auctionId}`
      );
    }

    // Create new state entry
    return await this.createState(auctionId, newState);
  }

  /**
   * Check if a transition is valid
   */
  static isValidTransition(currentState: AuctionStateEnum, newState: AuctionStateEnum): boolean {
    return VALID_AUCTION_TRANSITIONS[currentState]?.includes(newState) ?? false;
  }

  /**
   * Get auctions by current state
   */
  static async getAuctionsByCurrentState(state: AuctionStateEnum): Promise<string[]> {
    // Get latest state for each auction
    const latestStates = await db.auctionState.findMany({
      select: {
        auctionId: true,
        state: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Group by auction ID and get the latest state for each
    const auctionStateMap = new Map<string, AuctionStateEnum>();
    
    for (const stateRecord of latestStates) {
      if (!auctionStateMap.has(stateRecord.auctionId)) {
        auctionStateMap.set(stateRecord.auctionId, stateRecord.state);
      }
    }

    // Filter auctions with the specified state
    return Array.from(auctionStateMap.entries())
      .filter(([_, currentState]) => currentState === state)
      .map(([auctionId, _]) => auctionId);
  }

  /**
   * Get time spent in each state for an auction
   */
  static async getStateTimings(auctionId: string): Promise<Array<{
    state: AuctionStateEnum;
    startTime: Date;
    endTime: Date | null;
    durationMs: number | null;
  }>> {
    const stateHistory = await this.getStateHistory(auctionId);
    
    return stateHistory.map((state, index) => {
      const nextState = stateHistory[index + 1];
      const endTime = nextState?.createdAt ?? null;
      const durationMs = endTime 
        ? endTime.getTime() - state.createdAt.getTime()
        : null;

      return {
        state: state.state,
        startTime: state.createdAt,
        endTime,
        durationMs,
      };
    });
  }
}