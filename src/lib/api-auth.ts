import { createClient } from "@/lib/supabase/server";
import { NextRequest } from "next/server";
import { User } from "@supabase/supabase-js";
import { db } from "@/lib/db";
import { Role } from "@prisma/client";

/**
 * Standardized authentication utility for API routes
 * Provides consistent user authentication and role validation
 */
export class ApiAuthError extends Error {
  constructor(
    message: string,
    public statusCode: number = 401,
    public code?: string
  ) {
    super(message);
    this.name = "ApiAuthError";
  }
}

/**
 * Get current authenticated user from request
 * Throws ApiAuthError if user is not authenticated
 */
export async function getCurrentUser(_request?: NextRequest): Promise<User> {
  const supabase = await createClient();
  
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    throw new ApiAuthError("No autorizado", 401, "UNAUTHORIZED");
  }
  
  return user;
}

/**
 * Validate user has required role
 * Throws ApiAuthError if user doesn't have the required role
 */
export async function requireRole(
  user: User,
  requiredRole: string | Role
): Promise<void> {
  // Get user role from our database using Prisma
  const dbUser = await db.user.findUnique({
    where: { id: user.id },
    select: { role: true }
  });
  
  if (!dbUser || dbUser.role !== requiredRole) {
    throw new ApiAuthError(
      "Acceso denegado",
      403,
      "INSUFFICIENT_PERMISSIONS"
    );
  }
}

/**
 * Check if user has any of the specified roles
 */
export async function hasAnyRole(
  user: User,
  roles: (string | Role)[]
): Promise<boolean> {
  const dbUser = await db.user.findUnique({
    where: { id: user.id },
    select: { role: true }
  });
  
  return dbUser ? roles.includes(dbUser.role) : false;
}

/**
 * Get user role
 */
export async function getUserRole(user: User): Promise<string | null> {
  const dbUser = await db.user.findUnique({
    where: { id: user.id },
    select: { role: true }
  });
  
  return dbUser?.role ?? null;
}

/**
 * Middleware function to authenticate and optionally validate role
 */
export async function authenticateRequest(
  request?: NextRequest,
  requiredRole?: string
): Promise<{ user: User; role: string | null }> {
  const user = await getCurrentUser(request);
  const role = await getUserRole(user);
  
  if (requiredRole) {
    await requireRole(user, requiredRole);
  }
  
  return { user, role };
}

/**
 * Type guard for API auth errors
 */
export function isApiAuthError(error: unknown): error is ApiAuthError {
  return error instanceof ApiAuthError;
}