import { db } from "@/lib/db";

/**
 * Phone validation utilities for handling unique constraint validation
 * across the application in a DRY way.
 */

export interface PhoneValidationError {
  isPhoneUniqueConstraint: boolean;
  userFriendlyMessage: string;
}

/**
 * Checks if an error is related to phone number unique constraint violation
 * and returns appropriate user-friendly error message.
 * 
 * Handles both Prisma P2002 errors and generic error messages.
 */
export function handlePhoneValidationError(error: unknown): PhoneValidationError {
  // Handle Prisma P2002 errors (from API routes)
  if (
    error && 
    typeof error === "object" && 
    "code" in error && 
    error.code === "P2002" &&
    "meta" in error && 
    error.meta && 
    typeof error.meta === "object" && 
    "target" in error.meta && 
    Array.isArray(error.meta.target) && 
    error.meta.target.includes("phone")
  ) {
    return {
      isPhoneUniqueConstraint: true,
      userFriendlyMessage: "Este número de teléfono ya está en uso por otro usuario."
    };
  }

  // Handle generic error messages (from server actions)
  if (
    error instanceof Error && 
    error.message.includes("Unique constraint failed on the fields: (`phone`)")
  ) {
    return {
      isPhoneUniqueConstraint: true,
      userFriendlyMessage: "Este número de teléfono ya está registrado. Por favor, use un número diferente."
    };
  }

  return {
    isPhoneUniqueConstraint: false,
    userFriendlyMessage: ""
  };
}

/**
 * Checks if a phone number is already in use by another user.
 * Useful for preemptive validation before attempting database operations.
 * 
 * @param phone - The phone number to check
 * @param excludeUserId - User ID to exclude from the check (for profile updates)
 * @returns true if phone is already in use, false otherwise
 */
export async function isPhoneAlreadyInUse(phone: string, excludeUserId?: string): Promise<boolean> {
  if (!phone || phone.trim().length === 0) {
    return false;
  }

  const whereClause: { phone: string; id?: { not: string } } = {
    phone: phone.trim()
  };

  if (excludeUserId) {
    whereClause.id = { not: excludeUserId };
  }

  const existingUser = await db.user.findFirst({
    where: whereClause,
    select: { id: true }
  });

  return !!existingUser;
}

/**
 * Standard phone validation error message for consistent user experience.
 */
export const PHONE_ALREADY_IN_USE_MESSAGE = "Este número de teléfono ya está en uso por otro usuario.";