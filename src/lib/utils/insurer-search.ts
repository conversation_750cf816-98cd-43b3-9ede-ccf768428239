import { InsurerCompany } from "@prisma/client";
import { normalizeForSearch } from "@/lib/text-utils";

/**
 * Insurer Company Search Utilities
 * 
 * Centralizes search logic for insurer companies using Prisma enums
 * Eliminates 72+ lines of hardcoded enum duplication across routes
 */

/**
 * Get all insurer companies as searchable array
 */
export function getAllInsurerCompanies(): InsurerCompany[] {
  return Object.values(InsurerCompany);
}

/**
 * Filter insurer companies by search term with accent normalization
 */
export function filterInsurerCompaniesBySearch(search: string): InsurerCompany[] {
  const normalizedSearch = normalizeForSearch(search);
  
  return getAllInsurerCompanies().filter(company => {
    // Handle special case for "OTHER" -> "OTRAS" display
    const displayName = company === InsurerCompany.OTHER ? "OTRAS" : company;
    return normalizeForSearch(displayName).includes(normalizedSearch);
  });
}

/**
 * Create Prisma filter condition for insurer company search
 * Replaces the massive hardcoded objects in routes
 */
export function createInsurerCompanySearchFilter(search: string) {
  const matchingCompanies = filterInsurerCompaniesBySearch(search);
  
  return {
    insurerCompany: {
      in: matchingCompanies
    }
  };
}

/**
 * Create nested Prisma filter for policy-related insurer search
 * Used in auction routes where insurerCompany is through policy relation
 */
export function createNestedInsurerCompanySearchFilter(search: string) {
  const matchingCompanies = filterInsurerCompaniesBySearch(search);
  
  return {
    policy: {
      insurerCompany: {
        in: matchingCompanies
      }
    }
  };
}

/**
 * Type-safe insurer company validation
 */
export function isValidInsurerCompany(value: string): value is InsurerCompany {
  return Object.values(InsurerCompany).includes(value as InsurerCompany);
}

/**
 * Get display name for insurer company
 * Handles the OTHER -> OTRAS conversion
 */
export function getInsurerCompanyDisplayName(company: InsurerCompany): string {
  return company === InsurerCompany.OTHER ? "OTRAS" : company;
}