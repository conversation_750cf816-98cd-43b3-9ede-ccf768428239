import { type NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { ApiResponses } from "@/lib/api-responses";
import { Role } from "@prisma/client";

const PUBLIC_PATHS = ["/login", "/signup", "/auth/confirm", "/error", "/unauthorized", "/complete-profile", "/forgot-password", "/reset-password"];

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    // For API routes, return JSON error instead of redirect
    if (pathname.startsWith("/api/")) {
      return ApiResponses.unauthorized("Authentication required", "UNAUTHORIZED");
    }
    
    if (pathname === "/") {
      return NextResponse.redirect(new URL("/login", req.url));
    }
    if (PUBLIC_PATHS.includes(pathname)) {
      return NextResponse.next();
    }
    return NextResponse.redirect(new URL("/login", req.url));
  }

  // --- User is Authenticated ---
  const userRole = user.user_metadata?.role;
  const isProfileComplete = user.user_metadata?.phone_number_is_set;

  // Handle incomplete profile for account holders
  // If metadata is missing (e.g., during OAuth flow), let the entry page handle it
  if (userRole === Role.ACCOUNT_HOLDER && isProfileComplete === false) {
    if (pathname !== "/complete-profile") {
      return NextResponse.redirect(new URL("/complete-profile", req.url));
    }
    // Allow access to complete-profile page
    return NextResponse.next();
  }

  // If the user is authenticated, redirect them from any public path to a safe entry point based on their role.
  // Exception: don't redirect from complete-profile as it's handled above
  if ((PUBLIC_PATHS.includes(pathname) || pathname === "/") && pathname !== "/complete-profile") {
    if (userRole === Role.BROKER) {
      return NextResponse.redirect(new URL("/broker", req.url));
    } else if (userRole === Role.ADMIN) {
      return NextResponse.redirect(new URL("/admin", req.url));
    } else {
      return NextResponse.redirect(new URL("/account-holder", req.url));
    }
  }

  // Role-based access control for protected routes
  if (pathname.startsWith("/broker") && userRole !== Role.BROKER) {
    if (pathname.startsWith("/api/")) {
      return ApiResponses.forbidden("Insufficient permissions", "INSUFFICIENT_PERMISSIONS");
    }
    return NextResponse.redirect(new URL("/unauthorized", req.url));
  }

  if (pathname.startsWith("/admin") && userRole !== Role.ADMIN) {
    if (pathname.startsWith("/api/")) {
      return ApiResponses.forbidden("Insufficient permissions", "INSUFFICIENT_PERMISSIONS");
    }
    return NextResponse.redirect(new URL("/unauthorized", req.url));
  }

  if (pathname.startsWith("/account-holder") && userRole !== Role.ACCOUNT_HOLDER) {
    if (pathname.startsWith("/api/")) {
      return ApiResponses.forbidden("Insufficient permissions", "INSUFFICIENT_PERMISSIONS");
    }
    return NextResponse.redirect(new URL("/unauthorized", req.url));
  }

  // Allow all other requests to proceed. The page-level logic can handle
  // role-based access if needed, but the main routing is done.
  return NextResponse.next();
}

export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};