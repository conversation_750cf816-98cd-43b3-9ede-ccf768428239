@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    /* Brand Colors - Following Architecture Document */
    --brand-aquamarine-green: #6BE1A6;
    --brand-white: #FFFFFF;
    --brand-black: #000000;

    /* Primary uses brand aquamarine green */
    --primary: 155 60% 65%; /* #6BE1A6 in HSL */
    --primary-foreground: 0 0% 0%;

    /* Secondary uses brand emerald green */
    --secondary: 130 49% 44%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 130 49% 44%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --warning: 43 96% 56%;
    --warning-foreground: 0 0% 0%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 145 75% 58%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%; /* Light gray for hover */
    --sidebar-accent-foreground: 240 5.3% 26.1%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --kanban-board-circle-blue: oklch(0.6232 0.1879 259.80);
    --kanban-board-circle-cyan: oklch(0.7146 0.1256 215.13);
    --kanban-board-circle-gray: oklch(0.5513 0.0233 264.36);
    --kanban-board-circle-green: oklch(0.7229 0.1921 149.58);
    --kanban-board-circle-indigo: oklch(0.5860 0.2037 277.12);
    --kanban-board-circle-pink: oklch(0.6559 0.2117 354.34);
    --kanban-board-circle-purple: oklch(0.6267 0.2325 303.86);
    --kanban-board-circle-red: oklch(0.6368 0.2078 25.33);
    --kanban-board-circle-violet: oklch(0.6059 0.2187 292.72);
    --kanban-board-circle-yellow: oklch(0.7959 0.1618 86.05);
    --color-kanban-board-circle-blue: var(--kanban-board-circle-blue);
    --color-kanban-board-circle-cyan: var(--kanban-board-circle-cyan);
    --color-kanban-board-circle-gray: var(--kanban-board-circle-gray);
    --color-kanban-board-circle-green: var(--kanban-board-circle-green);
    --color-kanban-board-circle-indigo: var(--kanban-board-circle-indigo);
    --color-kanban-board-circle-pink: var(--kanban-board-circle-pink);
    --color-kanban-board-circle-primary: var(--kanban-board-circle-primary);
    --color-kanban-board-circle-purple: var(--kanban-board-circle-purple);
    --color-kanban-board-circle-red: var(--kanban-board-circle-red);
    --color-kanban-board-circle-violet: var(--kanban-board-circle-violet);
    --color-kanban-board-circle-yellow: var(--kanban-board-circle-yellow);
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 155 60% 65%; /* #6BE1A6 in HSL */
    --primary-foreground: 0 0% 0%;

    --secondary: 130 49% 44%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 130 49% 44%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --warning: 43 96% 56%;
    --warning-foreground: 0 0% 0%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 145 75% 58%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%; /* Dark gray for hover */
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --kanban-board-circle-blue: oklch(0.7135 0.1435 254.61);
    --kanban-board-circle-cyan: oklch(0.7973 0.1339 211.45);
    --kanban-board-circle-gray: oklch(0.7137 0.0192 261.33);
    --kanban-board-circle-green: oklch(0.8003 0.1823 151.70);
    --kanban-board-circle-indigo: oklch(0.6797 0.1586 276.96);
    --kanban-board-circle-pink: oklch(0.7253 0.1752 349.74);
    --kanban-board-circle-purple: oklch(0.7218 0.1766 305.51);
    --kanban-board-circle-red: oklch(0.7108 0.1660 22.21);
    --kanban-board-circle-violet: oklch(0.7093 0.1589 293.52);
    --kanban-board-circle-yellow: oklch(0.8601 0.1730 91.84);
  }
  .theme {
    --kanban-board-circle-primary: var(--primary);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-50 text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
  }

  h1 {
    @apply text-4xl;
  }

  h2 {
    @apply text-3xl;
  }

  h3 {
    @apply text-2xl;
  }

  h4 {
    @apply text-xl;
  }
}

@layer components {
  .policy-card {
    @apply bg-card rounded-lg border border-border p-6 shadow-sm hover:shadow-lg transition-all duration-200 hover:scale-[1.02];
  }

  .form-container {
    @apply space-y-6 max-w-2xl mx-auto bg-card p-8 rounded-lg shadow-sm border border-border backdrop-blur-sm;
  }

  .onboarding-container {
    @apply max-w-4xl mx-auto bg-card p-8 rounded-lg shadow-sm border border-border backdrop-blur-sm;
  }

  .upload-area {
    @apply border-2 border-dashed border-border rounded-lg p-8 text-center cursor-pointer transition-all duration-200 hover:border-primary/50 hover:bg-muted hover:scale-[1.02];
  }

  .step-indicator {
    @apply flex items-center justify-center space-x-2 mb-8;
  }

  .step-dot {
    @apply w-3 h-3 rounded-full bg-muted;
  }

  .step-dot.active {
    @apply bg-primary;
  }

  .step-dot.completed {
    @apply bg-primary;
  }

  /* Brand Button Styles - Following DRY Principles */
  .btn-brand-primary {
    @apply bg-brand-aquamarine-green text-black font-medium;
    &:hover {
      background-color: var(--brand-aquamarine-green);
      opacity: 0.9;
    }
  }
  
  .btn-brand-secondary {
    @apply bg-brand-aquamarine-green text-black font-medium;
    &:hover {
      background-color: var(--brand-aquamarine-green);
      opacity: 0.9;
    }
  }
  
  .btn-brand-outline {
    @apply border border-brand-aquamarine-green text-brand-aquamarine-green;
    &:hover {
      @apply bg-brand-aquamarine-green text-white;
    }
  }
  
  .text-brand-primary {
    @apply text-brand-aquamarine-green;
  }
  
  .text-brand-secondary {
    @apply text-brand-aquamarine-green;
  }
  
  .bg-brand-primary {
    @apply bg-brand-aquamarine-green;
  }
  
  .bg-brand-secondary {
    @apply bg-brand-aquamarine-green;
  }

  /* Policy Selection Card Components - Following DRY Principle */
  .policy-selection-card {
    @apply cursor-pointer w-1/3 border-gray-400 text-center transition-all duration-200 hover:bg-primary/10;
  }

  .policy-selection-card-car {
    @apply policy-selection-card;
  }

  .policy-selection-card-motorcycle {
    @apply policy-selection-card;
  }

}

@layer base {
  body {
    @apply bg-background text-foreground;
  }
}