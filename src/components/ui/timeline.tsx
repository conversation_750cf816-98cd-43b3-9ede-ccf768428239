
'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Check, Circle } from 'lucide-react';

interface TimelineProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

const Timeline = React.forwardRef<HTMLDivElement, TimelineProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('relative', className)}
      {...props}
    >
      {children}
    </div>
  ),
);
Timeline.displayName = 'Timeline';

interface TimelineItemProps extends React.HTMLAttributes<HTMLDivElement> {
  status?: 'completed' | 'pending';
  title: string;
  time: string;
  isLast?: boolean;
}

const TimelineItem = React.forwardRef<HTMLDivElement, TimelineItemProps>(
  ({ className, status = 'pending', title, time, isLast = false, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('relative flex items-start gap-3 pb-6', className)}
      {...props}
    >
      {/* Icon */}
      <div className="relative flex-shrink-0">
        <div
          className={cn(
            'flex h-6 w-6 items-center justify-center rounded-full',
            status === 'completed'
              ? 'bg-primary text-primary-foreground'
              : 'bg-gray-300 text-gray-600'
          )}
        >
          {status === 'completed' ? (
            <Check className="h-3 w-3" />
          ) : (
            <Circle className="h-3 w-3 fill-current" />
          )}
        </div>
        
        {/* Connector line */}
        {!isLast && (
          <div className="absolute left-3 top-6 h-6 w-0.5 bg-gray-200" />
        )}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900">{title}</p>
        <p className="text-xs text-gray-500 mt-1">{time}</p>
      </div>
    </div>
  ),
);
TimelineItem.displayName = 'TimelineItem';

export { Timeline, TimelineItem };
