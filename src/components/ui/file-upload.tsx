"use client";

import { useState, useRef, useCallback } from "react";
import { AlertCircle, FileText, Upload, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";

export interface FileUploadProps {
  onFileSelect?: (file: File) => void;
  onFileRemove?: () => void;
  acceptedFileTypes?: string[];
  maxFileSize?: number; // in bytes
  disabled?: boolean;
  className?: string;
  // Customizable text props (Spanish by default)
  title?: string;
  description?: string;
  buttonText?: string;
  acceptText?: string;
  dragText?: string;
  // File processing state
  isProcessing?: boolean;
  processingText?: string;
  // Error handling
  error?: string | null;
  onErrorClear?: () => void;
  // Size customization
  compact?: boolean;
}

export function FileUpload({
  onFileSelect,
  onFileRemove,
  acceptedFileTypes = ["application/pdf", "image/jpeg", "image/png"],
  maxFileSize = 10 * 1024 * 1024, // 10MB default
  disabled = false,
  className,
  title = "Arrastra tu PDF o imagen, o haz clic para seleccionar un archivo",
  description = "Aceptamos PDF, JPG, PNG. Máx 10 MB",
  buttonText = "Seleccionar archivo",
  acceptText = ".pdf,.jpg,.jpeg,.png",
  dragText = "Arrastra tu PDF o imagen, o haz clic para seleccionar un archivo",
  isProcessing = false,
  processingText = "Procesando archivo...",
  error,
  onErrorClear,
  compact = false,
}: FileUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const getFileTypeError = useCallback(() => {
    const types = acceptedFileTypes
      .map((type) => {
        switch (type) {
          case "application/pdf":
            return "PDF";
          case "image/jpeg":
          case "image/jpg":
            return "JPG";
          case "image/png":
            return "PNG";
          default:
            return type;
        }
      })
      .join(", ");
    return `Tipo de archivo inválido. Por favor sube un ${types}.`;
  }, [acceptedFileTypes]);

  const getSizeError = useCallback(() => {
    const sizeMB = Math.round(maxFileSize / (1024 * 1024));
    return `El tamaño del archivo excede el límite de ${sizeMB}MB.`;
  }, [maxFileSize]);

  const validateFile = useCallback(
    (file: File): string | null => {
      if (!acceptedFileTypes.includes(file.type)) {
        return getFileTypeError();
      }

      if (file.size > maxFileSize) {
        return getSizeError();
      }

      return null;
    },
    [acceptedFileTypes, maxFileSize, getFileTypeError, getSizeError]
  );

  const handleFileSelect = useCallback(
    (file: File) => {
      const validationError = validateFile(file);

      if (validationError) {
        toast({
          variant: "destructive",
          title: "Error de archivo",
          description: validationError,
        });
        return;
      }

      setSelectedFile(file);
      onFileSelect?.(file);
      onErrorClear?.();
    },
    [validateFile, onFileSelect, onErrorClear, toast]
  );

  const handleFileRemove = useCallback(() => {
    setSelectedFile(null);
    onFileRemove?.();
    onErrorClear?.();
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, [onFileRemove, onErrorClear]);

  const handleDragOver = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      if (!disabled) {
        setIsDragging(true);
      }
    },
    [disabled]
  );

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      setIsDragging(false);

      if (disabled) return;

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        const file = e.dataTransfer.files[0];
        if (file) {
          handleFileSelect(file);
        }
      }
    },
    [disabled, handleFileSelect]
  );

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files.length > 0) {
        const file = e.target.files[0];
        if (file) {
          handleFileSelect(file);
        }
      }
    },
    [handleFileSelect]
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === "Enter" && !disabled) {
        fileInputRef.current?.click();
      }
    },
    [disabled]
  );

  const handleClick = useCallback(() => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  }, [disabled]);

  if (selectedFile) {
    return (
      <div className={cn("rounded-lg border p-6 space-y-4", className)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="rounded-full bg-accent/10 p-2">
              <FileText className="h-5 w-5 text-accent" />
            </div>
            <div>
              <p className="font-medium">{selectedFile.name}</p>
              <p className="text-sm text-muted-foreground">
                {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleFileRemove}
            disabled={disabled || isProcessing}
            title="Eliminar archivo"
            className="hover:bg-primary hover:border-primary hover:text-primary-foreground"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {error && (
          <div className="flex items-start text-destructive space-x-2">
            <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
            <span className="text-sm">{error}</span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center border-2 rounded-lg transition-all cursor-pointer",
        compact ? "p-4" : "p-8",
        isDragging
          ? "border-accent bg-accent/5 border-dotted"
          : "border-gray-300 border-dotted hover:border-gray-400",
        disabled && "cursor-not-allowed opacity-50",
        className
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={disabled ? -1 : 0}
      role="button"
      aria-label={dragText}
    >
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileInputChange}
        className="sr-only"
        accept={acceptText}
        disabled={disabled}
      />

      <div className={cn("flex flex-col items-center justify-center", compact ? "space-y-2" : "space-y-4")}>
        <div className={cn("rounded-full bg-accent/10", compact ? "p-2" : "p-3")}>
          <Upload className={cn(compact ? "h-5 w-5" : "h-6 w-6", "text-brand-aquamarine-green")} />
        </div>
        <div className="space-y-1 text-center">
          <h3 className={cn(compact ? "text-base" : "text-lg", "font-medium")}>{title}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
        <Button
          type="button"
          variant="outline"
          disabled={disabled}
          className={cn(compact ? "mt-2" : "mt-4", "hover:bg-primary hover:border-primary hover:text-primary-foreground")}
        >
          {buttonText}
        </Button>
      </div>

      {error && (
        <div className="mt-4 flex items-start text-destructive space-x-2">
          <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
          <span className="text-sm">{error}</span>
        </div>
      )}
    </div>
  );
}
