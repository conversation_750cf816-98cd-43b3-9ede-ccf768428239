"use client";

import { useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils";
import { CheckCircle, PiggyBank } from "lucide-react";
import { AuctionBid } from "@/features/auctions/types/auction";
import confetti from "canvas-confetti";

interface CompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedBid: AuctionBid | null;
  currentPremium: number;
  nextRenewalDate?: string;
  auctionId: string;
  onComplete?: () => void;
}

export function CompletionModal({
  isOpen,
  onClose,
  selectedBid,
  currentPremium,
  onComplete,
}: CompletionModalProps) {

  // Trigger confetti animation when modal opens
  useEffect(() => {
    if (isOpen) {
      const duration = 3000;
      const animationEnd = Date.now() + duration;
      const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 9999 };

      function randomInRange(min: number, max: number) {
        return Math.random() * (max - min) + min;
      }

      const interval: NodeJS.Timeout = setInterval(function() {
        const timeLeft = animationEnd - Date.now();

        if (timeLeft <= 0) {
          return clearInterval(interval);
        }

        const particleCount = 50 * (timeLeft / duration);

        // Launch confetti from two sides
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
        });
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
        });
      }, 250);

      return () => clearInterval(interval);
    }
  }, [isOpen]);

  if (!selectedBid) return null;

  const savings = currentPremium - selectedBid.annualPremium;
  const savingsPercentage = ((savings / currentPremium) * 100).toFixed(1);

  const handleClose = () => {
    if (onComplete) {
      onComplete();
    }
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center gap-3 space-y-0 pb-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-brand-aquamarine-green/20">
            <CheckCircle className="h-6 w-6 text-black" />
          </div>
          <div>
            <DialogTitle className="text-xl font-semibold">
              ¡Proceso Completado Exitosamente!
            </DialogTitle>
            <p className="text-sm text-muted-foreground">
              Su nueva póliza ha sido procesada correctamente
            </p>
          </div>
        </DialogHeader>

        <div className="space-y-6">

          {/* Savings Summary */}
          {savings > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PiggyBank className="h-5 w-5 text-brand-aquamarine-green" />
                  Resumen de Ahorros
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <p className="text-sm text-muted-foreground">Prima Anterior</p>
                    <p className="text-xl font-bold text-red-600">
                      {formatCurrency(currentPremium)}
                    </p>
                  </div>
                  <div className="text-center p-4 bg-brand-aquamarine-green/10 rounded-lg">
                    <p className="text-sm text-muted-foreground">Nueva Prima</p>
                    <p className="text-xl font-bold text-brand-aquamarine-green">
                      {formatCurrency(selectedBid.annualPremium)}
                    </p>
                  </div>
                </div>
                
                <div className="text-center p-4 bg-green-50 rounded-lg border-2 border-brand-aquamarine-green">
                  <p className="text-sm text-muted-foreground">Ahorro Total</p>
                  <p className="text-3xl font-bold text-brand-aquamarine-green">
                    {formatCurrency(savings)}
                  </p>
                  <Badge variant="secondary" className="bg-brand-aquamarine-green text-black mt-2">
                    {savingsPercentage}% de descuento
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Button */}
          <div className="flex gap-3 pt-4">
            <Button
              onClick={handleClose}
              className="flex-1 btn-brand-primary"
            >
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                ¡Enhorabuena!
              </div>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}