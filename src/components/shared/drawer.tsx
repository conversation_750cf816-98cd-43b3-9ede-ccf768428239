"use client";

import * as React from "react";
import { createPortal } from "react-dom";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

interface DrawerProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
}

export function Drawer({ isOpen, onClose, children, className }: DrawerProps) {
  const [mounted, setMounted] = React.useState(false);

  // Handle escape key
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll when drawer is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  // Set mounted state after component mounts
  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!isOpen || !mounted) return null;

  const drawerContent = (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-[9998]"
        onClick={onClose}
        style={{ 
          position: "fixed",
          top: 0, 
          left: 0, 
          right: 0, 
          bottom: 0,
          width: "100vw",
          height: "100vh"
        }}
      />
      
      {/* Drawer */}
      <div
        className={cn(
          "fixed right-0 top-0 h-screen bg-white shadow-xl z-[9999]",
          "w-full max-w-2xl min-w-[50vw]", // At least 50% viewport width, max 2xl (672px)
          "transform transition-transform duration-300 ease-in-out",
          "flex flex-col", // Changed from overflow-y-auto to flex layout
          isOpen ? "translate-x-0" : "translate-x-full",
          className
        )}
        style={{
          position: "fixed",
          height: "100vh",
          top: 0,
          right: 0
        }}
      >
        {children}
      </div>
    </>
  );

  // Render the drawer using a portal to document.body
  return createPortal(drawerContent, document.body);
}

interface DrawerHeaderProps {
  children: React.ReactNode;
  onClose: () => void;
  className?: string;
}

export function DrawerHeader({ children, onClose, className }: DrawerHeaderProps) {
  return (
    <div className={cn("flex items-center justify-between p-6 border-b flex-shrink-0", className)}>
      <div className="flex-1">{children}</div>
      <button
        onClick={onClose}
        className="ml-4 p-1 hover:bg-gray-100 rounded-md transition-colors"
      >
        <X className="h-5 w-5 text-gray-500" />
      </button>
    </div>
  );
}

interface DrawerContentProps {
  children: React.ReactNode;
  className?: string;
}

export function DrawerContent({ children, className }: DrawerContentProps) {
  return (
    <div className={cn("p-6 flex-1 overflow-y-auto", className)}>
      {children}
    </div>
  );
}