import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  <PERSON><PERSON>eader,
  CardTitle,
} from "../ui/card";
import { cn } from "../../lib/utils";

interface ZCardProps {
  title?: React.ReactNode;
  description?: React.ReactNode;
  footer?: React.ReactNode;
  variant?: "default" | "metric" | "client" | "auction" | "policy";
  children?: React.ReactNode;
  className?: string;
}

export function ZCard({
  title,
  description,
  footer,
  variant = "default",
  className,
  children,
  ...props
}: ZCardProps & Omit<React.HTMLAttributes<HTMLDivElement>, "title">) {
  return (
    <Card
      className={cn(
        "overflow-hidden",
        variant === "metric" && "bg-white",
        variant === "client" && "transition-all hover:shadow-md",
        variant === "auction" && "transition-all hover:shadow-md",
        variant === "policy" && "border-l-4 border-l-primary",
        className
      )}
      {...props}
    >
      {(title ?? description) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      {children && <CardContent>{children}</CardContent>}
      {footer && <CardFooter>{footer}</CardFooter>}
    </Card>
  );
}