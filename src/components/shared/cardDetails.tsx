import { Activity, Clock, PiggyBank, Shield, Gavel } from "lucide-react";
import { AuctionDetails } from "@/features/auctions/types/auction";
import { AuctionStateEnum } from "@prisma/client";
import { formatCurrency } from "@/lib/utils";
import { cn } from "@/lib/utils";

import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";


interface CardDetailsProps {
    auction: AuctionDetails;
    timeRemaining?: string;
}

export const CardDetails = ({ auction, timeRemaining }: CardDetailsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-2">
      {auction.status === AuctionStateEnum.SIGNED_POLICY ? (
        // SIGNED_POLICY KPIs: Ahorro anual, Nueva Aseguradora, Tu Agente, Estado, Vencimiento
        <>
          {/* Ahorro anual */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Ahorro anual</CardTitle>
              <PiggyBank className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {auction.newPolicyDocument?.extractedPolicy ? (() => {
                  // Calculate savings: original policy premium vs extracted new policy premium
                  const originalPremium = auction.annualPremium;
                  const newPremium = auction.newPolicyDocument.extractedPolicy.premium;
                  const savings = originalPremium - newPremium;
                  return formatCurrency(savings);
                })() : "No disponible"}
              </div>
            </CardContent>
          </Card>

          {/* Nueva aseguradora */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Nueva aseguradora</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-bold">
                {auction.newPolicyDocument?.extractedPolicy?.insurerCompany ?? "No disponible"}
              </div>
            </CardContent>
          </Card>

          {/* Tu agente */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tu agente</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-bold">
                {auction.selectedBid?.brokerName ? (() => {
                  // Extract first name and last initial from selected bid broker
                  const nameParts = auction.selectedBid.brokerName.split(" ");
                  const firstName = nameParts[0] ?? "";
                  const lastName = nameParts[nameParts.length - 1];
                  return lastName ? `${firstName} ${lastName.charAt(0)}.` : firstName;
                })() : "No disponible"}
              </div>
            </CardContent>
          </Card>

          {/* Estado */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Estado</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <Badge variant="default">
                Póliza Firmada
              </Badge>
            </CardContent>
          </Card>

          {/* Vencimiento */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Vencimiento</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-bold">
                {auction.newPolicyDocument?.extractedPolicy?.endDate ? (() => {
                  const endDate = new Date(auction.newPolicyDocument.extractedPolicy.endDate);
                  return endDate.toLocaleDateString("es-ES", {
                    day: "2-digit",
                    month: "2-digit", 
                    year: "numeric"
                  });
                })() : "No disponible"}
              </div>
            </CardContent>
          </Card>
        </>
      ) : (
        // OPEN/CLOSED KPIs: Tu póliza actual, Mejor Oferta, Ahorro Potencial, Estado, Tiempo Restante
        <>
          {/* Tu póliza actual */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tu póliza actual</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(auction.annualPremium)}/año
              </div>
            </CardContent>
          </Card>

          {/* Mejor Oferta */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Mejor Oferta</CardTitle>
              <Gavel className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {auction.bids && auction.bids.length > 0
                  ? `${formatCurrency(Math.min(...auction.bids.map(bid => bid.annualPremium)))}/año`
                  : "Sin ofertas"}
              </div>
            </CardContent>
          </Card>

          {/* Ahorro Potencial */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              {auction.bids && auction.bids.length > 0 ? (() => {
                const bestOffer = Math.min(...auction.bids.map(bid => bid.annualPremium));
                const difference = auction.annualPremium - bestOffer;
                const isSaving = difference > 0;
                
                return (
                  <>
                    <CardTitle className="text-sm font-medium">
                      {isSaving ? "Ahorro Potencial" : "Coste Adicional"}
                    </CardTitle>
                    <PiggyBank className="h-4 w-4 text-muted-foreground" />
                  </>
                );
              })() : (
                <>
                  <CardTitle className="text-sm font-medium">Ahorro Potencial</CardTitle>
                  <PiggyBank className="h-4 w-4 text-muted-foreground" />
                </>
              )}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {auction.bids && auction.bids.length > 0 ? (() => {
                  const bestOffer = Math.min(...auction.bids.map(bid => bid.annualPremium));
                  const difference = auction.annualPremium - bestOffer;
                  const isSaving = difference > 0;
                  
                  return (
                    <span className={isSaving ? "text-primary" : "text-red-600"}>
                      {formatCurrency(Math.abs(difference))}/año
                    </span>
                  );
                })() : "No disponible"}
              </div>
            </CardContent>
          </Card>

          {/* Estado */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Estado</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <Badge
                variant="outline"
                className={cn({
                  "bg-emerald-100 text-emerald-800 border-emerald-200": auction.status === AuctionStateEnum.OPEN,
                  "bg-warning/10 text-warning-foreground border-warning/20": auction.status === AuctionStateEnum.CLOSED,
                })}
              >
                {auction.status === AuctionStateEnum.OPEN 
                  ? "Abierta" 
                  : auction.status === AuctionStateEnum.CLOSED 
                    ? "Cerrada" 
                    : "Cerrada"}
              </Badge>
            </CardContent>
          </Card>

          {/* Tiempo Restante */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tiempo Restante</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {auction.status === AuctionStateEnum.CLOSED ? "Finalizada" : (timeRemaining ?? "Calculando...")}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default CardDetails;
