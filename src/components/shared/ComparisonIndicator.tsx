"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";

interface ComparisonIndicatorProps {
  type: "better" | "worse" | "same" | "missing";
  value?: string;
  className?: string;
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
  showBadge?: boolean;
}

export function ComparisonIndicator({
  type,
  value,
  className,
  size = "md",
  showIcon = true,
  showBadge = true
}: ComparisonIndicatorProps): React.ReactElement {
  const config = {
    better: {
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
      badgeColor: "bg-green-500"
    },
    worse: {
      icon: XCircle,
      color: "text-red-600",
      bgColor: "bg-red-50",
      borderColor: "border-red-200",
      badgeColor: "bg-red-500"
    },
    same: {
      icon: AlertCircle,
      color: "text-gray-600",
      bgColor: "bg-gray-50",
      borderColor: "border-gray-200",
      badgeColor: "bg-gray-500"
    },
    missing: {
      icon: XCircle,
      color: "text-red-600",
      bgColor: "bg-red-50",
      borderColor: "border-red-200",
      badgeColor: "bg-red-500"
    }
  };

  const sizeClasses = {
    sm: {
      icon: "h-3 w-3",
      text: "text-xs",
      padding: "px-1.5 py-0.5"
    },
    md: {
      icon: "h-4 w-4",
      text: "text-sm",
      padding: "px-2 py-1"
    },
    lg: {
      icon: "h-5 w-5",
      text: "text-base",
      padding: "px-3 py-1.5"
    }
  };
  
  const { icon: Icon, color, bgColor, borderColor, badgeColor } = config[type];
  const { icon: iconSize, text: textSize, padding } = sizeClasses[size];
  
  return (
    <div className={cn(
      "inline-flex items-center gap-1.5 rounded-md border",
      bgColor,
      borderColor,
      padding,
      className
    )}>
      {showIcon && <Icon className={cn(iconSize, color)} />}
      {value && (
        <span className={cn(textSize, color, "font-medium")}>
          {value}
        </span>
      )}
      {showBadge && (
        <Badge className={cn(
          "text-white text-xs",
          badgeColor
        )}>
          {type === "better" ? "↑" : type === "worse" ? "↓" : type === "same" ? "=" : "✗"}
        </Badge>
      )}
    </div>
  );
}

interface ComparisonRowProps {
  label: string;
  currentValue: string;
  newValue: string;
  type: "better" | "worse" | "same" | "missing";
  notes?: string;
}

export function ComparisonRow({ label, currentValue, newValue, type, notes }: ComparisonRowProps): React.ReactElement {
  return (
    <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100 last:border-b-0">
      <div className="text-sm font-medium text-gray-700">{label}</div>
      <div className="text-sm text-gray-600">{currentValue}</div>
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-900">{newValue}</span>
        <ComparisonIndicator type={type} size="sm" showBadge={false} />
        {notes && (
          <span className="text-xs text-gray-500">({notes})</span>
        )}
      </div>
    </div>
  );
}

interface ComparisonSummaryProps {
  betterCount: number;
  worseCount: number;
  sameCount: number;
  missingCount: number;
}

export function ComparisonSummary({ betterCount, worseCount, sameCount, missingCount }: ComparisonSummaryProps): React.ReactElement {
  const total = betterCount + worseCount + sameCount + missingCount;
  
  if (total === 0) {
    return (
      <div className="text-sm text-gray-500">
        No hay coberturas para comparar
      </div>
    );
  }
  
  return (
    <div className="flex flex-wrap gap-2">
      {betterCount > 0 && (
        <ComparisonIndicator 
          type="better" 
          value={`${betterCount} mejores`} 
          size="sm" 
        />
      )}
      {worseCount > 0 && (
        <ComparisonIndicator 
          type="worse" 
          value={`${worseCount} peores`} 
          size="sm" 
        />
      )}
      {sameCount > 0 && (
        <ComparisonIndicator 
          type="same" 
          value={`${sameCount} iguales`} 
          size="sm" 
        />
      )}
      {missingCount > 0 && (
        <ComparisonIndicator 
          type="missing" 
          value={`${missingCount} faltantes`} 
          size="sm" 
        />
      )}
    </div>
  );
}