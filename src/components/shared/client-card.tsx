import React from "react";
import Image from "next/image";
import { ZCard } from "./z-card";
import { cn } from "@/lib/utils";

interface ClientCardProps {
  clientName: string;
  clientImage?: string;
  AssetType?: "car" | "motorcycle" | "van";
  paymentType?: string;
  location?: string;
  contact?: string;
  policyCount?: number;
  renewalMonths?: number;
  className?: string;
}

export function ClientCard({
  clientName,
  clientImage,
  AssetType,
  paymentType,
  location,
  contact,
  policyCount,
  renewalMonths,
  className,
  ...props
}: ClientCardProps & Omit<React.HTMLAttributes<HTMLDivElement>, "title">) {
  return (
    <ZCard variant="client" className={cn("", className)} {...props}>
      <div className="flex items-center gap-4">
        <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center overflow-hidden">
          {clientImage ? (
            <Image
              src={clientImage}
              alt={clientName}
              width={40}
              height={40}
              className="h-full w-full object-cover"
            />
          ) : (
            <span className="text-lg font-medium">{clientName.charAt(0)}</span>
          )}
        </div>
        <div>
          <h3 className="font-medium">{clientName}</h3>
          <div className="flex gap-2 text-xs text-muted-foreground">
            {AssetType && (
              <span>
                {AssetType === "car"
                  ? "Coche"
                  : AssetType === "motorcycle"
                  ? "Moto"
                  : "Furgoneta"}
              </span>
            )}
            {paymentType && <span>•</span>}
            {paymentType && <span>{paymentType}</span>}
            {location && <span>•</span>}
            {location && <span>{location}</span>}
          </div>
        </div>
      </div>
      {(contact ?? policyCount ?? renewalMonths !== undefined) && (
        <div className="mt-4 flex justify-between items-center text-sm">
          {contact && <div>📞 {contact}</div>}
          {policyCount !== undefined && <div>Pólizas: {policyCount}</div>}
          {renewalMonths !== undefined && (
            <div
              className={cn(
                "px-2 py-1 rounded text-xs",
                renewalMonths <= 1
                  ? "bg-red-100 text-red-800"
                  : "bg-blue-100 text-blue-800"
              )}
            >
              Renovación: {renewalMonths}{" "}
              {renewalMonths === 1 ? "mes" : "meses"}
            </div>
          )}
        </div>
      )}
    </ZCard>
  );
}