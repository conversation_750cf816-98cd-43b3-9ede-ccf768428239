import React, { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

interface ZCountdownProps {
  endTime: Date;
  onComplete?: () => void;
  className?: string;
  compact?: boolean;
}

export function Countdown({
  endTime,
  onComplete,
  className,
  compact = false,
}: ZCountdownProps) {
  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  function calculateTimeLeft() {
    const difference = +endTime - +new Date();
    let timeLeft = {
      hours: 0,
      minutes: 0,
      seconds: 0,
      isComplete: false,
    };

    if (difference > 0) {
      timeLeft = {
        hours: Math.floor(difference / (1000 * 60 * 60)),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60),
        isComplete: false,
      };
    } else {
      timeLeft.isComplete = true;
    }

    return timeLeft;
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      const newTimeLeft = calculateTimeLeft();
      setTimeLeft(newTimeLeft);

      if (newTimeLeft.isComplete && onComplete) {
        onComplete();
      }
    }, 1000);

    return () => clearTimeout(timer);
  });

  const formatNumber = (num: number): string => {
    return num < 10 ? `0${num}` : `${num}`;
  };

  if (compact) {
    return (
      <div className={cn("text-xs font-medium", className)}>
        {timeLeft.hours}hr {timeLeft.minutes}min {timeLeft.seconds}sec
      </div>
    );
  }

  return (
    <div className={cn("flex items-center gap-1 font-mono", className)}>
      <span className="px-2 py-1 bg-gray-100 rounded">
        {formatNumber(timeLeft.hours)}
      </span>
      <span>:</span>
      <span className="px-2 py-1 bg-gray-100 rounded">
        {formatNumber(timeLeft.minutes)}
      </span>
      <span>:</span>
      <span className="px-2 py-1 bg-gray-100 rounded">
        {formatNumber(timeLeft.seconds)}
      </span>
    </div>
  );
}

export default Countdown;