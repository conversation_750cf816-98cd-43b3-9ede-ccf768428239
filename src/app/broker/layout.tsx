import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bar<PERSON>rigger, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/shared/app-sidebar";
import { createClient } from "@/lib/supabase/server";
import { UserDataProvider } from "@/features/auth/components/user-data-provider";
import { extractUserName } from "@/features/auth/utils/user-metadata";
import { UserService } from "@/features/auth/services/user.service";
import { Role } from "@prisma/client";
import React from "react";

export default async function BrokerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const { data: { user: authUser } } = await supabase.auth.getUser();
  const { fullName } = authUser ? extractUserName(authUser) : { fullName: "Usuario" };
  
  // Get database user
  const user = authUser ? await UserService.getById(authUser.id) : null;

  return (
    <SidebarProvider>
      <AppSidebar userRole={Role.BROKER} />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <UserDataProvider user={user} userName={fullName}>
            {children}
          </UserDataProvider>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}