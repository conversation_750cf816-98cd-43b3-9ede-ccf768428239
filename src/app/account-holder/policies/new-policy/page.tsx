"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { PolicyStepper } from "@/features/policies/components/upload/policy-stepper";
import { useEffect, useState } from "react";

export default function AccountHolderNewPolicyPage() {
  const router = useRouter();
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="flex flex-1 flex-col">
      {/* Sticky Header */}
      <div className={`sticky top-0 z-10 bg-white py-4 ${isScrolled ? "shadow-md" : ""} transition-shadow duration-200`}>
        <div className="px-4">
          {/* Title with Sidebar Trigger and Back Button */}
          <div className="flex items-center gap-4 mb-4">
            <SidebarTrigger className="-ml-1 hover:bg-gray-100" />
            <Button
              variant="outline"
              size="icon"
              onClick={() => router.back()}
              className="hover:bg-primary hover:border-primary hover:text-primary-foreground"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Nueva Subasta</h1>
              <p className="text-gray-600">Crea una nueva subasta para tu póliza de seguro</p>
            </div>
          </div>
          <Separator className="mb-4" />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 px-4 pb-4">
        <PolicyStepper />
      </div>
    </div>
  );
}