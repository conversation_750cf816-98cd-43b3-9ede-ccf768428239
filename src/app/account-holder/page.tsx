import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { UserService } from "@/features/auth/services/user.service";
import { AuthService } from "@/features/auth/services/auth.service";

export default async function AccountHolderLandingPage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/login");
  }

  // Check database for user profile - this is the source of truth
  const appUser = await UserService.getById(user.id);

  if (!appUser) {
    return redirect("/error?message=profile_not_found");
  }

  // Check if profile is complete based on database
  const isProfileComplete = !!appUser.phone;

  if (!isProfileComplete) {
    return redirect("/complete-profile");
  }

  // Profile is complete, redirect to home
  const homeRoute = AuthService.getHomeRouteForRole(appUser.role);
  return redirect(homeRoute);
}