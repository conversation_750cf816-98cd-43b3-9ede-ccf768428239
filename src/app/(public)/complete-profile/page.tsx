"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { PhoneInput } from "@/components/ui/phone-input";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { useToast } from "@/components/ui/use-toast";
import { updatePhoneNumber } from "@/features/auth/actions/complete-profile";
import { AuthSchemas, PhoneFormValues } from "@/lib/schemas";

export default function CompleteProfilePage() {
    const { toast } = useToast();

    const form = useForm<PhoneFormValues>({
        resolver: zodResolver(AuthSchemas.ClientPhoneSchema),
        defaultValues: {
            phone: "",
        },
    });

    const { isSubmitting } = form.formState;

    async function onSubmit(values: PhoneFormValues) {
        const formData = new FormData();
        formData.append("phone", values.phone);

        // The server action will now handle the redirect on success.
        // It will only return if there's an error.
        const result = await updatePhoneNumber(formData);

        if (result?.error) {
            toast({
                variant: "destructive",
                title: "Error al actualizar",
                description: result.error,
            });
        }
    }

    return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
            <div className="w-full max-w-sm">
                <div className="flex flex-col items-center gap-2 mb-6 text-center">
                    <Image
                        src="/logo-short-dark.svg"
                        alt="Zeeguros Logo"
                        className="h-10 w-10"
                        width={56}
                        height={54}
                    />
                    <h1 className="text-2xl font-bold">Un último paso</h1>
                    <p className="text-muted-foreground">
                        Por favor, introduce tu número de teléfono para completar tu perfil.
                    </p>
                </div>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                            control={form.control}
                            name="phone"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Número de teléfono</FormLabel>
                                    <FormControl>
                                        <PhoneInput
                                            placeholder="Introduce tu número de teléfono"
                                            defaultCountry="ES"
                                            disabled={isSubmitting}
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <Button type="submit" className="w-full" disabled={isSubmitting}>
                            {isSubmitting ? "Guardando..." : "Guardar y continuar"}
                        </Button>
                    </form>
                </Form>
            </div>
        </div>
    );
}