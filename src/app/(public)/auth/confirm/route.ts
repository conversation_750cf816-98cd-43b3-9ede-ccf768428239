import { type NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { syncUserWithDatabase } from "@/features/auth/utils/sync-user";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get("code");
  const token_hash = searchParams.get("token_hash");
  const type = searchParams.get("type");
  const supabase = await createClient();

  // Handle OAuth callback (Google sign-in)
  if (code) {
    const { data: { session } } = await supabase.auth.exchangeCodeForSession(code);

    if (session?.user) {
      await syncUserWithDatabase(session.user, supabase);
    }
  }
  
  // Handle email confirmation (regular signup)
  else if (token_hash && type) {
    const { data: { session }, error } = await supabase.auth.verifyOtp({
      token_hash,
      type: type as "email" | "email_change" | "recovery" | "invite" | "magiclink" | "signup"
    });

    if (session?.user && !error) {
      await syncUserWithDatabase(session.user, supabase);
    }
  }

  // Always redirect to the root. The middleware will take it from here.
  return NextResponse.redirect(new URL("/", request.url));
}