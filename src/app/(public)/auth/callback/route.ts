import { NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { syncUserWithDatabase } from "@/features/auth/utils/sync-user";
import { AuthService } from "@/features/auth/services/auth.service";
import { UserService } from "@/features/auth/services/user.service";

export async function GET(request: Request) {
    const { searchParams, origin } = new URL(request.url);
    const code = searchParams.get("code");

    if (code) {
        const supabase = await createClient();
        const { data, error } = await supabase.auth.exchangeCodeForSession(code);

        if (error) {
            return NextResponse.redirect(`${origin}/login?error=Authentication failed. Please try again.`);
        }

        if (data.user) {
            await syncUserWithDatabase(data.user);

            const appUser = data.user.email ? await UserService.getByEmail(data.user.email) : null;

            if (!appUser) {
                return NextResponse.redirect(`${origin}/login?error=User profile could not be created.`);
            }
            const role = appUser?.role;
            const homeRoute = AuthService.getHomeRouteForRole(role);
            return NextResponse.redirect(`${origin}${homeRoute}`);
        }
    }

    return NextResponse.redirect(`${origin}/login?error=Invalid authentication code.`);
}