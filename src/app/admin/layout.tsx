import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/shared/app-sidebar";
import { createClient } from "@/lib/supabase/server";
import { UserDataProvider } from "@/features/auth/components/user-data-provider";
import { extractUserName } from "@/features/auth/utils/user-metadata";
import { UserService } from "@/features/auth/services/user.service";
import { Role } from "@prisma/client";
import React from "react";

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const { data: { user: authUser } } = await supabase.auth.getUser();
  const { fullName } = authUser ? extractUserName(authUser) : { fullName: "Admin" };
  
  // Get database user
  const user = authUser ? await UserService.getById(authUser.id) : null;

  return (
    <SidebarProvider>
      <AppSidebar userRole={Role.ADMIN} />
      <SidebarInset>
        <div className="flex flex-1 flex-col">
          <UserDataProvider user={user} userName={fullName}>
            {children}
          </UserDataProvider>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}