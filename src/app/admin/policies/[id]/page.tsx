"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Download, Car, Bike, Calendar, User, FileText, Gavel, Eye, Edit, Trash2, AlertTriangle } from "lucide-react";
import { formatAssetType } from "@/lib/format-asset-type";
import { PolicyStateEnum } from "@prisma/client";
import Link from "next/link";
// Removed server action import - now using API route

interface AdminPolicyDetails {
  id: string;
  policyNumber: string;
  accountHolderName: string;
  brokerName: string;
  type: string;
  icon: JSX.Element;
  status: string;
  effectiveDate: string;
  expirationDate: string;
  premium: string;
  auctionCommission: string;
  commissionRate: string;
  carrier: string;
  coverages: {
    name: string;
    limit: string;
    deductible: string;
  }[];
  documents: {
    name: string;
    date: string;
    url: string;
  }[];
  accountHolderContact: {
    email: string;
    phone: string;
  };
  brokerContact: {
    email: string;
    phone: string;
  };
  auctionInfo?: {
    status: string;
    startDate: string;
    endDate: string;
    totalBids: number;
    winningBid?: number;
  };
  systemInfo: {
    createdAt: string;
    lastUpdated: string;
    source: string;
  };
}

export default function AdminPolicyDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [policy, setPolicy] = useState<AdminPolicyDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const policyId = params.id as string;

  useEffect(() => {
    const fetchPolicyDetails = async () => {
      try {
        setIsLoading(true);

        // Get policy from database using API route
        const response = await fetch(`/api/policies/${policyId}`);
        const result = await response.json();
        
        if (!response.ok || !result.success) {
          setIsLoading(false);
          return;
        }
        
        const data = result.data;

        // Transform data to match admin view
        const startDate = data.startDate ? new Date(data.startDate) : new Date();
        const endDate = data.endDate ? new Date(data.endDate) : new Date();
        const startDateStr = startDate.toISOString().split("T")[0] ?? "";
        const endDateStr = endDate.toISOString().split("T")[0] ?? "";

        // Determine asset type safely
        const assetType = data.asset?.type ?? "CAR";
        const isCarInsurance = assetType === "CAR";

        // Calculate auctionCommission (assume 10% for demo)
        const premiumValue = data.premium ?? 0;
        const auctionCommissionValue = premiumValue * 0.1;

        const policyDetails: AdminPolicyDetails = {
          id: data.id,
          policyNumber: data.policyNumber ?? data.id.substring(0, 8).toUpperCase(),
          accountHolderName: data.accountHolder?.firstName
            ? `${data.accountHolder.firstName} ${data.accountHolder.lastName ?? ""}`
            : "Cliente",
          brokerName: data.brokerName ?? "Pendiente",
          type: isCarInsurance ? "Seguro de Coche" : "Seguro de Moto",
          icon: isCarInsurance ? <Car className="h-5 w-5" /> : <Bike className="h-5 w-5" />,
          status: data.status ?? PolicyStateEnum.DRAFT,
          effectiveDate: startDateStr,
          expirationDate: endDateStr,
          premium: `${premiumValue}€`,
          auctionCommission: `${auctionCommissionValue}€`,
          commissionRate: "10%",
          carrier: "Zeeguros",
          coverages: data.coverages?.map((coverage: { customName?: string; type: string; limit?: number; deductible?: number }) => ({
            name: coverage.customName ?? coverage.type,
            limit: coverage.limit ? `${coverage.limit}€` : "Incluido",
            deductible: coverage.deductible ? `${coverage.deductible}€` : "N/A"
          })) ?? [],
          documents: [
            {
              name: "Póliza Original",
              date: startDateStr,
              url: data.pdfUrl ?? "#"
            },
            {
              name: "Contrato de Intermediación",
              date: startDateStr,
              url: "#"
            },
            {
              name: "Registro de Auditoría",
              date: startDateStr,
              url: "#"
            },
          ],
          accountHolderContact: {
            email: "<EMAIL>",
            phone: "+34 600 000 000"
          },
          brokerContact: {
            email: "<EMAIL>",
            phone: "+34 600 000 001"
          },
          auctionInfo: {
            status: "AWARDED",
            startDate: startDateStr,
            endDate: endDateStr,
            totalBids: 5,
            winningBid: premiumValue * 0.9
          },
          systemInfo: {
            createdAt: startDateStr,
            lastUpdated: startDateStr,
            source: "Portal Web"
          }
        };

        setPolicy(policyDetails);
      } catch {
      } finally {
        setIsLoading(false);
      }
    };

    fetchPolicyDetails();
  }, [policyId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!policy) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-semibold tracking-tight">
            Detalles de Póliza
          </h1>
        </div>

        <Card className="bg-white">
          <CardContent className="p-6 flex flex-col items-center justify-center py-10">
            <p className="text-muted-foreground text-center">No hay datos de póliza disponibles.</p>
            <Button variant="outline" className="mt-4" onClick={() => router.push("/admin/policies")}>
              Volver a Pólizas
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case PolicyStateEnum.ACTIVE:
        return <Badge className="bg-green-100 text-green-800 border-green-200">Activa</Badge>;
      case PolicyStateEnum.RENEW_SOON:
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Por renovar</Badge>;
      case PolicyStateEnum.EXPIRED:
        return <Badge className="bg-red-100 text-red-800 border-red-200">Expirada</Badge>;
      case PolicyStateEnum.DRAFT:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Borrador</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">
              Detalles de Póliza
            </h1>
            <p className="text-muted-foreground">#{policy.policyNumber}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Editar
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button variant="destructive" size="sm">
            <Trash2 className="h-4 w-4 mr-2" />
            Eliminar
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-4">
        <Card className="md:col-span-3">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl">Información General</CardTitle>
            <div className="flex items-center gap-2">
              {getStatusBadge(policy.status)}
              <div className="rounded-full bg-primary/10 p-2">
                {policy.icon}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Número de Póliza</p>
                <p className="font-medium">{policy.policyNumber}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Tipo de Póliza</p>
                <p className="font-medium">{formatAssetType(policy.type)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Titular</p>
                <p className="font-medium">{policy.accountHolderName}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Corredor</p>
                <p className="font-medium">{policy.brokerName}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Fecha de Inicio</p>
                <p className="font-medium">{policy.effectiveDate}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Fecha de Vencimiento</p>
                <p className="font-medium">{policy.expirationDate}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Prima Anual</p>
                <p className="font-medium">{policy.premium}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Comisión</p>
                <p className="font-medium text-green-600">{policy.auctionCommission}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Acciones Rápidas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" size="sm" className="w-full">
                <Eye className="h-4 w-4 mr-2" />
                Ver Auditoría
              </Button>
              <Button variant="outline" size="sm" className="w-full">
                <User className="h-4 w-4 mr-2" />
                Contactar Cliente
              </Button>
              <Button variant="outline" size="sm" className="w-full">
                <Gavel className="h-4 w-4 mr-2" />
                Ver Subasta
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Información del Sistema
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground">Creado</p>
                  <p className="font-medium">{policy.systemInfo.createdAt}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Última Actualización</p>
                  <p className="font-medium">{policy.systemInfo.lastUpdated}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Fuente</p>
                  <p className="font-medium">{policy.systemInfo.source}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Tabs defaultValue="coverages" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="coverages">Coberturas</TabsTrigger>
          <TabsTrigger value="contacts">Contactos</TabsTrigger>
          <TabsTrigger value="auction">Subasta</TabsTrigger>
          <TabsTrigger value="documents">Documentos</TabsTrigger>
        </TabsList>

        <TabsContent value="coverages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Coberturas de la Póliza</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4">Cobertura</th>
                      <th className="text-left py-3 px-4">Límite</th>
                      <th className="text-left py-3 px-4">Deducible</th>
                    </tr>
                  </thead>
                  <tbody>
                    {policy.coverages.map((coverage, index) => (
                      <tr key={index} className="border-b">
                        <td className="py-3 px-4">{coverage.name}</td>
                        <td className="py-3 px-4">{coverage.limit}</td>
                        <td className="py-3 px-4">{coverage.deductible}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contacts" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Titular de la Póliza
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-muted-foreground">Nombre</p>
                    <p className="font-medium">{policy.accountHolderName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-medium">{policy.accountHolderContact.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Teléfono</p>
                    <p className="font-medium">{policy.accountHolderContact.phone}</p>
                  </div>
                  <Button variant="outline" size="sm" className="w-full">
                    Contactar
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Corredor Asignado
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-muted-foreground">Nombre</p>
                    <p className="font-medium">{policy.brokerName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-medium">{policy.brokerContact.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Teléfono</p>
                    <p className="font-medium">{policy.brokerContact.phone}</p>
                  </div>
                  <Button variant="outline" size="sm" className="w-full">
                    Contactar
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="auction" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gavel className="h-5 w-5" />
                Información de la Subasta
              </CardTitle>
            </CardHeader>
            <CardContent>
              {policy.auctionInfo ? (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Estado</p>
                    <Badge className="bg-purple-100 text-purple-800 border-purple-200">
                      {policy.auctionInfo.status}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total de Ofertas</p>
                    <p className="font-medium">{policy.auctionInfo.totalBids}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Fecha de Inicio</p>
                    <p className="font-medium">{policy.auctionInfo.startDate}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Fecha de Fin</p>
                    <p className="font-medium">{policy.auctionInfo.endDate}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Oferta Ganadora</p>
                    <p className="font-medium text-green-600">{policy.auctionInfo.winningBid}€</p>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">No hay información de subasta disponible.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Documentos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {policy.documents.map((doc, index) => (
                  <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{doc.name}</p>
                      <p className="text-sm text-muted-foreground">{doc.date}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={doc.url}>
                          <Eye className="h-4 w-4 mr-2" />
                          Ver
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={doc.url}>
                          <Download className="h-4 w-4 mr-2" />
                          Descargar
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}