import { DashboardLayoutClient } from "@/components/shared/dashboard-layout-client";
import { createClient } from "@/lib/supabase/server";
import { extractUserName } from "@/features/auth/utils/user-metadata";

export default async function AdminSettingsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  const { firstName } = user ? extractUserName(user) : { firstName: "" };

  // Get user initials for avatar
  const userInitials = firstName?.charAt(0) ?? "Z";

  return <DashboardLayoutClient userInitials={userInitials}>{children}</DashboardLayoutClient>;
}