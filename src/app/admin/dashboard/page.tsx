"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  FileText,
  Building2,
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Activity
} from "lucide-react";
import Link from "next/link";

// Mock data for admin dashboard
const mockDashboardData = {
  totalUsers: 1247,
  totalPolicies: 856,
  activeBrokers: 45,
  totalRevenue: 125680,
  pendingApprovals: 12,
  activeAuctions: 8,
  recentActivity: [
    { id: 1, type: "policy", action: "Nueva póliza creada", user: "<PERSON>", time: "hace 5 min" },
    { id: 2, type: "auction", action: "Subasta finalizada", user: "Seguros ABC", time: "hace 15 min" },
    { id: 3, type: "approval", action: "Póliza aprobada", user: "Admin", time: "hace 30 min" },
    { id: 4, type: "broker", action: "Nuevo corredor registrado", user: "Broker XYZ", time: "hace 1 hora" }
  ],
  systemHealth: {
    database: "healthy",
    api: "healthy",
    storage: "warning",
    auctions: "healthy"
  }
};

export default function AdminDashboardPage() {
  const [dashboardData] = useState(mockDashboardData);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">
            Panel de Administración
          </h1>
          <p className="text-muted-foreground">
            Gestión integral de la plataforma Zeeguros
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/admin/policies">
              <FileText className="h-4 w-4 mr-2" />
              Gestionar Pólizas
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/admin/settings">
              <Activity className="h-4 w-4 mr-2" />
              Configuración
            </Link>
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Usuarios</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.totalUsers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">+12% vs mes anterior</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pólizas</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.totalPolicies.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">+8% vs mes anterior</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Corredores Activos</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.activeBrokers}</div>
            <p className="text-xs text-muted-foreground">+3 este mes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ingresos Totales</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.totalRevenue.toLocaleString()}€</div>
            <p className="text-xs text-muted-foreground">+15% vs mes anterior</p>
          </CardContent>
        </Card>
      </div>

      {/* Action Items */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aprobaciones Pendientes</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{dashboardData.pendingApprovals}</div>
            <p className="text-xs text-muted-foreground">Requieren atención inmediata</p>
            <Button size="sm" className="mt-2" asChild>
              <Link href="/admin/policies?status=DRAFT">
                Ver Pendientes
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Subastas Activas</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{dashboardData.activeAuctions}</div>
            <p className="text-xs text-muted-foreground">En progreso ahora</p>
            <Button size="sm" className="mt-2" variant="outline" asChild>
              <Link href="/admin/auctions">
                Ver Subastas
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* System Health & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* System Health */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Estado del Sistema</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(dashboardData.systemHealth).map(([service, status]) => (
                <div key={service} className="flex items-center justify-between">
                  <span className="capitalize">{service}</span>
                  <Badge
                    variant={status === "healthy" ? "default" : "destructive"}
                    className={status === "healthy" ? "bg-green-100 text-green-800" :
                               status === "warning" ? "bg-yellow-100 text-yellow-800" : ""}
                  >
                    {status === "healthy" ? "Saludable" :
                     status === "warning" ? "Advertencia" : "Error"}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Actividad Reciente</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dashboardData.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3">
                  <div className="rounded-full bg-primary/10 p-1">
                    {activity.type === "policy" && <FileText className="h-3 w-3 text-primary" />}
                    {activity.type === "auction" && <TrendingUp className="h-3 w-3 text-primary" />}
                    {activity.type === "approval" && <CheckCircle className="h-3 w-3 text-primary" />}
                    {activity.type === "broker" && <Building2 className="h-3 w-3 text-primary" />}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{activity.action}</p>
                    <p className="text-xs text-muted-foreground">
                      {activity.user} • {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}