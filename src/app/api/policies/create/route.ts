import { createApiRoute } from "@/lib/api/route-factory";
import { resolveAccountHolderProfile } from "@/features/auth/utils/profile-resolver";
import { PolicyUploadService } from "@/lib/services/policy-upload.service";
import { PolicyStateService } from "@/lib/services/policy-state.service";
import { db } from "@/lib/db";
import { Role, AssetType, PolicyStateEnum } from "@prisma/client";
import { PolicySchemas } from "@/lib/schemas";



export const POST = createApiRoute()
  .auth(Role.ACCOUNT_HOLDER)
  .handler(async ({ user, request }) => {
    const formData = await request.formData();
    const type = formData.get("type") as AssetType;
    const file = formData.get("file") as File | null;
    const termsAccepted = formData.get("termsAccepted") === "true";

    const validationResult = PolicySchemas.CreateForm.safeParse({
      type,
      file: file ?? undefined,
      termsAccepted
    });

    if (!validationResult.success) {
      throw new Error("Datos inválidos");
    }

    let documentPath = null;
    let policyId = null;

    if (file) {
      const accountHolderProfile = await resolveAccountHolderProfile(user);

      const uploadResult = await PolicyUploadService.uploadNewPolicyFile(
        file,
        file.name,
        user.id,
        accountHolderProfile.id
      );

      const asset = await db.asset.create({
        data: {
          accountHolderId: accountHolderProfile.id,
          assetType: type,
          description: `${type === AssetType.CAR ? "Coche" : "Motocicleta"} - Pendiente de completar información`
        }
      });

      await db.vehicle.create({
        data: {
          assetId: asset.id
        }
      });

      const newPolicy = await db.policy.create({
        data: {
          accountHolder: {
            connect: {
              id: accountHolderProfile.id
            }
          },
          document: {
            connect: {
              id: uploadResult.documentation.id
            }
          },
          asset: {
            connect: {
              id: asset.id
            }
          },
          isAssetsTypeConfirmed: true,
          termsAccepted,
          termsAcceptedAt: termsAccepted ? new Date() : null
        }
      });
      
      // Create initial state history entry
      await PolicyStateService.createState(newPolicy.id, PolicyStateEnum.DRAFT);
      
      documentPath = uploadResult.r2Key;
      policyId = newPolicy.id;
    }

    return {
      success: true,
      message: "Póliza registrada con éxito",
      policyId,
      documentPath,
      assetType: type
    };
  });
