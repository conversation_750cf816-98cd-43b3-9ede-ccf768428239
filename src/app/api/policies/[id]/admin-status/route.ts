import { createApiRoute } from "@/lib/api/route-factory";
import { PolicySchemas } from "@/lib/schemas";
import { BusinessValidators } from "@/lib/api/validation-middleware";
import { PolicyStateEnum, Role } from "@prisma/client";
import { AdminPolicyService } from "@/features/policies/services/policy.service";
import { AuctionService } from "@/features/auctions/services/auction.service";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger";

export const PATCH = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .validateBody(PolicySchemas.StatusUpdate)
  .validate(BusinessValidators.policyStatusUpdate)
  .handler(async ({ user, role, body, params }) => {
    if (!body) {
      throw new Error("Request body is required");
    }
    const { status } = body;
    const policyId = params?.id ?? "";
    
    logger.business("Policy state update requested", {
      policyId,
      newState: status,
      userRole: role,
      userId: user.id
    });

    // Role-aware authorization - additional validation can be added for account holders
    if (role === Role.ACCOUNT_HOLDER) {
      // Future enhancement: Verify account holder owns this policy
      // const policy = await db.policy.findFirst({
      //   where: { id: policyId, accountHolderId: accountHolderProfile.id }
      // });
      // if (!policy) throw new Error("Policy not found or unauthorized");
    }
    
    // Update policy state using business domain service
    const updatedPolicy = await AdminPolicyService.updateState(policyId, status);
    
    logger.business("Policy state updated successfully", {
      policyId,
      newState: status,
      userRole: role
    });
    
    // Business logic: Create auction if policy ready for renewal
    if (status === PolicyStateEnum.RENEW_SOON) {
      await AuctionService.createAuctionFromPolicy(policyId);
      logger.business("Auction created for policy renewal", {
        policyId,
        reason: "Policy state changed to RENEW_SOON",
        userRole: role
      });
    }
    
    // Revalidate paths
    revalidatePath("/admin/policies");
    revalidatePath(`/admin/policies/${policyId}`);
    revalidatePath("/account-holder/policies");
    revalidatePath(`/account-holder/policies/${policyId}`);
    
    return updatedPolicy;
  });