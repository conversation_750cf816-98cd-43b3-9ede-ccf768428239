import { createApiRoute } from "@/lib/api/route-factory";
import { CommonSchemas } from "@/lib/schemas";
import { db } from "@/lib/db";
import { transformPolicyInsuredPartiesData } from "@/features/policies/utils/insured-party-transformer";
import { Role, PolicyStateEnum } from "@prisma/client";
import { PolicyStateService } from "@/lib/services/policy-state.service";

export const GET = createApiRoute()
  .auth() // Allow any authenticated user (role-based access checked inside)
  .handler(async ({ user, params }) => {
    const validatedParams = CommonSchemas.IDParam.parse(params);
    
    const policy = await db.policy.findUnique({
      where: {
        id: validatedParams.id
      },
      include: {
        asset: {
          include: {
            vehicleDetails: true
          }
        },
        accountHolder: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        },
        coverages: true,
        insuredParties: {
          include: {
            insuredParty: true
          }
        },
        document: {
          select: {
            id: true,
            fileName: true,
            fileSize: true,
            mimeType: true,
            url: true,
            uploadedAt: true
          }
        }
      }
    });

    if (!policy) {
      throw new Error("Póliza no encontrada");
    }

    const userRole = user.user_metadata?.role;
    if (userRole !== Role.ADMIN && policy.accountHolder?.userId !== user.id) {
      throw new Error("No tienes acceso a esta póliza");
    }

    // Get current policy state
    let currentState: PolicyStateEnum;
    try {
      currentState = await PolicyStateService.getCurrentState(policy.id);
    } catch {
      currentState = PolicyStateEnum.DRAFT; // Fallback
    }

    const policyDetails = {
      id: policy.id,
      assetId: policy.assetId,
      accountHolderId: policy.accountHolderId,
      policyNumber: policy.policyNumber,
      status: currentState,
      startDate: policy.startDate?.toISOString() ?? null,
      endDate: policy.endDate?.toISOString() ?? null,
      premium: policy.premium ? parseFloat(policy.premium.toString()) : null,
      productName: policy.productName,
      insurerCompany: policy.insurerCompany,
      paymentPeriod: policy.paymentPeriod,
      termsAccepted: policy.termsAccepted,
      termsAcceptedAt: policy.termsAcceptedAt?.toISOString() ?? null,
      asset: policy.asset ? {
        id: policy.asset.id,
        assetType: policy.asset.assetType,
        description: policy.asset.description,
        value: policy.asset.value ? parseFloat(policy.asset.value.toString()) : null,
        vehicleDetails: policy.asset.vehicleDetails ? {
          licensePlate: policy.asset.vehicleDetails.licensePlate,
          brand: policy.asset.vehicleDetails.brand,
          model: policy.asset.vehicleDetails.model,
          version: policy.asset.vehicleDetails.version,
          year: policy.asset.vehicleDetails.year,
          firstRegistrationDate: policy.asset.vehicleDetails.firstRegistrationDate?.toISOString() ?? null,
          fuelType: policy.asset.vehicleDetails.fuelType,
          powerCv: policy.asset.vehicleDetails.powerCv,
          seats: policy.asset.vehicleDetails.seats,
          usageType: policy.asset.vehicleDetails.usageType,
          garageType: policy.asset.vehicleDetails.garageType,
          kmPerYear: policy.asset.vehicleDetails.kmPerYear,
          isLeased: policy.asset.vehicleDetails.isLeased,
          chassisNumber: policy.asset.vehicleDetails.chassisNumber
        } : null
      } : null,
      accountHolder: policy.accountHolder ? {
        id: policy.accountHolder.id,
        userId: policy.accountHolder.userId,
        user: policy.accountHolder.user
      } : null,
      coverages: policy.coverages.map(coverage => ({
        id: coverage.id,
        type: coverage.type,
        customName: coverage.customName,
        description: coverage.description,
        limit: coverage.limit ? parseFloat(coverage.limit.toString()) : null,
        limitIsUnlimited: coverage.limitIsUnlimited,
        limitIsFullCost: coverage.limitIsFullCost,
        limitPerDay: coverage.limitPerDay ? parseFloat(coverage.limitPerDay.toString()) : null,
        limitMaxDays: coverage.limitMaxDays,
        limitMaxMonths: coverage.limitMaxMonths,
        liabilityBodilyCap: coverage.liabilityBodilyCap ? parseFloat(coverage.liabilityBodilyCap.toString()) : null,
        liabilityPropertyCap: coverage.liabilityPropertyCap ? parseFloat(coverage.liabilityPropertyCap.toString()) : null,
        deductible: coverage.deductible ? parseFloat(coverage.deductible.toString()) : null,
        deductiblePercent: coverage.deductiblePercent ? parseFloat(coverage.deductiblePercent.toString()) : null
      })),
      insuredParties: policy.insuredParties ? transformPolicyInsuredPartiesData(policy.insuredParties) : [],
      document: policy.document,
      createdAt: policy.createdAt.toISOString(),
      updatedAt: policy.updatedAt.toISOString()
    };

    return policyDetails;
  });