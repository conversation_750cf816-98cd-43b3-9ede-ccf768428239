import { createApiRoute } from "@/lib/api/route-factory";
import { PolicyListSchema } from "@/lib/api/list-route-builder";
import { QueryBuilders } from "@/lib/api/query-builders";
import { transformPolicyForList, PolicyWithRelations } from "@/lib/api/response-transformers";
import { resolveAccountHolderProfile } from "@/features/auth/utils/profile-resolver";
import { Role } from "@prisma/client";
import { logger } from "@/lib/logger";

export const GET = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .validateQuery(PolicyListSchema)
  .paginate()
  .handler(async ({ user, role, query, pagination }) => {
    
    logger.business("Policy list requested", {
      userRole: role,
      userId: user.id,
      filters: query,
      pagination
    });

    let result;

    // Role-aware data access
    switch (role) {
      case Role.ACCOUNT_HOLDER:
        // Account holders see only their own policies
        const accountHolderProfile = await resolveAccountHolderProfile(user);
        result = await QueryBuilders.policies()
          .forAccountHolder(accountHolderProfile.id, {
            ...query,
            ...pagination
          });
        break;

      case Role.BROKER:
        // Brokers see policies they're involved with (auctions, bids)
        result = await QueryBuilders.policies()
          .forBroker(user.id, {
            ...query,
            ...pagination
          });
        break;

      case Role.ADMIN:
        // Admins see all policies for administrative purposes
        result = await QueryBuilders.policies()
          .forAdmin({
            ...query,
            ...pagination
          });
        break;

      default:
        throw new Error("Unauthorized role for policy listing");
    }

    // Transform data using centralized transformer (now async)
    const transformedPolicies = await Promise.all(
      result.data.map((policy: PolicyWithRelations) => transformPolicyForList(policy))
    );

    logger.business("Policy list provided", {
      userRole: role,
      policiesCount: transformedPolicies.length,
      totalAvailable: result.total
    });

    // Return paginated result (automatic via createApiRoute)
    return {
      data: transformedPolicies,
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages
    };
  });