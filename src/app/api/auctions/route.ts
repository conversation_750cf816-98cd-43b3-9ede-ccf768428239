import { createApiRoute } from "@/lib/api/route-factory";
import { AuctionListWithMultiStatusSchema } from "@/lib/api/list-route-builder";
import { QueryBuilders } from "@/lib/api/query-builders";
import { transformAuctionForList } from "@/lib/api/response-transformers";
import { resolveAccountHolderProfile } from "@/features/auth/utils/profile-resolver";
import { Role } from "@prisma/client";
import { logger } from "@/lib/logger";

export const GET = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .validateQuery(AuctionListWithMultiStatusSchema)
  .paginate()
  .handler(async ({ user, role, query, pagination }) => {
    
    logger.business("Auction list requested", {
      userRole: role,
      userId: user.id,
      filters: query,
      pagination
    });

    let result;

    // Role-aware data access
    switch (role) {
      case Role.ACCOUNT_HOLDER:
        // Account holders see only their own auctions
        const accountHolderProfile = await resolveAccountHolderProfile(user);
        result = await QueryBuilders.auctions()
          .forAccountHolder(accountHolderProfile.id, {
            ...query,
            ...pagination
          });
        break;

      case Role.BROKER:
        // Brokers see all auctions they can bid on (business logic can be refined)
        result = await QueryBuilders.auctions()
          .forBroker(user.id, {
            ...query,
            ...pagination
          });
        break;

      case Role.ADMIN:
        // Admins see all auctions for administrative purposes
        result = await QueryBuilders.auctions()
          .forAdmin({
            ...query,
            ...pagination
          });
        break;

      default:
        throw new Error("Unauthorized role for auction listing");
    }

    // Transform data using centralized transformer (preserves all business logic)
    const transformedAuctions = await Promise.all(result.data.map(transformAuctionForList));

    logger.business("Auction list provided", {
      userRole: role,
      auctionsCount: transformedAuctions.length,
      totalAvailable: result.total
    });

    // Return with cache control headers (handled by createApiRoute)
    return {
      data: transformedAuctions,
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages
    };
  });