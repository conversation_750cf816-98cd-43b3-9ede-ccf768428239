import { createApiRoute } from "@/lib/api/route-factory";
import { CommonSchemas } from "@/lib/schemas";
import { db } from "@/lib/db";
import { generateAuctionTimeline } from "@/features/auctions/utils/timeline-generator";
import { formatInsurerCompany } from "@/lib/format-insurer";
import { resolveAccountHolderProfile } from "@/features/auth/utils/profile-resolver";
import { Role, AuctionStateEnum, PolicyStateEnum } from "@prisma/client";
import { logger } from "@/lib/logger";
import { AuctionStateService } from "@/lib/services/auction-state.service";

function toIdentifier(id: string) {
  return `ZEE-AU-${id.slice(-6).toUpperCase()}`;
}

export const GET = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .handler(async ({ user, role, params }) => {
    const validatedParams = CommonSchemas.IDParam.parse(params);
    const auctionId = validatedParams.id;
    
    logger.business("Auction details requested", {
      auctionId,
      userRole: role,
      userId: user.id
    });

    // Role-aware data filtering
    const whereClause: { id: string; accountHolderId?: string } = { id: auctionId };
    
    // ACCOUNT_HOLDERS can only see their own auctions
    if (role === Role.ACCOUNT_HOLDER) {
      const accountHolderProfile = await resolveAccountHolderProfile(user);
      whereClause.accountHolderId = accountHolderProfile.id;
    }
    
    // BROKERS and ADMINS can see any auction (different business rules can be added here)
    // For now, brokers and admins can see all auctions for business purposes

    // Get auction with all related data
    let auction = await db.auction.findFirst({
      where: whereClause,
      include: {
        policy: {
          include: {
            asset: {
              include: {
                vehicleDetails: true
              },
            },
            insuredParties: {
              include: {
                insuredParty: {
                  include: {
                    address: true,
                  },
                },
              },
            },
            coverages: true,
            document: true,
          },
        },
        bids: {
          include: {
            broker: {
              include: {
                user: true,
              },
            },
            bidCoverages: true,
            document: true, // Include bid document for closed auctions
          },
          orderBy: {
            amount: "asc", // Sort by lowest premium first
          },
        },
        selectedBid: {
          include: {
            broker: {
              include: {
                user: true,
              },
            },
          },
        },
        newPolicyDocument: true,
      },
    });

    if (!auction) {
      throw new Error("Subasta no encontrada o acceso denegado");
    }

    // For closed auctions, optimize by fetching only the actual winning bids
    const currentState = await AuctionStateService.getCurrentState(auction.id);
    if (currentState === AuctionStateEnum.CLOSED) {
      const optimizedAuction = await db.auction.findFirst({
        where: whereClause,
        include: {
          policy: {
            include: {
              asset: {
                include: {
                  vehicleDetails: true
                },
              },
              insuredParties: {
                include: {
                  insuredParty: {
                    include: {
                      address: true,
                    },
                  },
                },
              },
              coverages: true,
              document: true,
            },
          },
          winners: {
            include: {
              bid: {
                include: {
                  broker: {
                    include: {
                      user: true,
                    },
                  },
                  bidCoverages: true,
                  document: true,
                },
              },
            },
            orderBy: {
              position: "asc", // Sort by winner position (1st, 2nd, 3rd)
            },
          },
          bids: {
            where: {
              id: {
                in: [], // Will be populated with winner bid IDs
              },
            },
            include: {
              broker: {
                include: {
                  user: true,
                },
              },
              bidCoverages: true,
              document: true,
            },
          },
          selectedBid: {
            include: {
              broker: {
                include: {
                  user: true,
                },
              },
            },
          },
          newPolicyDocument: true,
        },
      });

      if (optimizedAuction) {
        // Extract winning bids from the winners relation
        const winningBids = optimizedAuction.winners.map(winner => winner.bid);

        // Replace the bids array with only winning bids, sorted by position
        optimizedAuction.bids = winningBids.sort((a, b) => {
          const aWinner = optimizedAuction.winners.find(w => w.bidId === a.id);
          const bWinner = optimizedAuction.winners.find(w => w.bidId === b.id);
          return (aWinner?.position ?? 0) - (bWinner?.position ?? 0);
        });

        auction = optimizedAuction;
      }
    }

    // Transform auction data
    const id = auction.id;
    const identifier = toIdentifier(id);
    const premium = auction.policy?.premium?.toNumber() ?? 0;
    const insurer = auction.policy?.insurerCompany ?? null;

    let assetDisplayName = "Sin información del activo";
    const asset = auction.policy?.asset;
    if (asset?.vehicleDetails) {
      const v = asset.vehicleDetails;
      const brand = v.brand ?? "Sin marca";
      const model = v.model ?? "Sin modelo";
      const year = v.year ?? "Sin año";
      assetDisplayName = `${brand} ${model} (${year})`;
    } else if (asset?.description) {
      assetDisplayName = asset.description;
    }

    // Role-aware bid data filtering
    const shouldShowContactInfo = role === Role.ACCOUNT_HOLDER && currentState === AuctionStateEnum.CLOSED;
    const shouldShowBrokerDetails = role !== Role.ACCOUNT_HOLDER; // Brokers and admins see more details

    // Transform bids data with role-aware filtering
    const transformedBids = auction.bids.map((bid) => ({
      id: bid.id,
      annualPremium: bid.amount.toNumber(),
      brokerName: bid.broker.user.displayName ?? bid.broker.user.firstName ?? "Broker sin nombre",
      brokerCompany: bid.broker.insurerCompany ? formatInsurerCompany(bid.broker.insurerCompany) : "Empresa no especificada",
      createdAt: bid.createdAt.toISOString(),
      hasDocument: !!bid.documentId,
      // Contact information - role-aware access
      brokerPhone: shouldShowContactInfo || shouldShowBrokerDetails ? bid.broker.user.phone : null,
      brokerEmail: shouldShowContactInfo || shouldShowBrokerDetails ? bid.broker.user.email : null,
      brokerIdentifier: shouldShowContactInfo || shouldShowBrokerDetails ? bid.broker.identifier : null,
      // Document information - role-aware access
      quoteDocument: (shouldShowContactInfo || shouldShowBrokerDetails) && bid.document ? {
        id: bid.document.id,
        fileName: bid.document.fileName,
        fileSize: bid.document.fileSize,
        uploadedAt: bid.document.uploadedAt.toISOString(),
        url: bid.document.url,
      } : null,
      bidCoverages: bid.bidCoverages?.map(coverage => ({
        id: coverage.id,
        type: coverage.type,
        customName: coverage.customName,
        limit: coverage.limit?.toNumber() ?? null,
        deductible: coverage.deductible?.toNumber() ?? null,
        description: coverage.description,
      })) ?? [],
    }));

    // Transform policy data for PolicyDetailsDrawer compatibility
    const transformedPolicy = auction.policy ? {
      id: auction.policy.id,
      policyNumber: auction.policy.policyNumber,
      status: PolicyStateEnum.ACTIVE, // Policies in auctions are typically active
      insurerCompany: auction.policy.insurerCompany,
      premium: auction.policy.premium?.toNumber() ?? 0,
      startDate: auction.policy.startDate?.toISOString() ?? null,
      endDate: auction.policy.endDate?.toISOString() ?? null,
      productName: auction.policy.productName,
      asset: auction.policy.asset ? {
        id: auction.policy.asset.id,
        assetType: auction.policy.asset.assetType,
        description: auction.policy.asset.description,
        vehicleDetails: auction.policy.asset.vehicleDetails ? {
          brand: auction.policy.asset.vehicleDetails.brand,
          model: auction.policy.asset.vehicleDetails.model,
          year: auction.policy.asset.vehicleDetails.year,
          licensePlate: auction.policy.asset.vehicleDetails.licensePlate,
          chassisNumber: auction.policy.asset.vehicleDetails.chassisNumber,
          firstRegistrationDate: auction.policy.asset.vehicleDetails.firstRegistrationDate?.toISOString() ?? null,
          version: auction.policy.asset.vehicleDetails.version,
          fuelType: auction.policy.asset.vehicleDetails.fuelType,
          powerCv: auction.policy.asset.vehicleDetails.powerCv,
          seats: auction.policy.asset.vehicleDetails.seats,
          usageType: auction.policy.asset.vehicleDetails.usageType,
          garageType: auction.policy.asset.vehicleDetails.garageType,
          kmPerYear: auction.policy.asset.vehicleDetails.kmPerYear,
          isLeased: auction.policy.asset.vehicleDetails.isLeased,
        } : null,
      } : null,
      insuredParties: auction.policy.insuredParties?.map(policyParty => ({
        id: policyParty.insuredParty.id,
        fullName: `${policyParty.insuredParty.firstName ?? ""} ${policyParty.insuredParty.lastName ?? ""}`.trim(),
        firstName: policyParty.insuredParty.firstName ?? "",
        lastName: policyParty.insuredParty.lastName ?? "",
        identification: policyParty.insuredParty.identification,
        roles: policyParty.insuredParty.roles ?? [],
        gender: policyParty.insuredParty.gender ?? "",
        email: "", // Email not available in InsuredParty model
        phone: "", // Phone not available in InsuredParty model
        birthDate: policyParty.insuredParty.birthDate?.toISOString() ?? null,
        address: policyParty.insuredParty.address?.street ?? "",
        postalCode: policyParty.insuredParty.address?.postalCode ?? "",
        regionName: policyParty.insuredParty.address?.region ?? "",
        country: policyParty.insuredParty.address?.country ?? "",
      })) ?? [],
      coverages: auction.policy.coverages?.map(coverage => ({
          id: coverage.id,
          title: coverage.customName ?? coverage.type,
          limit: coverage.limit,
          description: coverage.description,
          guaranteeType: coverage.type,
          customName: coverage.customName,
          deductible: coverage.deductible,
        })) ?? [],
      document: auction.policy.document ? {
        id: auction.policy.document.id,
        fileName: auction.policy.document.fileName,
        fileSize: auction.policy.document.fileSize,
        mimeType: auction.policy.document.mimeType,
        url: auction.policy.document.url,
        uploadedAt: auction.policy.document.uploadedAt.toISOString(),
      } : null,
    } : null;

    // Generate dynamic timeline events
    const timelineEvents = generateAuctionTimeline({
      id: auction.id,
      status: currentState,
      startDate: auction.startDate,
      endDate: auction.endDate,
      createdAt: auction.createdAt,
      bids: auction.bids.map(bid => ({
        id: bid.id,
        createdAt: bid.createdAt,
        annualPremium: bid.amount.toNumber(),
        broker: {
          user: {
            displayName: bid.broker.user.displayName,
            firstName: bid.broker.user.firstName,
          }
        }
      }))
    });

    // Transform selected bid and new policy document for SIGNED_POLICY auctions
    const transformedSelectedBid = auction.selectedBid ? {
      id: auction.selectedBid.id,
      annualPremium: auction.selectedBid.amount.toNumber(),
      brokerName: auction.selectedBid.broker.user.displayName ?? auction.selectedBid.broker.user.firstName ?? "Broker sin nombre",
      brokerCompany: auction.selectedBid.broker.insurerCompany ? formatInsurerCompany(auction.selectedBid.broker.insurerCompany) : "Empresa no especificada",
      createdAt: auction.selectedBid.createdAt.toISOString(),
    } : null;

    // Find the extracted policy by matching the new policy document ID
    const extractedPolicy = auction.newPolicyDocument ? 
      await db.policy.findFirst({
        where: {
          documentId: auction.newPolicyDocument.id,
        },
        include: {
          asset: {
            include: {
              vehicleDetails: true
            },
          },
          coverages: true,
        },
      }) : null;

    const transformedNewPolicyDocument = auction.newPolicyDocument ? {
      id: auction.newPolicyDocument.id,
      fileName: auction.newPolicyDocument.fileName,
      extractedPolicy: extractedPolicy ? {
        id: extractedPolicy.id,
        premium: extractedPolicy.premium?.toNumber() ?? 0,
        insurerCompany: extractedPolicy.insurerCompany ?? "Sin aseguradora",
        endDate: extractedPolicy.endDate?.toISOString() ?? null,
        policyNumber: extractedPolicy.policyNumber ?? "Sin número",
      } : null,
    } : null;

    const response = {
      id,
      identifier,
      status: currentState,
      startDate: auction.startDate?.toISOString() ?? null,
      endDate: auction.endDate?.toISOString() ?? null,
      annualPremium: premium,
      currency: "EUR",
      currentInsurer: insurer,
      assetDisplayName,
      assetType: asset?.assetType ?? null,
      quotesReceived: auction.bids.length,
      bids: transformedBids,
      policy: transformedPolicy,
      events: timelineEvents,
      selectedBid: transformedSelectedBid,
      newPolicyDocument: transformedNewPolicyDocument,
    };

    logger.business("Auction details provided", {
      auctionId,
      userRole: role,
      bidsCount: transformedBids.length,
      auctionStatus: currentState
    });

    return response;
  });