import { createApiRoute } from "@/lib/api/route-factory";
import { CommonSchemas } from "@/lib/schemas";
import { db } from "@/lib/db";
import { AuctionStateEnum, PolicyStateEnum, Role } from "@prisma/client";
import { logger } from "@/lib/logger";
import { AuctionStateService } from "@/lib/services/auction-state.service";
import { PolicyStateService } from "@/lib/services/policy-state.service";

export const POST = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .handler(async ({ user, role, params }) => {
    const validatedParams = CommonSchemas.IDParam.parse(params);
    const auctionId = validatedParams.id;

    logger.business("Auction completion requested", {
      auctionId,
      userRole: role,
      userId: user.id
    });

    // Check current auction state
    let currentAuctionState: AuctionStateEnum;
    try {
      currentAuctionState = await AuctionStateService.getCurrentState(auctionId);
    } catch {
      logger.business("Auction completion failed - no state history found", {
        auctionId,
        userRole: role,
        userId: user.id
      });
      throw new Error("Subasta no encontrada");
    }

    if (currentAuctionState !== AuctionStateEnum.SIGNED_POLICY) {
      logger.business("Auction completion failed - invalid state", {
        auctionId,
        currentState: currentAuctionState,
        userRole: role,
        userId: user.id
      });
      throw new Error("La subasta debe estar en estado SIGNED_POLICY para completarse");
    }

    // Role-aware authorization
    const whereClause: {
      id: string;
      accountHolder?: { userId: string };
    } = {
      id: auctionId
    };

    if (role === Role.ACCOUNT_HOLDER) {
      // Account holders can only complete their own auctions
      whereClause.accountHolder = {
        userId: user.id
      };
    }
    // Admins can complete any auction (business support functionality)

    const auction = await db.auction.findFirst({
      where: whereClause,
      include: {
        policy: {
          include: {
            document: true
          }
        },
        newPolicyDocument: true
      }
    });

    if (!auction) {
      logger.business("Auction completion failed - not found or unauthorized", {
        auctionId,
        userRole: role,
        userId: user.id
      });
      throw new Error("Subasta no encontrada o no autorizada");
    }

    if (!auction.newPolicyDocumentId) {
      logger.business("Auction completion failed - no new policy document", {
        auctionId,
        userRole: role
      });
      throw new Error("Debe subir la nueva póliza antes de completar");
    }

    // Get updated auction data with all related information
    const updatedAuction = await db.auction.findFirst({
      where: {
        id: auctionId
      },
      include: {
        policy: {
          include: {
            document: true
          }
        },
        newPolicyDocument: true,
        selectedBid: {
          include: {
            broker: {
              include: {
                user: true
              }
            }
          }
        },
        bids: {
          include: {
            broker: {
              include: {
                user: true
              }
            }
          }
        }
      }
    });

    // Update policy state to ACTIVE - this is the core business logic
    await PolicyStateService.transitionToState(auction.policyId, PolicyStateEnum.ACTIVE);

    logger.business("Auction completed successfully", {
      auctionId,
      policyId: auction.policyId,
      userRole: role,
      selectedBroker: updatedAuction?.selectedBid?.broker.user.email
    });

    return {
      success: true,
      message: "Subasta completada exitosamente",
      data: {
        auction: updatedAuction
      }
    };
  });