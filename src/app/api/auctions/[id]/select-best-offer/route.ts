import { createApiRoute } from "@/lib/api/route-factory";
import { CommonSchemas } from "@/lib/schemas";
import { db } from "@/lib/db";
import { AuctionStateEnum, Role } from "@prisma/client";
import { z } from "zod";
import { logger } from "@/lib/logger";
import { AuctionStateService } from "@/lib/services/auction-state.service";

const selectOfferSchema = z.object({
  bidId: z.string().uuid("Invalid bid ID format")
});

export const POST = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .validateBody(selectOfferSchema)
  .handler(async ({ user, role, params, body }) => {
    const validatedParams = CommonSchemas.IDParam.parse(params);
    const auctionId = validatedParams.id;
    
    if (!body) {
      throw new Error("Request body is required");
    }
    
    const { bidId } = body;

    logger.business("Offer selection requested", {
      auctionId,
      bidId,
      userRole: role,
      userId: user.id
    });

    // Check current auction state
    let currentAuctionState: AuctionStateEnum;
    try {
      currentAuctionState = await AuctionStateService.getCurrentState(auctionId);
    } catch {
      logger.business("Offer selection failed - no state history found", {
        auctionId,
        bidId,
        userRole: role,
        userId: user.id
      });
      throw new Error("Subasta no encontrada");
    }

    if (currentAuctionState !== AuctionStateEnum.CLOSED) {
      logger.business("Offer selection failed - invalid state", {
        auctionId,
        bidId,
        currentState: currentAuctionState,
        userRole: role,
        userId: user.id
      });
      throw new Error("Solo se pueden seleccionar ofertas en subastas cerradas");
    }

    // Role-aware authorization
    const whereClause: {
      id: string;
      accountHolder?: { userId: string };
    } = {
      id: auctionId,
    };

    if (role === Role.ACCOUNT_HOLDER) {
      // Account holders can only select offers for their own auctions
      whereClause.accountHolder = {
        userId: user.id,
      };
    }
    // Admins can help with offer selection for any auction

    // Verify auction exists and user has permission
    const auction = await db.auction.findFirst({
      where: whereClause,
      include: {
        bids: {
          where: {
            id: bidId,
          },
        },
      },
    });

    if (!auction) {
      logger.business("Offer selection failed - auction not found or unauthorized", {
        auctionId,
        bidId,
        userRole: role
      });
      throw new Error("Subasta no encontrada o no autorizada");
    }

    if (auction.bids.length === 0) {
      logger.business("Offer selection failed - bid not found in auction", {
        auctionId,
        bidId,
        userRole: role
      });
      throw new Error("Oferta no encontrada en esta subasta");
    }

    // Update auction to selected bid and transition to SIGNED_POLICY state
    await db.auction.update({
      where: { id: auctionId },
      data: {
        selectedBidId: bidId
      }
    });
    
    // Transition auction state to SIGNED_POLICY
    await AuctionStateService.transitionToState(auctionId, AuctionStateEnum.SIGNED_POLICY);
    
    // Get detailed auction information for response
    const updatedAuction = await db.auction.findFirst({
      where: {
        id: auctionId,
      },
      include: {
        bids: {
          where: {
            id: bidId,
          },
          include: {
            broker: true,
            document: true,
          },
        },
        policy: true,
        selectedBid: {
          include: {
            broker: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    });

    logger.business("Offer selection confirmed", {
      auctionId,
      bidId,
      userRole: role,
      selectedBroker: updatedAuction?.bids[0]?.broker.identifier
    });

    return {
      success: true,
      message: "Confirmación de firma registrada exitosamente",
      data: {
        auction: updatedAuction
      }
    };
  });