import { createApiRoute } from "@/lib/api/route-factory";
import { CommonSchemas } from "@/lib/schemas";
import { db } from "@/lib/db";
import { AuctionStateEnum, Role } from "@prisma/client";
import { PolicyUploadService } from "@/lib/services/policy-upload.service";
import { AuctionStateService } from "@/lib/services/auction-state.service";
import { logger } from "@/lib/logger";

export const POST = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .handler(async ({ user, role, params, request }) => {
    const validatedParams = CommonSchemas.IDParam.parse(params);
    const auctionId = validatedParams.id;

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const fileName = formData.get("fileName") as string;
    const bidId = formData.get("bidId") as string;

    if (!bidId) {
      throw new Error("ID de oferta requerido");
    }

    logger.business("Policy upload to auction requested", {
      auctionId,
      bidId,
      fileName,
      userRole: role,
      userId: user.id
    });

    // Role-aware authorization
    const whereClause: {
      id: string;
      accountHolder?: { userId: string };
    } = {
      id: auctionId,
    };

    if (role === Role.ACCOUNT_HOLDER) {
      // Account holders can only upload policies to their own auctions
      whereClause.accountHolder = {
        userId: user.id,
      };
    }
    // Admins can assist with policy uploads for any auction

    // Verify auction exists and user has permission
    const auction = await db.auction.findFirst({
      where: whereClause,
      include: {
        policy: true,
        accountHolder: true,
        bids: {
          where: {
            id: bidId,
          },
        },
      },
    });

    if (!auction) {
      logger.business("Policy upload failed - auction not found or unauthorized", {
        auctionId,
        bidId,
        userRole: role
      });
      throw new Error("Subasta no encontrada o no autorizada");
    }

    // Check if auction is in CLOSED state using state service
    const currentState = await AuctionStateService.getCurrentState(auction.id);
    if (currentState !== AuctionStateEnum.CLOSED) {
      logger.business("Policy upload failed - auction not closed", {
        auctionId,
        currentState,
        userRole: role
      });
      throw new Error("La subasta debe estar cerrada para subir pólizas");
    }

    if (auction.bids.length === 0) {
      logger.business("Policy upload failed - bid not found in auction", {
        auctionId,
        bidId,
        userRole: role
      });
      throw new Error("Oferta no encontrada en esta subasta");
    }

    // Upload file using unified service (includes AI validation)
    const uploadResult = await PolicyUploadService.uploadAuctionPolicyFile(
      file,
      fileName,
      user.id, // Use user ID for folder organization
      auction.accountHolderId,
      auction.id
    );

    logger.business("Policy file uploaded successfully", {
      auctionId,
      bidId,
      documentId: uploadResult.documentation.id,
      userRole: role
    });

    // Update auction with selected bid, signature confirmation, new policy document, status change, and finalization
    await db.auction.update({
      where: {
        id: auction.id,
      },
      data: {
        selectedBidId: bidId,
        newPolicyDocumentId: uploadResult.documentation.id,
      },
    });

    // Transition auction to SIGNED_POLICY state
    await AuctionStateService.transitionToState(auction.id, AuctionStateEnum.SIGNED_POLICY);

    // Update policy with new document
    await db.policy.update({
      where: {
        id: auction.policyId,
      },
      data: {
        documentId: uploadResult.documentation.id,
      },
    });

    logger.business("Auction updated with new policy document", {
      auctionId,
      policyId: auction.policyId,
      selectedBidId: bidId,
      newStatus: AuctionStateEnum.SIGNED_POLICY,
      userRole: role
    });

    return {
      success: true,
      message: "Póliza subida exitosamente",
      data: {
        documentation: uploadResult.documentation
      }
    };
  });