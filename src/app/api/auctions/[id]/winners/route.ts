import { createApiRoute } from "@/lib/api/route-factory";
import { AuctionSchemas } from "@/lib/schemas";
import { db } from "@/lib/db";
import { AuctionStateEnum, Role } from "@prisma/client";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import { logger } from "@/lib/logger";
import { AuctionStateService } from "@/lib/services/auction-state.service";

// Using Prisma-generated schema for strict validation
const selectWinnersSchema = AuctionSchemas.SelectWinners;
type SelectWinnersBody = z.infer<typeof selectWinnersSchema>;

export const POST = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .validateBody(selectWinnersSchema)
  .handler(async ({ user, role, body, params }) => {
    const { selectedBidIds } = body as SelectWinnersBody;
    const auctionId = params?.id ?? "";

    logger.business("Auction winners selection requested", {
      auctionId,
      selectedBidIds,
      userRole: role,
      userId: user.id
    });

    // Atomic transaction prevents race conditions
    const result = await db.$transaction(async (tx) => {
      // Role-aware authorization
      const whereClause: {
        id: string;
        accountHolderId?: string;
      } = { 
        id: auctionId,
      };

      if (role === Role.ACCOUNT_HOLDER) {
        // Account holders can only select winners for their own auctions
        whereClause.accountHolderId = user.id;
      }
      // Admins can select winners for any auction

      // 1. Lock and validate auction exists and user has access
      const auction = await tx.auction.findUnique({
        where: whereClause,
        select: { id: true, accountHolderId: true }
      });

      if (!auction) {
        logger.business("Winners selection failed - auction not found or unauthorized", {
          auctionId,
          userRole: role
        });
        throw new Error("Auction not found or access denied");
      }

      // 2. Check auction state using state service
      const currentState = await AuctionStateService.getCurrentState(auctionId);
      if (currentState !== AuctionStateEnum.CLOSED) {
        logger.business("Winners selection failed - auction not in CLOSED state", {
          auctionId,
          currentState,
          userRole: role
        });
        throw new Error("Auction must be closed to select winners");
      }

      // 3. Validate bid ownership
      const bids = await tx.bid.findMany({
        where: {
          id: { in: selectedBidIds },
          auctionId: auctionId
        }
      });

      if (bids.length !== selectedBidIds.length) {
        logger.business("Winners selection failed - invalid bids", {
          auctionId,
          expectedBids: selectedBidIds.length,
          foundBids: bids.length,
          userRole: role
        });
        throw new Error("Some selected bids are invalid");
      }

      // 4. Atomic state transition using state service
      await AuctionStateService.transitionToState(auctionId, AuctionStateEnum.SIGNED_POLICY);

      // Get updated auction
      const updatedAuction = await tx.auction.findUnique({
        where: { id: auctionId }
      });

      logger.business("Auction winners selected successfully", {
        auctionId,
        winnersCount: selectedBidIds.length,
        newStatus: AuctionStateEnum.SIGNED_POLICY,
        userRole: role
      });

      return { auction: updatedAuction, selectedBids: bids };
    });

    // Revalidate paths
    revalidatePath(`/auctions/${auctionId}`);

    return {
      success: true,
      message: "¡Ganadores seleccionados con éxito!",
      data: {
        auction: result.auction,
        selectedBids: result.selectedBids
      }
    };
  });