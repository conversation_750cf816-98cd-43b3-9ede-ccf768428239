import { createApiRoute } from "@/lib/api/route-factory";
import { db } from "@/lib/db";
import { Role, AuctionStateEnum } from "@prisma/client";
import { AuctionSchemas } from "@/lib/schemas";
import { AuctionStateService } from "@/lib/services/auction-state.service";

export const POST = createApiRoute()
  .auth(Role.BROKER)
  .validateBody(AuctionSchemas.CreateBid)
  .handler(async ({ user, body }) => {
    if (!body) {
      throw new Error("Request body is required");
    }
    const { policyId, annualPremium } = body;

    const brokerProfile = await db.brokerProfile.findUnique({
      where: { userId: user.id }
    });

    if (!brokerProfile) {
      throw new Error("Perfil de broker no encontrado");
    }

    const auction = await db.auction.findFirst({
      where: {
        policyId
      }
    });

    if (!auction) {
      throw new Error("Subasta no encontrada");
    }

    // Check auction state using state service
    const currentState = await AuctionStateService.getCurrentState(auction.id);
    if (currentState !== AuctionStateEnum.OPEN) {
      throw new Error("La subasta no está activa");
    }

    const existingBid = await db.bid.findFirst({
      where: {
        auctionId: auction.id,
        brokerId: brokerProfile.id
      }
    });

    if (existingBid) {
      await db.bid.update({
        where: { id: existingBid.id },
        data: {
          amount: annualPremium
        }
      });
    } else {
      await db.bid.create({
        data: {
          auctionId: auction.id,
          brokerId: brokerProfile.id,
          amount: annualPremium
        }
      });
    }

    return { success: true };
  });