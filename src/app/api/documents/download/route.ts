
import { NextRequest, NextResponse } from "next/server";
import { r2Client } from "@/lib/r2";
import { db } from "@/lib/db";
import { GetObjectCommand } from "@aws-sdk/client-s3";
import { getCurrentUser } from "@/lib/api-auth";
import { handleApiError, ApiResponses } from "@/lib/api-responses";
import { CommonSchemas } from "@/lib/schemas";
import { BusinessValidators } from "@/lib/api/validation-middleware";

// Special route that returns file instead of JSON - using manual approach with DRY validation
export async function GET(request: NextRequest) {
  try {
    // 1. Parse and validate query
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const { key } = CommonSchemas.DocumentDownload.parse(queryParams);

    // 2. Authenticate user
    const user = await getCurrentUser(request);

    // 3. Validate document access with centralized validator
    const context = { user, role: user?.user_metadata?.role ?? null, query: { key }, request };
    await BusinessValidators.documentAccess(context);

    // 4. Get document details for response headers
    const document = await db.documentation.findFirst({
      where: { url: key },
      select: { fileName: true, mimeType: true }
    });

    if (!document) {
      return ApiResponses.notFound("Document not available");
    }

    // 5. Fetch file from R2
    const bucketName = process.env.R2_BUCKET_NAME;
    if (!bucketName) {
      return ApiResponses.internalServerError("R2 bucket configuration missing");
    }
    const getObjectCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: key,
    });

    const r2Response = await r2Client.send(getObjectCommand);

    if (!r2Response.Body) {
      return ApiResponses.notFound("Document not available");
    }

    // 6. Stream file with proper headers
    const fileBuffer = await r2Response.Body.transformToByteArray();

    return new NextResponse(Buffer.from(fileBuffer), {
      status: 200,
      headers: {
        "Content-Type": document.mimeType ?? "application/octet-stream",
        "Content-Disposition": `attachment; filename="${document.fileName ?? "document"}"`,
        "Content-Length": fileBuffer.byteLength.toString(),
      },
    });

  } catch (error) {
    return handleApiError(error);
  }
}
