import { createApiRoute } from "@/lib/api/route-factory";
import { AuthSchemas } from "@/lib/schemas";
import { AuthService } from "@/features/auth/services/auth.service";
import { ValidationError } from "@/lib/api-responses";
import { logger } from "@/lib/logger";

export const PATCH = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .validateBody(AuthSchemas.PasswordUpdate.or(AuthSchemas.PasswordSetInitial))
  .handler(async ({ user, role, body }) => {
    
    logger.business("Password update requested", {
      userRole: role,
      userId: user.id
    });

    const userHasPassword = await AuthService.checkUserHasPassword();
    
    let result;
    
    if (userHasPassword) {
      // User has password - validate using update schema
      const validationResult = AuthSchemas.PasswordUpdate.parse(body);
      const { currentPassword, newPassword } = validationResult;
      
      if (currentPassword === newPassword) {
        logger.business("Password update failed - same as current", {
          userRole: role,
          userId: user.id
        });
        throw new ValidationError("La nueva contraseña debe ser diferente a la actual.");
      }
      
      if (!user.email) {
        throw new Error("Email del usuario no disponible.");
      }
      
      result = await AuthService.updateUserPasswordWithVerification(
        user.email,
        currentPassword,
        newPassword
      );
    } else {
      // User doesn't have password - validate using initial schema
      const validationResult = AuthSchemas.PasswordSetInitial.parse(body);
      const { newPassword } = validationResult;
      result = await AuthService.setInitialPassword(newPassword);
    }
    
    if (result.error) {
      logger.business("Password update failed", {
        userRole: role,
        userId: user.id,
        error: result.error
      });
      throw new ValidationError(result.error);
    }
    
    const message = userHasPassword 
      ? "Contraseña actualizada correctamente." 
      : "Contraseña establecida correctamente.";

    logger.business("Password updated successfully", {
      userRole: role,
      userId: user.id,
      isInitial: !userHasPassword
    });
      
    return { success: true, message };
  });