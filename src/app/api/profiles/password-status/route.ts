import { createApiRoute } from "@/lib/api/route-factory";
import { AuthService } from "@/features/auth/services/auth.service";
import { logger } from "@/lib/logger";

export const GET = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .handler(async ({ user, role }) => {
    
    logger.business("Password status requested", {
      userRole: role,
      userId: user.id
    });

    const hasPassword = await AuthService.checkUserHasPassword();
    
    logger.business("Password status provided", {
      userRole: role,
      userId: user.id,
      hasPassword
    });
    
    return { success: true, data: { hasPassword } };
  });