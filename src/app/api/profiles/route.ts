import { createApiRoute } from "@/lib/api/route-factory";
import { AuthSchemas } from "@/lib/schemas";
import { db } from "@/lib/db";
import { handlePhoneValidationError } from "@/lib/validation/phone-validation";
import { logger } from "@/lib/logger";

// GET profile route - Role-aware profile access
export const GET = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .handler(async ({ user, role }) => {
    
    logger.business("Profile data requested", {
      userRole: role,
      userId: user.id
    });

    // Get user from database (prioritize DB data over Supabase metadata)
    const dbUser = await db.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        displayName: true,
        phone: true,
        role: true, // Include role for profile display
      },
    });

    if (!dbUser) {
      logger.business("Profile data failed - user not found", {
        userRole: role,
        userId: user.id
      });
      throw new Error("Usuario no encontrado");
    }

    // Ensure we return the data with all fields explicitly
    const userData = {
      id: dbUser.id,
      email: dbUser.email,
      firstName: dbUser.firstName,
      lastName: dbUser.lastName,
      displayName: dbUser.displayName,
      phone: dbUser.phone,
      role: dbUser.role,
    };

    logger.business("Profile data provided", {
      userRole: role,
      userId: user.id
    });

    return { 
      success: true, 
      data: userData 
    };
  });

// PATCH profile route - Role-aware profile updates
export const PATCH = createApiRoute()
  .auth() // Authenticated users only - authorization handled inside
  .validateBody(AuthSchemas.ProfileUpdate)
  .handler(async ({ user, role, body }) => {
    if (!body) throw new Error("Body is required");
    const { firstName, lastName, phone } = body;
    
    logger.business("Profile update requested", {
      userRole: role,
      userId: user.id,
      updates: { firstName, lastName, hasPhone: !!phone }
    });
    
    // Generate display_name from first_name and last_name
    const displayName = [firstName, lastName].filter(Boolean).join(" ");
    
    // Update user in database with phone validation error handling
    let updatedUser;
    try {
      updatedUser = await db.user.update({
        where: { id: user.id },
        data: {
          firstName,
          lastName,
          displayName,
          phone: phone ?? null,
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          phone: true,
          role: true,
        },
      });
    } catch (error: unknown) {
      // Use centralized phone validation error handling
      const phoneValidation = handlePhoneValidationError(error);
      if (phoneValidation.isPhoneUniqueConstraint) {
        logger.business("Profile update failed - phone already exists", {
          userRole: role,
          userId: user.id
        });
        throw new Error(phoneValidation.userFriendlyMessage);
      }
      throw error; // Re-throw if it's a different error
    }
    
    // Sync with Supabase metadata to keep them in sync
    try {
      const { createClient } = await import("@/lib/supabase/server");
      const supabase = await createClient();
      
      const { data: { user: supabaseUser } } = await supabase.auth.getUser();
      if (supabaseUser) {
        const metadataUpdate: Record<string, string | boolean> = {
          ...supabaseUser.user_metadata,
          first_name: firstName,
          last_name: lastName,
          display_name: displayName,
        };
        
        if (phone) {
          metadataUpdate.phone = phone;
          metadataUpdate.phone_number_is_set = true;
        } else {
          metadataUpdate.phone_number_is_set = false;
        }
        
        await supabase.auth.updateUser({
          data: metadataUpdate,
        });
      }
    } catch {
      // Continue anyway - database update succeeded
      logger.business("Supabase metadata sync failed but DB update succeeded", {
        userRole: role,
        userId: user.id
      });
    }
    
    logger.business("Profile updated successfully", {
      userRole: role,
      userId: user.id,
      newDisplayName: displayName
    });
    
    return {
      success: true,
      message: "Perfil actualizado correctamente",
      data: updatedUser
    };
  });