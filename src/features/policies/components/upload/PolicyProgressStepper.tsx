"use client";

import { Check } from "lucide-react";

interface Step {
  title: string;
  description: string;
}

interface PolicyProgressStepperProps {
  currentStep: number;
  steps: Step[];
}

export function PolicyProgressStepper({
  currentStep,
  steps,
}: PolicyProgressStepperProps) {
  return (
    <div className="w-full sticky top-[128px] z-20 bg-background py-4">
      <div className="relative">
        {/* Progress line background */}
        <div className="absolute top-5 left-0 right-0 h-0.5 bg-muted-foreground/30"></div>

        {/* Progress line active */}
        <div
          className="absolute top-5 left-0 h-0.5 bg-[#7AE4AE] transition-all duration-500"
          style={{
            width: `${(currentStep / (steps.length - 1)) * 100}%`
          }}
        ></div>

        <ol className="relative grid grid-cols-3 text-sm font-medium text-center text-muted-foreground">
          {steps.map((step, index) => {
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;
            return (
              <li key={index} className="flex flex-col items-center gap-1">
                <div
                  className={`z-10 flex items-center justify-center w-10 h-10 rounded-full transition-colors ${
                    isActive
                      ? "bg-[#7AE4AE] text-white"
                      : isCompleted
                        ? "bg-[#7AE4AE] text-white"
                        : "bg-slate-200 text-muted-foreground"
                  }`}
                >
                  {isCompleted || (isActive && index === steps.length - 1) ? (
                    <Check className="w-6 h-6" />
                  ) : (
                    index + 1
                  )}
                </div>
                <span className="text-xs sm:text-sm">
                  {step.title}
                </span>
              </li>
            );
          })}
        </ol>
      </div>
    </div>
  );
}