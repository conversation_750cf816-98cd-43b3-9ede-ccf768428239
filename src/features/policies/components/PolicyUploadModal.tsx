"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Upload } from "lucide-react";
import { PolicyFileUploadStep } from "./upload/PolicyFileUploadStep";
import { AuctionBid } from "../../auctions/types/auction";

interface PolicyUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onFinalize: (file: File, bidId: string) => void;
  onBack?: () => void;
  selectedBid: AuctionBid | null;
  auctionId: string;
  loading?: boolean;
}

export function PolicyUploadModal({
  isOpen,
  onClose,
  onFinalize,
  onBack,
  selectedBid,
  auctionId: _auctionId,
  loading = false,
}: PolicyUploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handleTermsAcceptanceChange = (_accepted: boolean) => {
    // Terms acceptance is handled by the PolicyFileUploadStep component
  };

  const handleFinalize = () => {
    if (!selectedFile || !selectedBid) {
      return;
    }
    onFinalize(selectedFile, selectedBid.id);
  };

  const handleClose = () => {
    setSelectedFile(null);
    onClose();
  };

  const handleBackClick = () => {
    setSelectedFile(null);
    if (onBack) {
      onBack();
    } else {
      onClose();
    }
  };

  if (!selectedBid) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="flex items-center gap-3">
            <div className="w-12 h-12 bg-brand-aquamarine-green rounded-lg flex items-center justify-center">
              <Upload className="h-6 w-6 text-black" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Subir Nueva Póliza</h2>
              <p className="text-sm text-gray-600 mt-1">Sube tu nueva póliza firmada con {selectedBid.brokerCompany}</p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="px-6 pb-6">
          <PolicyFileUploadStep
            onBack={handleBackClick}
            onContinue={handleFinalize}
            onFileSelect={handleFileSelect}
            onTermsAcceptanceChange={handleTermsAcceptanceChange}
            isSubmitting={loading}
            title="Arrastra tu póliza aquí o haz clic para seleccionar"
            description="Solo archivos PDF • Máximo 10MB"
            acceptedFileTypes={["application/pdf", "image/jpeg", "image/png"]}
            maxFileSize={10 * 1024 * 1024} // 10MB
            showTermsCheckbox={true}
            continueButtonText="Finalizar y Confirmar"
            backButtonText={onBack ? "Volver" : "Cancelar"}
            processingText="Finalizando..."
            isAuctionMode={true}
            selectedBid={selectedBid}
            showImportantSection={true}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}