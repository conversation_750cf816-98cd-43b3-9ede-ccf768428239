"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { PrimaryButton } from "@/components/shared/primary-button";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Plus, Car, Bike, Search, X, Loader2, AlertCircle, FileText } from "lucide-react";
import Link from "next/link";
import { PolicyStateEnum, AssetType } from "@prisma/client";
import { PolicyCard } from "@/features/policies/components/policy-card";
import { useState, useMemo, ChangeEvent, useEffect } from "react";

import { usePolicies, usePolicyCounts } from "@/features/policies/hooks/usePolicies";
import { transformPoliciesForDisplay, GenericPolicyData } from "@/features/policies/utils/policy-transformer";
import { Separator } from "@/components/ui/separator";

function LoadingState() {
  return (
    <div className="flex items-center justify-center py-12">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
        <p className="text-gray-600">Cargando pólizas...</p>
      </div>
    </div>
  );
}

function ErrorState({ error, onRetry }: { error: string; onRetry: () => void }) {
  return (
    <Card>
      <CardContent className="p-10 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error al cargar pólizas</h3>
        <p className="text-gray-600 mb-6">{error}</p>
        <Button onClick={onRetry} variant="outline">
          Intentar de nuevo
        </Button>
      </CardContent>
    </Card>
  );
}

export function PolicyList() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const [filterStatus, setFilterStatus] = useState<
    PolicyStateEnum | "all" | "renew_soon" | "expired"
  >("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>("");
  const [filterAssetType, setFilterAssetType] = useState<"all" | AssetType>(
    "all"
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(4);

  // Debounce search term to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 800); // 800ms delay - improved UX for typing accented characters

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Convert filter status to API format
  const apiStatusFilter = useMemo(() => {
    if (filterStatus === "all") return undefined;
    if (filterStatus === "renew_soon") return PolicyStateEnum.RENEW_SOON;
    if (filterStatus === "expired") return PolicyStateEnum.EXPIRED;
    return filterStatus;
  }, [filterStatus]);

  // Fetch policies using the new hook
  const {
    data: policiesResponse,
    isLoading,
    error,
    refetch
  } = usePolicies({
    page: currentPage,
    limit: itemsPerPage,
    status: apiStatusFilter,
    assetType: filterAssetType !== "all" ? filterAssetType : undefined,
    search: debouncedSearchTerm || undefined,
  });

  // Fetch total counts for all categories (without filters)
  const {
    data: totalCounts,
    isLoading: isLoadingCounts
  } = usePolicyCounts();

  const pagination = policiesResponse?.meta;

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Reset to first page when debounced search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm]);

  // Transform server data using shared utility
  const transformedPolicies = useMemo(() => {
    const policiesData = policiesResponse?.data ?? [];
    // Cast to GenericPolicyData to handle interface compatibility
    const compatiblePoliciesData = policiesData.map(policy => ({
      ...policy,
      coverages: policy.coverages.map(coverage => ({
        ...coverage,
        title: coverage.customName ?? coverage.type, // Add missing title field
        guaranteeType: coverage.type, // Add missing guaranteeType field
      }))
    }));
    return transformPoliciesForDisplay(compatiblePoliciesData as GenericPolicyData[], "/account-holder");
  }, [policiesResponse?.data]);

  // Use total counts from the dedicated hook for accurate counts
  const drafts = totalCounts?.drafts ?? 0;
  const actives = totalCounts?.actives ?? 0;
  const renewSoon = totalCounts?.renewSoon ?? 0;
  const expired = totalCounts?.expired ?? 0;
  const totalPolicies = totalCounts?.total ?? 0;

  const getFilterLabel = (
    status: PolicyStateEnum | "all" | "renew_soon" | "expired"
  ) => {
    switch (status) {
      case "all":
        return `Todas las pólizas (${totalPolicies})`;
      case PolicyStateEnum.ACTIVE:
        return `Activas (${actives})`;
      case PolicyStateEnum.DRAFT:
        return `Borradores (${drafts})`;
      case PolicyStateEnum.RENEW_SOON:
        return `Renovar Pronto (${renewSoon})`;
      case PolicyStateEnum.EXPIRED:
        return `Expiradas (${expired})`;
      default:
        return `Todas las pólizas (${totalPolicies})`;
    }
  };

  const getButtonClass = (type: "all" | AssetType) => {
    return `${
      filterAssetType === type
        ? "bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground"
        : "bg-white text-gray-700 hover:bg-gray-50 hover:text-gray-700"
    } transition-colors duration-200`;
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (
    value: PolicyStateEnum | "all" | "renew_soon" | "expired"
  ) => {
    setFilterStatus(value);
    setCurrentPage(1);
  };

  const handleAssetTypeFilterChange = (type: "all" | AssetType) => {
    setFilterAssetType(type);
    setCurrentPage(1);
  };

  // Show full loading state only for initial load (when there's no data yet)
  const isInitialLoading = (isLoading && !policiesResponse) || isLoadingCounts;
  const isSearching = isLoading && !!policiesResponse && (searchTerm !== debouncedSearchTerm);

  if (isInitialLoading) {
    return (
      <div className="flex flex-1 flex-col">
        {/* Sticky Header */}
        <div className={`sticky top-0 z-10 bg-white py-4 ${isScrolled ? "shadow-md" : ""} transition-shadow duration-200`}>
          <div className="px-4">
            {/* Title with Sidebar Trigger */}
            <div className="flex items-center gap-4 mb-4">
              <SidebarTrigger className="-ml-1 hover:bg-gray-100" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Mis Pólizas</h1>
                <p className="text-gray-600">Gestiona todas tus pólizas de seguro desde un solo lugar</p>
              </div>
            </div>
            <Separator className="mb-4" />
          </div>
        </div>
        
        <div className="px-3 sm:px-4">
          <LoadingState />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-1 flex-col">
        {/* Sticky Header */}
        <div className={`sticky top-0 z-10 bg-white py-4 ${isScrolled ? "shadow-md" : ""} transition-shadow duration-200`}>
          <div className="px-3 sm:px-4">
            {/* Title with Sidebar Trigger */}
            <div className="flex items-center gap-4 mb-4">
              <SidebarTrigger className="-ml-1 hover:bg-gray-100" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Mis Pólizas</h1>
                <p className="text-gray-600">Gestiona todas tus pólizas de seguro desde un solo lugar</p>
              </div>
            </div>
            <Separator className="mb-4" />
          </div>
        </div>

        <div className="px-3 sm:px-4">
          <ErrorState 
            error={error instanceof Error ? error.message : "Error desconocido"} 
            onRetry={() => refetch()} 
          />
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col">
      {/* Sticky Header */}
      <div className={`sticky top-0 z-10 bg-white py-4 ${isScrolled ? "shadow-md" : ""} transition-shadow duration-200`}>
        <div className="px-3 sm:px-4">
          {/* Title with Sidebar Trigger */}
          <div className="flex items-center gap-4 mb-4">
            <SidebarTrigger className="-ml-1 hover:bg-gray-100" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Mis Pólizas</h1>
              <p className="text-gray-600">Gestiona todas tus pólizas de seguro desde un solo lugar</p>
            </div>
          </div>
          <Separator className="mb-3" />

          {/* KPI Cards */}
          <div className="mb-3">
            <div className="grid grid-cols-3 gap-2 sm:gap-4">
              <Card className="border-l-4" style={{borderLeftColor: "#000000"}}>
                <CardContent className="p-3 sm:p-4">
                  <div className="flex justify-between items-center">
                    <div className="min-w-0 flex-1 pr-2">
                      <h3 className="text-sm font-medium text-gray-500">
                        Borradores
                      </h3>
                      <p className="text-xs text-gray-500 mt-0.5">Pólizas en edición</p>
                    </div>
                    <p className="text-xl sm:text-2xl font-bold flex-shrink-0">{drafts}</p>
                  </div>
                </CardContent>
              </Card>
              <Card className="border-l-4 border-l-primary">
                <CardContent className="p-3 sm:p-4">
                  <div className="flex justify-between items-center">
                    <div className="min-w-0 flex-1 pr-2">
                      <h3 className="text-sm font-medium text-gray-500">Activas</h3>
                      <p className="text-xs text-gray-500 mt-0.5">Pólizas vigentes</p>
                    </div>
                    <p className="text-xl sm:text-2xl font-bold flex-shrink-0">{actives}</p>
                  </div>
                </CardContent>
              </Card>
              <Card className="border-l-4 border-l-red-500">
                <CardContent className="p-3 sm:p-4">
                  <div className="flex justify-between items-center">
                    <div className="min-w-0 flex-1 pr-2">
                      <h3 className="text-sm font-medium text-gray-500">
                        Renovar Pronto
                      </h3>
                      <p className="text-xs text-gray-500 mt-0.5">
                        Pólizas próximas a renovar
                      </p>
                    </div>
                    <p className="text-xl sm:text-2xl font-bold flex-shrink-0">{renewSoon}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="space-y-2">
            {/* Row 1: Search bar + Filter dropdown */}
            <div className="flex flex-col sm:flex-row gap-4 sm:items-center">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  placeholder="Buscar por número de póliza, marca, modelo, aseguradora o tomador..."
                  className="pl-10 pr-10"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
                {isSearching && (
                  <Loader2 className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
                )}
              </div>
              <div className="flex items-center gap-2">
                <Select
                  onValueChange={handleStatusFilterChange}
                  value={filterStatus}
                >
                  <SelectTrigger className="w-full sm:w-auto bg-white min-w-[180px]">
                    <SelectValue placeholder={getFilterLabel(filterStatus)} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      value="all"
                      className="focus:bg-primary focus:text-primary-foreground data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground"
                    >
                      {getFilterLabel("all")}
                    </SelectItem>
                    <SelectItem
                      value={PolicyStateEnum.DRAFT}
                      className="focus:bg-primary focus:text-primary-foreground data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground"
                    >
                      {getFilterLabel(PolicyStateEnum.DRAFT)}
                    </SelectItem>
                    <SelectItem
                      value={PolicyStateEnum.ACTIVE}
                      className="focus:bg-primary focus:text-primary-foreground data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground"
                    >
                      {getFilterLabel(PolicyStateEnum.ACTIVE)}
                    </SelectItem>
                    <SelectItem
                      value={PolicyStateEnum.RENEW_SOON}
                      className="focus:bg-primary focus:text-primary-foreground data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground"
                    >
                      {getFilterLabel(PolicyStateEnum.RENEW_SOON)}
                    </SelectItem>
                    <SelectItem
                      value={PolicyStateEnum.EXPIRED}
                      className="focus:bg-primary focus:text-primary-foreground data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground"
                    >
                      {getFilterLabel(PolicyStateEnum.EXPIRED)}
                    </SelectItem>
                  </SelectContent>
                </Select>
                {filterStatus !== "all" && (
                  <Button
                    size="icon"
                    onClick={() => handleStatusFilterChange("all")}
                    className="h-9 w-9 bg-primary text-white flex-shrink-0"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                )}
              </div>
            </div>

            {/* Row 2: Asset type buttons + Create button */}
            <div className="flex flex-col sm:flex-row gap-4 sm:items-center sm:justify-between">
              <div className="flex gap-1 sm:gap-2 flex-1 min-w-0">
                <Button
                  variant="outline"
                  className={`${getButtonClass("all")} flex-1 text-xs sm:text-sm px-2 sm:px-4`}
                  onClick={() => handleAssetTypeFilterChange("all")}
                >
                  Todas
                </Button>
                <Button
                  variant="outline"
                  className={`${getButtonClass(AssetType.CAR)} gap-1 flex-1 text-xs sm:text-sm px-2 sm:px-4`}
                  onClick={() => handleAssetTypeFilterChange(AssetType.CAR)}
                >
                  <Car className="h-3 w-3 sm:h-4 sm:w-4" /> Coche
                </Button>
                <Button
                  variant="outline"
                  className={`${getButtonClass(AssetType.MOTORCYCLE)} gap-1 flex-1 text-xs sm:text-sm px-2 sm:px-4`}
                  onClick={() => handleAssetTypeFilterChange(AssetType.MOTORCYCLE)}
                >
                  <Bike className="h-3 w-3 sm:h-4 sm:w-4" /> Moto
                </Button>
              </div>
              <div className="flex-shrink-0">
                <Link href="/account-holder/policies/new-policy">
                  <PrimaryButton className="gap-1 w-full sm:w-auto">
                    <Plus className="h-4 w-4" />
                    Crear nueva subasta
                  </PrimaryButton>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="px-3 sm:px-4 space-y-4">
        {/* Pagination Controls - Top */}
        {transformedPolicies.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {transformedPolicies.map((policy) => (
              <PolicyCard
                key={policy.id}
                policy={policy.policyData} // Pass the full policy object
                id={policy.id}
                policyNumber={policy.policyNumber}
                assetInfo={policy.assetInfo}
                insurerName={policy.insurerName}
                productName={policy.productName}
                premium={policy.premium}
                status={policy.status}
                endDate={policy.endDate}
                insuredName={policy.insuredName}
                coverageCount={policy.coverageCount}
                assetType={policy.assetType}
                baseUrl="/account-holder"
              />
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-10 text-center">
              <div className="text-gray-400 mb-4">
                <FileText className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No se encontraron pólizas
              </h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || filterStatus !== "all" || filterAssetType !== "all"
                  ? "Intenta ajustar los filtros de búsqueda."
                  : "Comienza creando tu primera póliza."}
              </p>
              {(!searchTerm && filterStatus === "all" && filterAssetType === "all") && (
                <Link href="/account-holder/policies/new-policy">
                  <PrimaryButton className="gap-1">
                    <Plus className="h-4 w-4" />
                    Crear nueva subasta
                  </PrimaryButton>
                </Link>
              )}
            </CardContent>
          </Card>
        )}

        {/* Pagination Controls - Bottom */}
        {pagination && pagination.total > 0 && (
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-4 pt-4 pb-8">
            <div className="flex items-center justify-center gap-1 sm:gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="bg-primary text-black hover:bg-primary hover:text-black text-xs sm:text-sm px-2 sm:px-3"
              >
                Anterior
              </Button>

              <span className="text-xs sm:text-sm text-gray-600 whitespace-nowrap px-2">
                Página {pagination.page} de {pagination.totalPages}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === pagination.totalPages}
                className="bg-primary text-black hover:bg-primary hover:text-black text-xs sm:text-sm px-2 sm:px-3"
              >
                Siguiente
              </Button>
            </div>

            <div className="flex items-center justify-center gap-1 sm:gap-2">
              <span className="text-xs sm:text-sm text-gray-600 whitespace-nowrap">Mostrar</span>
              <Select
                value={itemsPerPage.toString()}
                onValueChange={handleItemsPerPageChange}
              >
                <SelectTrigger className="w-16 sm:w-20 text-center text-xs sm:text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="4">4</SelectItem>
                <SelectItem value="8">8</SelectItem>
                <SelectItem value="12">12</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-xs sm:text-sm text-gray-600 whitespace-nowrap">elementos</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}