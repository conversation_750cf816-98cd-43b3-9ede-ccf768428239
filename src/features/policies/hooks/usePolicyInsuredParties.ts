"use client";

import { useQuery } from "@tanstack/react-query";
import { getPolicyInsuredParties, getAccountHolderInsuredParties } from "../actions/get-policy-insured-parties";

/**
 * Hook to fetch insured parties for a specific policy
 * This follows the correct relationship: Policy -> PolicyInsuredParty (join table) -> InsuredParty
 * Uses TanStack Query for consistent caching and state management
 */
export function usePolicyInsuredParties(policyId: string | null) {
  return useQuery({
    queryKey: ["policy-insured-parties", policyId],
    queryFn: () => policyId ? getPolicyInsuredParties(policyId) : Promise.reject(new Error("Policy ID is required")),
    enabled: !!policyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to fetch all insured parties that belong to an account holder
 * This is useful for showing available insured parties when creating/editing policies
 * Uses TanStack Query for consistent caching and state management
 */
export function useAccountHolderInsuredParties(accountHolderId: string | null) {
  return useQuery({
    queryKey: ["account-holder-insured-parties", accountHolderId],
    queryFn: () => accountHolderId ? getAccountHolderInsuredParties(accountHolderId) : Promise.reject(new Error("Account holder ID is required")),
    enabled: !!accountHolderId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
}
