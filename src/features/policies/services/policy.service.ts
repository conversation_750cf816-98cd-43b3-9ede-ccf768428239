import { db } from "@/lib/db";
import { PolicyStateEnum } from "@prisma/client";
import type { Policy } from "@prisma/client";
import { logger } from "@/lib/logger";
import { PolicyStateService } from "@/lib/services/policy-state.service";

/**
 * Policy Service - Business domain service for policy management
 * Handles policy operations across all user roles
 */
export class PolicyService {
  /**
   * Update policy state - Admin functionality
   * @param policyId - The policy ID
   * @param state - New policy state
   * @returns Updated policy
   */
  static async updateState(policyId: string, state: PolicyStateEnum) {
    logger.debug("Updating policy state", {
      policyId,
      newState: state
    });
    
    // Transition to new state using state service
    await PolicyStateService.transitionToState(policyId, state);
    
    const updatedPolicy = await db.policy.findUniqueOrThrow({
      where: { id: policyId },
      include: {
        accountHolder: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        coverages: true,
        insuredParties: true,
        document: true,
      },
    });

    logger.business("Policy state updated", {
      policyId,
      newState: state,
      accountHolderId: updatedPolicy.accountHolderId
    });

    return updatedPolicy;
  }

  /**
   * Get policy by ID with full details and current state
   * @param policyId - The policy ID
   * @returns Policy with full details and current state or null
   */
  static async getById(policyId: string): Promise<(Policy & { currentState?: PolicyStateEnum }) | null> {
    const policy = await db.policy.findUnique({
      where: { id: policyId },
      include: {
        accountHolder: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        coverages: true,
        insuredParties: true,
        document: true,
      },
    });

    if (!policy) return null;

    // Get current state from state history
    try {
      const currentState = await PolicyStateService.getCurrentState(policyId);
      return { ...policy, currentState } as Policy & { currentState: PolicyStateEnum };
    } catch {
      // If no state history exists, return policy without currentState
      return policy;
    }
  }

  /**
   * Get policies with filters - supports all user roles
   * @param filters - Query filters
   * @returns Paginated policies
   */
  static async getWithFilters(filters: {
    state?: PolicyStateEnum[];
    accountHolderId?: string;
    limit?: number;
    offset?: number;
  }) {
    const { state, accountHolderId, limit = 20, offset = 0 } = filters;

    // If filtering by state, get policy IDs from state service
    let policyIds: string[] | undefined;
    if (state && state.length > 0) {
      policyIds = [];
      for (const stateEnum of state) {
        const idsForState = await PolicyStateService.getPoliciesByCurrentState(stateEnum);
        policyIds.push(...idsForState);
      }
      // Remove duplicates
      policyIds = [...new Set(policyIds)];
      
      // If no policies match the state filter, return empty result
      if (policyIds.length === 0) {
        return {
          policies: [],
          total: 0,
          hasMore: false,
        };
      }
    }

    const where: {
      id?: { in: string[] };
      accountHolderId?: string;
    } = {};
    if (policyIds) {
      where.id = { in: policyIds };
    }
    if (accountHolderId) {
      where.accountHolderId = accountHolderId;
    }

    const [policies, total] = await Promise.all([
      db.policy.findMany({
        where,
        include: {
          accountHolder: {
            select: {
              id: true,
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          coverages: true,
          insuredParties: true,
          document: true,
        },
        orderBy: { createdAt: "desc" },
        take: limit,
        skip: offset,
      }),
      db.policy.count({ where }),
    ]);

    return {
      policies,
      total,
      hasMore: offset + policies.length < total,
    };
  }
  /**
   * Create initial state for a new policy
   * @param policyId - The policy ID
   * @param state - Initial state (defaults to DRAFT)
   */
  static async createInitialState(policyId: string, state: PolicyStateEnum = PolicyStateEnum.DRAFT) {
    return PolicyStateService.createState(policyId, state);
  }

  /**
   * Activate a policy (transition from DRAFT to ACTIVE)
   * @param policyId - The policy ID
   * @returns Updated policy
   */
  static async activate(policyId: string) {
    logger.debug("Activating policy", { policyId });
    
    await PolicyStateService.transitionToState(policyId, PolicyStateEnum.ACTIVE);
    
    const updatedPolicy = await this.getById(policyId);
    
    logger.business("Policy activated", {
      policyId,
      accountHolderId: updatedPolicy?.accountHolderId
    });
    
    return updatedPolicy;
  }

  /**
   * Mark policy as approaching renewal
   * @param policyId - The policy ID
   * @returns Updated policy
   */
  static async markForRenewal(policyId: string) {
    logger.debug("Marking policy for renewal", { policyId });
    
    await PolicyStateService.transitionToState(policyId, PolicyStateEnum.RENEW_SOON);
    
    const updatedPolicy = await this.getById(policyId);
    
    logger.business("Policy marked for renewal", {
      policyId,
      accountHolderId: updatedPolicy?.accountHolderId
    });
    
    return updatedPolicy;
  }

  /**
   * Mark policy as expired
   * @param policyId - The policy ID
   * @returns Updated policy
   */
  static async markAsExpired(policyId: string) {
    logger.debug("Marking policy as expired", { policyId });
    
    await PolicyStateService.transitionToState(policyId, PolicyStateEnum.EXPIRED);
    
    const updatedPolicy = await this.getById(policyId);
    
    logger.business("Policy marked as expired", {
      policyId,
      accountHolderId: updatedPolicy?.accountHolderId
    });
    
    return updatedPolicy;
  }

  /**
   * Reject a policy (transition from DRAFT to REJECTED)
   * @param policyId - The policy ID
   * @returns Updated policy
   */
  static async reject(policyId: string) {
    logger.debug("Rejecting policy", { policyId });
    
    await PolicyStateService.transitionToState(policyId, PolicyStateEnum.REJECTED);
    
    const updatedPolicy = await this.getById(policyId);
    
    logger.business("Policy rejected", {
      policyId,
      accountHolderId: updatedPolicy?.accountHolderId
    });
    
    return updatedPolicy;
  }

  /**
   * Get current state of a policy
   * @param policyId - The policy ID
   * @returns Current policy state
   */
  static async getCurrentState(policyId: string): Promise<PolicyStateEnum> {
    return PolicyStateService.getCurrentState(policyId);
  }

  /**
   * Get state history of a policy
   * @param policyId - The policy ID
   * @returns State history
   */
  static async getStateHistory(policyId: string) {
    return PolicyStateService.getStateHistory(policyId);
  }

  /**
   * Get policies by current state
   * @param state - The policy state to filter by
   * @returns Policies in the specified state
   */
  static async getByCurrentState(state: PolicyStateEnum) {
    const policyIds = await PolicyStateService.getPoliciesByCurrentState(state);
    
    if (policyIds.length === 0) {
      return [];
    }
    
    return db.policy.findMany({
      where: {
        id: { in: policyIds }
      },
      include: {
        accountHolder: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        coverages: true,
        insuredParties: true,
        document: true,
      },
      orderBy: { createdAt: "desc" },
    });
  }

  /**
   * Get policies approaching renewal
   * @param daysBeforeExpiration - Days before expiration to consider (default: 30)
   * @returns Policies that should be marked for renewal
   */
  static async getPoliciesApproachingRenewal(daysBeforeExpiration: number = 30) {
    const policyIds = await PolicyStateService.getPoliciesApproachingRenewal(daysBeforeExpiration);
    
    if (policyIds.length === 0) {
      return [];
    }
    
    return db.policy.findMany({
      where: {
        id: { in: policyIds }
      },
      include: {
        accountHolder: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        coverages: true,
        insuredParties: true,
        document: true,
      },
      orderBy: { endDate: "asc" },
    });
  }
}

// Export the admin functionality for backward compatibility
export const AdminPolicyService = {
  updateState: PolicyService.updateState,
  getById: PolicyService.getById,
  activate: PolicyService.activate,
  reject: PolicyService.reject,
  // Legacy method name for backward compatibility
  updateStatus: PolicyService.updateState,
};