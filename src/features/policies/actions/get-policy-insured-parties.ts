"use server";

import { PolicyInsuredPartiesService, type PolicyInsuredPartyData } from "../services/policy-insured-parties.service";
import { logger } from "@/lib/logger";

/**
 * Server action to get all insured parties associated with a specific policy
 * This follows the correct relationship: Policy -> PolicyInsuredParty (join table) -> InsuredParty
 */
export async function getPolicyInsuredParties(policyId: string): Promise<PolicyInsuredPartyData[]> {
  try {
    if (!policyId) {
      logger.error("Policy ID is required", new Error("Missing policy ID"), { function: "getPolicyInsuredParties" });
      return [];
    }

    const insuredParties = await PolicyInsuredPartiesService.getInsuredPartiesForPolicy(policyId);
    return insuredParties;
  } catch (error) {
    logger.error("Error in getPolicyInsuredParties", error, { policyId });
    return [];
  }
}

/**
 * Server action to get all insured parties that belong to an account holder
 * This is useful for showing available insured parties when creating/editing policies
 */
export async function getAccountHolderInsuredParties(accountHolderId: string): Promise<PolicyInsuredPartyData[]> {
  try {
    if (!accountHolderId) {
      logger.error("Account Holder ID is required", new Error("Missing account holder ID"), { function: "getAccountHolderInsuredParties" });
      return [];
    }

    const insuredParties = await PolicyInsuredPartiesService.getInsuredPartiesForAccountHolder(accountHolderId);
    return insuredParties;
  } catch (error) {
    logger.error("Error in getAccountHolderInsuredParties", error, { accountHolderId });
    return [];
  }
}

/**
 * Server action to associate an existing insured party with a policy
 */
export async function associateInsuredPartyWithPolicy(policyId: string, insuredPartyId: string): Promise<{ success: boolean; error?: string }> {
  try {
    if (!policyId || !insuredPartyId) {
      return { success: false, error: "Policy ID and Insured Party ID are required" };
    }

    const success = await PolicyInsuredPartiesService.associateInsuredPartyWithPolicy(policyId, insuredPartyId);
    
    if (success) {
      return { success: true };
    } else {
      return { success: false, error: "Failed to associate insured party with policy" };
    }
  } catch (error) {
    logger.error("Error in associateInsuredPartyWithPolicy", error, { policyId, insuredPartyId });
    return { success: false, error: "An error occurred while associating insured party with policy" };
  }
}

/**
 * Server action to remove association between an insured party and a policy
 */
export async function removeInsuredPartyFromPolicy(policyId: string, insuredPartyId: string): Promise<{ success: boolean; error?: string }> {
  try {
    if (!policyId || !insuredPartyId) {
      return { success: false, error: "Policy ID and Insured Party ID are required" };
    }

    const success = await PolicyInsuredPartiesService.removeInsuredPartyFromPolicy(policyId, insuredPartyId);
    
    if (success) {
      return { success: true };
    } else {
      return { success: false, error: "Failed to remove insured party from policy" };
    }
  } catch (error) {
    logger.error("Error in removeInsuredPartyFromPolicy", error, { policyId, insuredPartyId });
    return { success: false, error: "An error occurred while removing insured party from policy" };
  }
}
