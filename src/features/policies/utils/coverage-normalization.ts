import { Coverage, BidCoverage, GuaranteeType } from "@prisma/client";
import { formatCurrency } from "@/lib/utils";
import { translateGuaranteeType, CoverageGroup } from "@/features/policies/utils/translations";
import { logger } from "@/lib/logger";

// Types
export interface CoverageComparisonResult {
  guaranteeType: GuaranteeType;
  present: boolean;
  title: string;
  limit: string;
  description: string;
  // Enhanced fields for detailed comparison
  deductible?: string;
  policyPresent?: boolean;
  bidPresent?: boolean;
  comparisonType?: "better" | "worse" | "same" | "missing" | "new";
  differences?: string[];
}

// Utility functions
export function isBidCoverageDataAvailable(bidCoverages: BidCoverage[]): boolean {
  return bidCoverages && bidCoverages.length > 0;
}

export function compareCoverages(
  policyCoverages: Coverage[],
  bidCoverages: BidCoverage[]
): CoverageComparisonResult[] {
  const results: CoverageComparisonResult[] = [];

  // Get all unique guarantee types from both policy and bid coverages
  const allGuaranteeTypes = new Set([
    ...policyCoverages.map(c => c.type),
    ...bidCoverages.map(c => c.type)
  ]);

  allGuaranteeTypes.forEach(guaranteeType => {
    const policyCoverage = policyCoverages.find(c => c.type === guaranteeType);
    const bidCoverage = bidCoverages.find(c => c.type === guaranteeType);

    // Determine comparison type and differences
    const comparisonResult = calculateCoverageDifference(policyCoverage ?? null, bidCoverage ?? null);
    const differences: string[] = [];

    // Add specific differences based on coverage fields
    if (policyCoverage && bidCoverage) {
      // Compare limits
      if (bidCoverage.limitIsUnlimited && !policyCoverage.limitIsUnlimited) {
        differences.push("Límite mejorado a ilimitado");
      } else if (!bidCoverage.limitIsUnlimited && policyCoverage.limitIsUnlimited) {
        differences.push("Límite reducido desde ilimitado");
      } else if (bidCoverage.limit && policyCoverage.limit) {
        const bidLimit = Number(bidCoverage.limit);
        const policyLimit = Number(policyCoverage.limit);
        if (bidLimit > policyLimit) {
          differences.push(`Límite aumentado en ${formatCurrency(bidLimit - policyLimit)}`);
        } else if (bidLimit < policyLimit) {
          differences.push(`Límite reducido en ${formatCurrency(policyLimit - bidLimit)}`);
        }
      }

      // Compare deductibles
      if (bidCoverage.deductible && policyCoverage.deductible) {
        const bidDeductible = Number(bidCoverage.deductible);
        const policyDeductible = Number(policyCoverage.deductible);
        if (bidDeductible < policyDeductible) {
          differences.push(`Franquicia reducida en ${formatCurrency(policyDeductible - bidDeductible)}`);
        } else if (bidDeductible > policyDeductible) {
          differences.push(`Franquicia aumentada en ${formatCurrency(bidDeductible - policyDeductible)}`);
        }
      }

      // Compare per-day limits
      if (bidCoverage.limitPerDay && policyCoverage.limitPerDay) {
        const bidPerDay = Number(bidCoverage.limitPerDay);
        const policyPerDay = Number(policyCoverage.limitPerDay);
        if (bidPerDay > policyPerDay) {
          differences.push(`Límite diario aumentado en ${formatCurrency(bidPerDay - policyPerDay)}`);
        } else if (bidPerDay < policyPerDay) {
          differences.push(`Límite diario reducido en ${formatCurrency(policyPerDay - bidPerDay)}`);
        }
      }
    }

    results.push({
      guaranteeType,
      present: !!bidCoverage,
      policyPresent: !!policyCoverage,
      bidPresent: !!bidCoverage,
      title: guaranteeType === "OTHER" && (policyCoverage?.customName || bidCoverage?.customName)
        ? (policyCoverage?.customName ?? bidCoverage?.customName ?? translateGuaranteeType(guaranteeType))
        : translateGuaranteeType(guaranteeType),
      limit: bidCoverage ? formatCoverageLimit(bidCoverage) : (policyCoverage ? formatCoverageLimit(policyCoverage) : "No disponible"),
      deductible: bidCoverage ? formatCoverageDeductible(bidCoverage) : (policyCoverage ? formatCoverageDeductible(policyCoverage) : "No aplica"),
      description: bidCoverage?.description ?? policyCoverage?.description ?? "",
      comparisonType: comparisonResult.type,
      differences: differences.length > 0 ? differences : [comparisonResult.notes]
    });
  });

  return results;
}

export function getKeyCoverageDifferences(
  policyCoverages: Coverage[],
  bidCoverages: BidCoverage[]
): string[] {
  const differences: string[] = [];
  
  // Check for missing mandatory coverages
  const mandatoryCoverages = [GuaranteeType.MANDATORY_LIABILITY];
  mandatoryCoverages.forEach(guaranteeType => {
    const hasPolicyCoverage = policyCoverages.some(c => c.type === guaranteeType);
    const hasBidCoverage = bidCoverages.some(c => c.type === guaranteeType);
    
    if (hasPolicyCoverage && !hasBidCoverage) {
      differences.push(`Falta cobertura obligatoria: ${translateGuaranteeType(guaranteeType)}`);
    }
  });
  
  return differences;
}

export function formatCoverageLimit(coverage: Coverage | BidCoverage): string {
  if (coverage.limitIsUnlimited) {
    return "Ilimitado";
  }

  if (coverage.limitIsFullCost) {
    return "Coste completo";
  }

  // Handle per-day limits with maximum days or months
  if (coverage.limitPerDay) {
    const perDayAmount = formatCurrency(Number(coverage.limitPerDay));

    if (coverage.limitMaxDays && coverage.limitMaxMonths) {
      // Both days and months specified
      const totalDays = coverage.limitMaxDays;
      const totalAmount = Number(coverage.limitPerDay) * totalDays;
      return `${perDayAmount}/día (máx. ${coverage.limitMaxDays} días / ${coverage.limitMaxMonths} meses - Total: ${formatCurrency(totalAmount)})`;
    } else if (coverage.limitMaxDays) {
      // Only days specified
      const totalAmount = Number(coverage.limitPerDay) * coverage.limitMaxDays;
      return `${perDayAmount}/día (máx. ${coverage.limitMaxDays} días - Total: ${formatCurrency(totalAmount)})`;
    } else if (coverage.limitMaxMonths) {
      // Only months specified
      return `${perDayAmount}/día (máx. ${coverage.limitMaxMonths} meses)`;
    } else {
      // Only per-day amount specified
      return `${perDayAmount}/día`;
    }
  }

  // Handle liability-specific caps (both bodily and property)
  if (coverage.liabilityBodilyCap && coverage.liabilityPropertyCap) {
    return `Personas: ${formatCurrency(Number(coverage.liabilityBodilyCap))} | Daños: ${formatCurrency(Number(coverage.liabilityPropertyCap))}`;
  }

  // Handle individual liability caps
  if (coverage.liabilityBodilyCap) {
    return `Personas: ${formatCurrency(Number(coverage.liabilityBodilyCap))}`;
  }

  if (coverage.liabilityPropertyCap) {
    return `Daños: ${formatCurrency(Number(coverage.liabilityPropertyCap))}`;
  }

  // Handle standard limit
  if (coverage.limit) {
    return formatCurrency(Number(coverage.limit));
  }

  return "No especificado";
}

/**
 * Format deductible information for display
 */
export function formatCoverageDeductible(coverage: Coverage | BidCoverage): string {
  if (!coverage.deductible && !coverage.deductiblePercent) {
    return "No aplica";
  }

  if (coverage.deductiblePercent && coverage.deductible) {
    // Both percentage and fixed amount
    const percentValue = Number(coverage.deductiblePercent) * 100; // Convert 0.15 to 15
    return `${percentValue}% (mín. ${formatCurrency(Number(coverage.deductible))})`;
  }

  if (coverage.deductiblePercent) {
    // Only percentage
    const percentValue = Number(coverage.deductiblePercent) * 100;
    return `${percentValue}%`;
  }

  if (coverage.deductible) {
    // Only fixed amount
    return formatCurrency(Number(coverage.deductible));
  }

  return "No aplica";
}

export function calculateCoverageDifference(
  policyCoverage: Coverage | null,
  bidCoverage: BidCoverage | null
): { type: "better" | "worse" | "same" | "missing"; notes: string } {
  if (!bidCoverage) {
    return { type: "missing", notes: "Cobertura no incluida" };
  }
  
  if (!policyCoverage) {
    return { type: "better", notes: "Nueva cobertura añadida" };
  }
  
  // Compare limits
  if (bidCoverage.limitIsUnlimited && !policyCoverage.limitIsUnlimited) {
    return { type: "better", notes: "Límite mejorado a ilimitado" };
  }
  
  if (!bidCoverage.limitIsUnlimited && policyCoverage.limitIsUnlimited) {
    return { type: "worse", notes: "Límite reducido desde ilimitado" };
  }
  
  if (bidCoverage.limit && policyCoverage.limit) {
    if (Number(bidCoverage.limit) > Number(policyCoverage.limit)) {
      return { type: "better", notes: "Límite aumentado" };
    } else if (Number(bidCoverage.limit) < Number(policyCoverage.limit)) {
      return { type: "worse", notes: "Límite reducido" };
    }
  }
  
  return { type: "same", notes: "Sin cambios significativos" };
}

// Re-export centralized translation function for backward compatibility
export { translateGuaranteeType };

// React components moved to @/components/shared/ComparisonIndicator.tsx

// ──────────────────────────────────────────────────────────────────────────
//  COVERAGE GROUPING FUNCTIONALITY
// ──────────────────────────────────────────────────────────────────────────

/**
 * Mapping of GuaranteeType to CoverageGroup based on PRD specifications
 */
export const guaranteeTypeToCoverageGroup: Record<GuaranteeType, CoverageGroup> = {
  // 1. Civil Liability (RC)
  MANDATORY_LIABILITY: CoverageGroup.CIVIL_LIABILITY,
  VOLUNTARY_LIABILITY: CoverageGroup.CIVIL_LIABILITY,
  LIABILITY_AT_REST: CoverageGroup.CIVIL_LIABILITY,
  TRAILER_LIABILITY: CoverageGroup.CIVIL_LIABILITY,
  LOAD_LIABILITY: CoverageGroup.CIVIL_LIABILITY,
  CARGO_LIABILITY: CoverageGroup.CIVIL_LIABILITY,

  // 2. Legal Defense & Management
  LEGAL_DEFENSE: CoverageGroup.LEGAL_DEFENSE_MANAGEMENT,
  LEGAL_REPRESENTATION_EXTENSION: CoverageGroup.LEGAL_DEFENSE_MANAGEMENT,
  FINES_MANAGEMENT: CoverageGroup.LEGAL_DEFENSE_MANAGEMENT,
  LICENSE_SUSPENSION: CoverageGroup.LEGAL_DEFENSE_MANAGEMENT,
  LICENSE_SUSPENSION_SUBSIDY: CoverageGroup.LEGAL_DEFENSE_MANAGEMENT,
  THIRD_PARTY_INSOLVENCY: CoverageGroup.LEGAL_DEFENSE_MANAGEMENT,

  // 3. Damage to the Insured Vehicle
  VEHICLE_DAMAGE: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  FIRE: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  THEFT: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  GLASS_BREAKAGE: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  WEATHER_DAMAGE: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  COLLISION_WITH_ANIMALS: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  TYRE_DAMAGE: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  NON_STANDARD_ACCESSORIES: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  CHARGING_CABLE: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  ERROR_REFUELING: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  UNAUTHORIZED_USE: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  TOTAL_LOSS_DAMAGE: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  TOTAL_LOSS_FIRE: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
  TOTAL_LOSS_THEFT: CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,

  // 4. Travel Assistance
  TRAVEL_ASSISTANCE: CoverageGroup.TRAVEL_ASSISTANCE,
  TOWING_FROM_KM0: CoverageGroup.TRAVEL_ASSISTANCE,
  RESCUE_EXPENSES: CoverageGroup.TRAVEL_ASSISTANCE,
  HOTEL_EXPENSES_TRAVEL_ASSIST: CoverageGroup.TRAVEL_ASSISTANCE,
  REPATRIATION: CoverageGroup.TRAVEL_ASSISTANCE, // Note: Can float between groups per PRD
  IMMOBILIZATION: CoverageGroup.TRAVEL_ASSISTANCE, // Note: Can float between groups per PRD

  // 5. Personal Accidents
  DRIVER_ACCIDENTS: CoverageGroup.PERSONAL_ACCIDENTS,
  PASSENGER_ACCIDENTS: CoverageGroup.PERSONAL_ACCIDENTS,
  PSYCHOLOGICAL_ASSISTANCE: CoverageGroup.PERSONAL_ACCIDENTS,
  PET_INJURY: CoverageGroup.PERSONAL_ACCIDENTS,
  EXTRAORDINARY_RISKS_PERSONS: CoverageGroup.PERSONAL_ACCIDENTS,

  // 6. Replacement Vehicle / Immobilization
  VEHICLE_REPLACEMENT: CoverageGroup.REPLACEMENT_VEHICLE_IMMOBILIZATION,
  PARALYZATION_COMPENSATION: CoverageGroup.REPLACEMENT_VEHICLE_IMMOBILIZATION,
  HANDYMAN_SERVICE: CoverageGroup.REPLACEMENT_VEHICLE_IMMOBILIZATION,

  // 7. Valuation & Compensation
  NEW_VALUE_COMPENSATION: CoverageGroup.VALUATION_COMPENSATION,
  GAP_COVERAGE: CoverageGroup.VALUATION_COMPENSATION,
  ADVANCE_COMPENSATION: CoverageGroup.VALUATION_COMPENSATION,
  EXTRAORDINARY_RISKS_VEHICLE: CoverageGroup.VALUATION_COMPENSATION,

  // 8. Complementary Services
  FREE_WORKSHOP_CHOICE: CoverageGroup.COMPLEMENTARY_SERVICES,
  PERSONAL_BELONGINGS: CoverageGroup.COMPLEMENTARY_SERVICES,
  ASSISTIVE_EQUIPMENT_RENTAL: CoverageGroup.COMPLEMENTARY_SERVICES,
  LOST_KEYS: CoverageGroup.COMPLEMENTARY_SERVICES,
  MOTORCYCLE_GEAR: CoverageGroup.COMPLEMENTARY_SERVICES,
  OTHER: CoverageGroup.COMPLEMENTARY_SERVICES, // Always defaults to Complementary Services per PRD
};

/**
 * Get the coverage group for a given guarantee type
 */
export function getCoverageGroup(guaranteeType: GuaranteeType): CoverageGroup {
  const group = guaranteeTypeToCoverageGroup[guaranteeType];
  if (!group) {
    logger.warn("Missing coverage group mapping for guarantee type", { guaranteeType });
    return CoverageGroup.COMPLEMENTARY_SERVICES; // Default fallback
  }
  return group;
}

/**
 * Group an array of coverage items by their coverage group
 */
export function groupCoveragesByGroup<T extends { type: GuaranteeType }>(
  coverages: T[]
): Record<CoverageGroup, T[]> {
  const grouped = {} as Record<CoverageGroup, T[]>;

  // Initialize all groups with empty arrays
  Object.values(CoverageGroup).forEach(group => {
    grouped[group] = [];
  });

  // Group coverages by their coverage group
  coverages.forEach(coverage => {
    // Skip coverages with invalid or missing type
    if (!coverage || !coverage.type) {
      logger.warn("Skipping coverage with missing type", { coverage });
      return;
    }
    
    const group = getCoverageGroup(coverage.type);
    if (grouped[group]) {
      grouped[group].push(coverage);
    } else {
      logger.error("Invalid coverage group for coverage type", { group, coverageType: coverage.type });
    }
  });

  return grouped;
}

/**
 * Get coverage groups in display order (as defined in PRD)
 */
export function getCoverageGroupsInOrder(): CoverageGroup[] {
  return [
    CoverageGroup.CIVIL_LIABILITY,
    CoverageGroup.LEGAL_DEFENSE_MANAGEMENT,
    CoverageGroup.DAMAGE_TO_INSURED_VEHICLE,
    CoverageGroup.TRAVEL_ASSISTANCE,
    CoverageGroup.PERSONAL_ACCIDENTS,
    CoverageGroup.REPLACEMENT_VEHICLE_IMMOBILIZATION,
    CoverageGroup.VALUATION_COMPENSATION,
    CoverageGroup.COMPLEMENTARY_SERVICES
  ];
}