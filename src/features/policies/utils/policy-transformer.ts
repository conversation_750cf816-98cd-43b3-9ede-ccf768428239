import { PolicyStateEnum, AssetType } from "@prisma/client";
import { formatInsurerCompany } from "@/lib/format-insurer";

/**
 * Shared policy transformation utilities
 * Eliminates duplicate transformation logic across components and roles
 */

/**
 * Generic policy data interface that works across all roles
 * Avoids coupling to role-specific interfaces
 */
export interface GenericPolicyData {
  id: string;
  policyNumber: string | null;
  status: PolicyStateEnum | null;
  premium: number | null;
  startDate: Date | null;
  endDate: Date | null;
  productName: string | null;
  insurerCompany: string | null;
  asset: {
    id: string;
    assetType: string;
    description: string | null;
    vehicleDetails: {
      licensePlate: string | null;
      firstRegistrationDate: Date | null;
      brand: string | null;
      model: string | null;
      version: string | null;
      year: number | null;
      fuelType: string | null;
      chassisNumber: string | null;
      powerCv: number | null;
      seats: number | null;
      usageType: string | null;
      garageType: string | null;
      kmPerYear: string | null;
      isLeased: boolean | null;
    } | null;
  } | null;
  coverages: Array<{
    id: string;
    title: string;
    type: string;
    guaranteeType: string;
    customName: string | null;
    limit: number | null;
    deductible: number | null;
    description: string | null;
    // Enhanced Coverage model fields
    limitIsUnlimited?: boolean;
    limitIsFullCost?: boolean;
    limitPerDay?: number | null;
    limitMaxDays?: number | null;
    limitMaxMonths?: number | null;
    liabilityBodilyCap?: number | null;
    liabilityPropertyCap?: number | null;
    deductiblePercent?: number | null;
  }>;
  document: {
    id: string;
    fileName: string | null;
    fileSize: number | null;
    mimeType: string | null;
    url: string;
    uploadedAt: string;
  } | null;
  insuredParties: Array<{
    id: string;
    role: string;
    fullName: string;
  }>;
  accountHolder: {
    firstName: string;
    lastName: string;
  } | null;
}

/**
 * Transforms asset information into display string
 * Centralized logic for consistent asset display across all components
 */
export function transformAssetInfo(asset: GenericPolicyData["asset"]): string {
  if (!asset) {
    return "Sin información del activo";
  }

  if (asset.vehicleDetails) {
    const brand = asset.vehicleDetails.brand ?? "Sin marca";
    const model = asset.vehicleDetails.model ?? "Sin modelo";
    const year = asset.vehicleDetails.year ?? "Sin año";
    return `${brand} ${model} (${year})`;
  }

  return asset.description ?? "Sin información del activo";
}

/**
 * Transforms insured party information into display name
 * Centralized logic for consistent insured party display
 */
export function transformInsuredName(
  insuredParties: GenericPolicyData["insuredParties"],
  _accountHolder: GenericPolicyData["accountHolder"]
): string {
  if (insuredParties && insuredParties.length > 0) {
    // Find POLICYHOLDER first, then fallback to first party
    const policyholder = insuredParties.find(party => party.role === "POLICYHOLDER");
    if (policyholder?.fullName) {
      return policyholder.fullName;
    }
    
    const firstParty = insuredParties[0];
    if (firstParty?.fullName) {
      return firstParty.fullName;
    }
  }

  // Always return "Sin asegurado" instead of falling back to account holder
  // This maintains consistency and avoids data leakage
  return "Sin asegurado";
}

/**
 * Transforms policy data for display components
 * Centralized transformation logic that can be used across all roles
 */
export function transformPolicyForDisplay(
  policy: GenericPolicyData,
  baseUrl: string = "/account-holder"
) {
  return {
    id: policy.id,
    policyNumber: policy.policyNumber ?? "Sin número",
    assetInfo: transformAssetInfo(policy.asset),
    insurerName: policy.insurerCompany 
      ? formatInsurerCompany(policy.insurerCompany) 
      : "Sin aseguradora",
    productName: policy.productName ?? "Sin producto",
    premium: policy.premium ?? 0,
    status: policy.status ?? PolicyStateEnum.DRAFT,
    endDate: policy.endDate ? new Date(policy.endDate) : null,
    insuredName: transformInsuredName(policy.insuredParties, policy.accountHolder),
    coverageCount: policy.coverages ? policy.coverages.length : 0,
    assetType: (policy.asset?.assetType ?? "CAR") as AssetType,
    baseUrl,
    // Include original policy for components that need it
    policyData: policy,
  };
}

/**
 * Batch transform policies for list display
 * Optimized for list components across all roles
 */
export function transformPoliciesForDisplay(
  policies: GenericPolicyData[],
  baseUrl: string = "/account-holder"
) {
  return policies.map(policy => transformPolicyForDisplay(policy, baseUrl));
}
