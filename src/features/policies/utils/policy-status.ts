import { PolicyStateEnum } from "@prisma/client";
import { formatDate } from "@/lib/utils";


/**
 * Get subtitle text based on policy state
 */
export const getSubtitleByStatus = (status: PolicyStateEnum, endDate?: Date | string) => {
  switch (status) {
    case PolicyStateEnum.ACTIVE:
      return "Póliza vigente";
    case PolicyStateEnum.DRAFT:
      return "Completa los datos pendientes";
    case PolicyStateEnum.RENEW_SOON:
      return endDate ? `Renovar antes del ${formatDate(endDate)}` : "Renovar Pronto";
    case PolicyStateEnum.EXPIRED:
      return "Cobertura caducada";
    case PolicyStateEnum.REJECTED:
      return "Documento rechazado";
    default:
      return "Póliza vigente";
  }
};

/**
 * Get filter label with count
 */
export const getFilterLabel = (
  status: PolicyStateEnum | "all" | "attention" | "renew_soon" | "expired",
  policies: { currentState?: PolicyStateEnum }[]
) => {
  switch (status) {
    case "all":
      return `Todas las pólizas (${policies.length})`;
    case PolicyStateEnum.ACTIVE:
      return `Activas (${policies.filter(p => p.currentState === PolicyStateEnum.ACTIVE).length})`;
    case PolicyStateEnum.DRAFT:
      return `Borradores (${policies.filter(p => p.currentState === PolicyStateEnum.DRAFT).length})`;
    case PolicyStateEnum.RENEW_SOON:
      return `Renovar Pronto (${policies.filter(p => p.currentState === PolicyStateEnum.RENEW_SOON).length})`;
    case PolicyStateEnum.EXPIRED:
      return `Expiradas (${policies.filter(p => p.currentState === PolicyStateEnum.EXPIRED).length})`;
    case PolicyStateEnum.REJECTED:
      return `Rechazadas (${policies.filter(p => p.currentState === PolicyStateEnum.REJECTED).length})`;
    case "attention":
      const needsAttention = policies.filter(p => 
        p.currentState === PolicyStateEnum.RENEW_SOON || p.currentState === PolicyStateEnum.EXPIRED
      ).length;
      return `Requieren Atención (${needsAttention})`;
    default:
      return `Todas las pólizas (${policies.length})`;
  }
};

/**
 * Get policy state display name
 */
export const getStatusDisplayName = (status: PolicyStateEnum) => {
  switch (status) {
    case PolicyStateEnum.ACTIVE:
      return "Activa";
    case PolicyStateEnum.DRAFT:
      return "Borrador";
    case PolicyStateEnum.RENEW_SOON:
      return "Renovar Pronto";
    case PolicyStateEnum.EXPIRED:
      return "Expirada";
    case PolicyStateEnum.REJECTED:
      return "Rechazada";
    default:
      return status;
  }
};

/**
 * Check if policy state indicates expiration
 */
export const isExpiredStatus = (status: PolicyStateEnum) => {
  return status === PolicyStateEnum.EXPIRED;
};

/**
 * Check if policy state indicates renewal needed
 */
export const isRenewSoonStatus = (status: PolicyStateEnum) => {
  return status === PolicyStateEnum.RENEW_SOON;
};

/**
 * Check if policy state indicates draft
 */
export const isDraftStatus = (status: PolicyStateEnum) => {
  return status === PolicyStateEnum.DRAFT;
};

/**
 * Check if policy state indicates rejection
 */
export const isRejectedStatus = (status: PolicyStateEnum) => {
  return status === PolicyStateEnum.REJECTED;
};