import { PartyRole, Gender, FuelType, UsageType, GarageType, KmRange, GuaranteeType } from "@prisma/client";

export const partyRoleTranslations: Record<PartyRole, string> = {
  POLICYHOLDER: "Tomador",
  OWNER: "Propietario",
  MAIN_DRIVER: "Conductor Principal",
  ADDITIONAL_DRIVER: "Conductor Adicional",
};

export const translatePartyRole = (role: PartyRole): string => {
  return partyRoleTranslations[role] || role;
};

export const genderTranslations: Record<Gender, string> = {
  FEMALE: "Femenino",
  MALE: "Masculino",
};

export const translateGender = (gender: Gender): string => {
  return genderTranslations[gender] || gender;
};

// Vehicle enum translations
export const fuelTypeTranslations: Record<FuelType, string> = {
  GASOLINE: "Gasolina",
  DIESEL: "Diésel",
  ELECTRIC: "Eléctrico",
  HYBRID: "Híbrido",
};

export const translateFuelType = (fuelType: FuelType | string): string => {
  return fuelTypeTranslations[fuelType as FuelType] || fuelType;
};

export const usageTypeTranslations: Record<UsageType, string> = {
  PRIVATE_OCCASIONAL: "Particular Ocasional",
  PRIVATE_REGULAR: "Particular Regular",
  PROFESSIONAL_OCCASIONAL: "Profesional Ocasional",
  PROFESSIONAL_REGULAR: "Profesional Regular",
};

export const translateUsageType = (usageType: UsageType | string): string => {
  return usageTypeTranslations[usageType as UsageType] || usageType;
};

export const garageTypeTranslations: Record<GarageType, string> = {
  STREET: "Calle",
  SHARED_UNGUARDED: "Garaje Compartido Sin Vigilancia",
  SHARED_GUARDED: "Garaje Compartido Con Vigilancia",
  PRIVATE: "Garaje Privado",
};

export const translateGarageType = (garageType: GarageType | string): string => {
  return garageTypeTranslations[garageType as GarageType] || garageType;
};

export const kmRangeTranslations: Record<KmRange, string> = {
  UP_TO_2000: "Hasta 2.000 km",
  FROM_2000_TO_4000: "2.000 - 4.000 km",
  FROM_4000_TO_6000: "4.000 - 6.000 km",
  FROM_6000_TO_8000: "6.000 - 8.000 km",
  FROM_8000_TO_10000: "8.000 - 10.000 km",
  FROM_10000_TO_12000: "10.000 - 12.000 km",
  FROM_12000_TO_14000: "12.000 - 14.000 km",
  FROM_14000_TO_16000: "14.000 - 16.000 km",
  FROM_16000_TO_18000: "16.000 - 18.000 km",
  FROM_18000_TO_20000: "18.000 - 20.000 km",
  FROM_20000_TO_22000: "20.000 - 22.000 km",
  FROM_22000_TO_24000: "22.000 - 24.000 km",
  FROM_24000_TO_26000: "24.000 - 26.000 km",
  FROM_26000_TO_28000: "26.000 - 28.000 km",
  FROM_28000_TO_30000: "28.000 - 30.000 km",
  FROM_30000_TO_32000: "30.000 - 32.000 km",
  FROM_32000_TO_34000: "32.000 - 34.000 km",
  FROM_34000_TO_36000: "34.000 - 36.000 km",
  FROM_36000_TO_38000: "36.000 - 38.000 km",
  FROM_38000_TO_40000: "38.000 - 40.000 km",
  FROM_40000_TO_45000: "40.000 - 45.000 km",
  FROM_45000_TO_50000: "45.000 - 50.000 km",
  OVER_50000: "Más de 50.000 km",
};

export const translateKmRange = (kmRange: KmRange | string): string => {
  return kmRangeTranslations[kmRange as KmRange] || kmRange;
};

// Guarantee type translations
export const guaranteeTypeTranslations: Record<GuaranteeType, string> = {
  OTHER: "Otra",
  ADVANCE_COMPENSATION: "Adelanto de Indemnización",
  ASSISTIVE_EQUIPMENT_RENTAL: "Alquiler de Equipamiento Asistencial",
  COLLISION_WITH_ANIMALS: "Colisión con Animales Cinegéticos",
  DRIVER_ACCIDENTS: "Accidentes del Conductor",
  EXTRAORDINARY_RISKS_PERSONS: "Riesgos Extraordinarios (Personas)",
  EXTRAORDINARY_RISKS_VEHICLE: "Riesgos Extraordinarios (Vehículo)",
  FINES_MANAGEMENT: "Gestión de Multas",
  FIRE: "Incendio",
  GLASS_BREAKAGE: "Rotura de Lunas",
  HANDYMAN_SERVICE: "Servicio de Manitas",
  IMMOBILIZATION: "Inmovilización del Vehículo",
  LEGAL_DEFENSE: "Defensa Jurídica",
  LEGAL_REPRESENTATION_EXTENSION: "Ampliación de Defensa Jurídica",
  LICENSE_SUSPENSION: "Retirada de Carnet",
  LICENSE_SUSPENSION_SUBSIDY: "Subsidio por Retirada de Carnet",
  LOAD_LIABILITY: "Responsabilidad Civil de Carga",
  LOST_KEYS: "Pérdida de Llaves",
  MANDATORY_LIABILITY: "Responsabilidad Civil Obligatoria",
  PERSONAL_BELONGINGS: "Efectos Personales",
  PSYCHOLOGICAL_ASSISTANCE: "Asistencia Psicológica",
  REPATRIATION: "Repatriación",
  THEFT: "Robo",
  TOTAL_LOSS_DAMAGE: "Pérdida Total por Daños",
  TOTAL_LOSS_FIRE: "Pérdida Total por Incendio",
  TOTAL_LOSS_THEFT: "Pérdida Total por Robo",
  TOWING_FROM_KM0: "Remolque desde Km 0",
  TRAVEL_ASSISTANCE: "Asistencia en Viaje",
  VEHICLE_DAMAGE: "Daños al Vehículo",
  VEHICLE_REPLACEMENT: "Vehículo de Sustitución",
  VOLUNTARY_LIABILITY: "Responsabilidad Civil Voluntaria",
  WEATHER_DAMAGE: "Daños por Fenómenos Atmosféricos",
  TYRE_DAMAGE: "Daños en Neumáticos",
  MOTORCYCLE_GEAR: "Equipamiento de Motocicleta",
  NON_STANDARD_ACCESSORIES: "Accesorios No Estándar",
  PET_INJURY: "Lesiones a Mascotas",
  PASSENGER_ACCIDENTS: "Accidentes de Pasajeros",
  GAP_COVERAGE: "Cobertura GAP",
  PARALYZATION_COMPENSATION: "Compensación por Paralización",
  CARGO_LIABILITY: "Responsabilidad Civil de Carga",
  UNAUTHORIZED_USE: "Uso No Autorizado",
  ERROR_REFUELING: "Error de Repostaje",
  CHARGING_CABLE: "Cable de Carga",
  LIABILITY_AT_REST: "Responsabilidad Civil en Reposo",
  TRAILER_LIABILITY: "Responsabilidad Civil del Remolque",
  THIRD_PARTY_INSOLVENCY: "Insolvencia de Terceros",
  FREE_WORKSHOP_CHOICE: "Libre Elección de Taller",
  NEW_VALUE_COMPENSATION: "Compensación a Valor de Nuevo",
  HOTEL_EXPENSES_TRAVEL_ASSIST: "Gastos de Hotel (Asistencia en Viaje)",
  RESCUE_EXPENSES: "Gastos de Rescate",
};

export const translateGuaranteeType = (guaranteeType: GuaranteeType | string): string => {
  return guaranteeTypeTranslations[guaranteeType as GuaranteeType] || guaranteeType;
};

// Coverage group translations for the new grouped coverage display
export enum CoverageGroup {
  CIVIL_LIABILITY = "CIVIL_LIABILITY",
  LEGAL_DEFENSE_MANAGEMENT = "LEGAL_DEFENSE_MANAGEMENT",
  DAMAGE_TO_INSURED_VEHICLE = "DAMAGE_TO_INSURED_VEHICLE",
  TRAVEL_ASSISTANCE = "TRAVEL_ASSISTANCE",
  PERSONAL_ACCIDENTS = "PERSONAL_ACCIDENTS",
  REPLACEMENT_VEHICLE_IMMOBILIZATION = "REPLACEMENT_VEHICLE_IMMOBILIZATION",
  VALUATION_COMPENSATION = "VALUATION_COMPENSATION",
  COMPLEMENTARY_SERVICES = "COMPLEMENTARY_SERVICES"
}

export const coverageGroupTranslations: Record<CoverageGroup, string> = {
  [CoverageGroup.CIVIL_LIABILITY]: "Responsabilidad Civil (RC)",
  [CoverageGroup.LEGAL_DEFENSE_MANAGEMENT]: "Defensa Jurídica y Gestión",
  [CoverageGroup.DAMAGE_TO_INSURED_VEHICLE]: "Daños al Vehículo Asegurado",
  [CoverageGroup.TRAVEL_ASSISTANCE]: "Asistencia en Viaje",
  [CoverageGroup.PERSONAL_ACCIDENTS]: "Accidentes Personales",
  [CoverageGroup.REPLACEMENT_VEHICLE_IMMOBILIZATION]: "Vehículo de Sustitución / Inmovilización",
  [CoverageGroup.VALUATION_COMPENSATION]: "Valoración y Compensación",
  [CoverageGroup.COMPLEMENTARY_SERVICES]: "Servicios Complementarios"
};

export const translateCoverageGroup = (group: CoverageGroup): string => {
  return coverageGroupTranslations[group];
};