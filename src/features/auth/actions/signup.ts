"use server";

import { AuthService } from "@/features/auth/services/auth.service";
import { AuthSchemas } from "@/lib/schemas"; // <-- Use centralized server schema

export async function signup(formData: FormData) {
  const rawFormData = Object.fromEntries(formData.entries());
  const validation = AuthSchemas.ServerSignupForm.safeParse(rawFormData); // <-- Use centralized server schema

  if (!validation.success) {
    return {
      success: false,
      error: "Los datos introducidos no son válidos. Por favor, revisa el formulario.",
    };
  }

  const { firstName, lastName, phone, email, password } = validation.data;

  const result = await AuthService.signUp({
    email,
    password,
    firstName,
    lastName,
    phone,
  });

  if (!result.success) {
    return { success: false, error: result.error };
  }

  return { success: true, email: validation.data.email };
}