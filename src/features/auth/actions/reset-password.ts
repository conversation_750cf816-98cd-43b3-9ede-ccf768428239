"use server";

import { AuthService } from "@/features/auth/services/auth.service";
import { z } from "zod";

const resetPasswordSchema = z.object({
  email: z.string().email({ message: "Por favor, introduce un correo electrónico válido." }),
});

export async function resetPassword(email: string) {
  const validation = resetPasswordSchema.safeParse({ email });

  if (!validation.success) {
    return {
      success: false,
      error: "Por favor, introduce un correo electrónico válido.",
    };
  }

  const { error } = await AuthService.resetPassword(validation.data.email);

  if (error) {
    if (typeof error === "string") {
      return { success: false, error };
    }
    return { success: false, error: error.message };
  }

  return { success: true };
}