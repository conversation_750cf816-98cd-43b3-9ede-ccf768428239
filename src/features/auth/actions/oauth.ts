"use server";

import { createClient } from "@/lib/supabase/server";

export async function signInWithGoogle() {
  const supabase = await createClient();
  const redirectTo = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirm`;

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: "google",
    options: {
      redirectTo,
      queryParams: {
        access_type: "offline",
        prompt: "consent",
      },
    },
  });

  if (error) {
    return { success: false, error: "No se pudo iniciar la sesión con Google." };
  }

  if (data.url) {
    return { success: true, url: data.url };
  }

  return { success: false, error: "No se pudo obtener la URL de redirección de Google." };
}