"use server";

import { createClient } from "@/lib/supabase/server";
import { db } from "@/lib/db";
import { revalidatePath } from "next/cache";
import { AuthSchemas } from "@/lib/schemas";
import { redirect } from "next/navigation";
import { AuthService } from "../services/auth.service";
import { handlePhoneValidationError } from "@/lib/validation/phone-validation";

export async function updatePhoneNumber(formData: FormData) {
    const supabase = await createClient();
    const {
        data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
        return { success: false, error: "Debes iniciar sesión para actualizar tu perfil." };
    }

    const phone = formData.get("phone");
    const validation = AuthSchemas.ServerPhoneSchema.safeParse({ phone });

    if (!validation.success) {
        const errorMessage = validation.error.errors[0]?.message ?? "El valor introducido no es válido.";
        return { success: false, error: errorMessage };
    }

    try {
        // First, update the user in your public schema
        await db.user.update({
            where: { id: user.id },
            data: { phone: validation.data.phone },
        });

        // Update metadata in Supabase Auth (metadata only, no direct phone field)
        try {
            const { error: updateUserError } = await supabase.auth.updateUser({
                data: {
                    phone: validation.data.phone,
                    phone_number_is_set: true,
                },
            });

            if (updateUserError) {
                // Continue anyway - database update succeeded which is what matters
            }
        } catch {
            // Continue anyway - database update succeeded which is what matters
        }

        // Force a session refresh to ensure the next request has the updated JWT
        await supabase.auth.refreshSession();

    } catch (error) {
        
        // Use centralized phone validation error handling
        const phoneValidation = handlePhoneValidationError(error);
        if (phoneValidation.isPhoneUniqueConstraint) {
            return { success: false, error: phoneValidation.userFriendlyMessage };
        }
        
        return { success: false, error: "No se pudo actualizar el número de teléfono." };
    }

    revalidatePath("/", "layout"); // Revalidate after the try/catch
    const homeRoute = AuthService.getHomeRouteForRole(user.user_metadata.role);
    redirect(homeRoute);
}