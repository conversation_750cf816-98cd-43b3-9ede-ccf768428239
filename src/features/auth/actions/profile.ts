"use server";

import { db } from "@/lib/db";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger";

// Update user profile data
export async function updateUserProfile(
  userId: string,
  fullName: string,
  phone?: string
) {
  try {
    logger.info("Updating user profile", { userId });

    // Parse full name into first and last name
    const [firstName, ...lastNameParts] = fullName.split(" ");
    const lastName = lastNameParts.join(" ");

    // Update user in database
    const updatedUser = await db.user.update({
      where: { id: userId },
      data: {
        firstName: firstName ?? null,
        lastName: lastName ?? null,
        displayName: fullName,
        phone: phone ?? null,
        updatedAt: new Date()
      }
    });

    logger.info("Profile updated successfully", { userId });

    // Revalidate cache to update profile information across all role-based settings
    revalidatePath("/account-holder/settings");
    revalidatePath("/broker/settings");
    revalidatePath("/admin/settings");
    revalidatePath("/dashboard");

    return { success: true, data: updatedUser };
  } catch (error) {
    logger.error("Unexpected error updating profile", error, { userId });
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al actualizar el perfil"
    };
  }
}