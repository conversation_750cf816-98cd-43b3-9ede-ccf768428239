"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { AuthService } from "@/features/auth/services/auth.service";

export async function login(formData: FormData) {
  const email = formData.get("email");
  const password = formData.get("password");

  if (!email || !password) {
    return { error: "El correo electrónico y la contraseña son requeridos" };
  }

  const credentials = {
    email: email as string,
    password: password as string,
  };

  const { error } = await AuthService.signIn(credentials);

  if (error) {
    return { error };
  }

  const userRole = await AuthService.getUserRole();
  const homeRoute = AuthService.getHomeRouteForRole(userRole);

  revalidatePath("/", "layout");
  redirect(homeRoute);
}