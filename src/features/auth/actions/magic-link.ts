"use server";

import { AuthService } from "@/features/auth/services/auth.service";
import { z } from "zod";

const magicLinkSchema = z.object({
  email: z.string().email({ message: "Por favor, introduce un correo electrónico válido." }),
});

export async function signInWithMagicLink(email: string) {
  const validation = magicLinkSchema.safeParse({ email });

  if (!validation.success) {
    return {
      success: false,
      error: "Por favor, introduce un correo electrónico válido.",
    };
  }

  const { success: _success, error } = await AuthService.signInWithOtp(validation.data.email);

  if (error) {
    return { success: false, error };
  }

  return { success: true };
}