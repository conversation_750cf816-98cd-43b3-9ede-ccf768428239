"use server";

import { AuthService } from "../services/auth.service";

/**
 * CLIENT-SIDE SERVER ACTION: Get current user from client components
 * 
 * Use case: When you need user data in CLIENT COMPONENTS via server actions
 * - Called from client components using server action pattern
 * - Goes through AuthService for database access
 * - Returns raw Supabase user object
 * 
 * For server-side utilities (API routes, middleware), use getCurrentUser() from server-auth.ts instead
 * 
 * @returns The raw Supabase user object or null if not authenticated
 */
export async function getCurrentUser() {
  try {
    const {
      data: { user },
    } = await AuthService.getUser();
    return user;
  } catch {
    return null;
  }
}