import { db } from "@/lib/db";
import type { User, Prisma } from "@prisma/client";
import { Role } from "@prisma/client";

export const UserService = {
  // Create a new user
  async create(data: Prisma.UserCreateInput): Promise<User> {
    const newUser = await db.user.create({
      data,
      include: {
        accountHolderProfile: true,
        brokerProfile: true,
      }
    });

    if (newUser.role === Role.ACCOUNT_HOLDER) {
      await db.accountHolderProfile.create({
        data: {
          userId: newUser.id,
        },
      });
    }

    return newUser;
  },

  // Get a user by ID
  async getById(id: string): Promise<User | null> {
    return db.user.findUnique({
      where: { id },
      include: {
        accountHolderProfile: true,
        brokerProfile: {
          include: {
            billingAddress: true,
          }
        },
      }
    });
  },

  // Get a user by email
  async getByEmail(email: string): Promise<User | null> {
    return db.user.findUnique({
      where: { email },
      include: {
        accountHolderProfile: true,
        brokerProfile: {
          include: {
            billingAddress: true,
          }
        }
      }
    });
  },

  // Update a user
  async update(id: string, data: Prisma.UserUpdateInput): Promise<User> {
    return db.user.update({
      where: { id },
      data,
      include: {
        accountHolderProfile: true,
        brokerProfile: true
      }
    });
  },

  // Delete a user
  async delete(id: string): Promise<User> {
    return db.user.delete({
      where: { id }
    });
  },

  // List users with pagination and optional filters
  async list(params: {
    skip?: number;
    take?: number;
    where?: Prisma.UserWhereInput;
    orderBy?: Prisma.UserOrderByWithRelationInput;
  }): Promise<{ users: User[]; total: number }> {
    const { skip = 0, take = 10, where, orderBy } = params;
    
    const [users, total] = await Promise.all([
      db.user.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          accountHolderProfile: true,
          brokerProfile: {
            include: {
              billingAddress: true,
            }
          }
        }
      }),
      db.user.count({ where })
    ]);

    return { users, total };
  },

  // Get users by role
  async getByRole(role: Role): Promise<User[]> {
    return db.user.findMany({
      where: { role },
      include: {
        accountHolderProfile: true,
        brokerProfile: {
          include: {
            billingAddress: true,
          }
        }
      }
    });
  }
};