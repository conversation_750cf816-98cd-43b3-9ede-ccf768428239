import { db } from "@/lib/db";
import type { BrokerProfile, Prisma } from "@prisma/client";
import { KYCStatus } from "@prisma/client";

export const BrokerProfileService = {
  // Create a new broker profile
  async create(data: Prisma.BrokerProfileCreateInput): Promise<BrokerProfile> {
    return db.brokerProfile.create({
      data,
      include: {
        user: true,
        billingAddress: true,
        documents: true,
        bids: true
      }
    });
  },

  // Get a broker profile by ID
  async getById(id: string): Promise<BrokerProfile | null> {
    return db.brokerProfile.findUnique({
      where: { id },
      include: {
        user: true,
        billingAddress: true,
        documents: true,
        bids: {
          include: {
            auction: true
          }
        }
      }
    });
  },

  // Get a broker profile by user ID
  async getByUserId(userId: string): Promise<BrokerProfile | null> {
    return db.brokerProfile.findUnique({
      where: { userId },
      include: {
        user: true,
        billingAddress: true,
        documents: true,
        bids: {
          include: {
            auction: true
          }
        }
      }
    });
  },

  // Update a broker profile
  async update(id: string, data: Prisma.BrokerProfileUpdateInput): Promise<BrokerProfile> {
    return db.brokerProfile.update({
      where: { id },
      data,
      include: {
        user: true,
        billingAddress: true,
        documents: true,
        bids: true
      }
    });
  },

  // Update KYC status
  async updateKycStatus(id: string, kycStatus: KYCStatus): Promise<BrokerProfile> {
    return db.brokerProfile.update({
      where: { id },
      data: { kycStatus },
      include: {
        user: true,
        billingAddress: true
      }
    });
  },

  // Delete a broker profile
  async delete(id: string): Promise<BrokerProfile> {
    return db.brokerProfile.delete({
      where: { id }
    });
  },

  // List broker profiles with pagination and optional filters
  async list(params: {
    skip?: number;
    take?: number;
    where?: Prisma.BrokerProfileWhereInput;
    orderBy?: Prisma.BrokerProfileOrderByWithRelationInput;
  }): Promise<{ brokers: BrokerProfile[]; total: number }> {
    const { skip = 0, take = 10, where, orderBy } = params;
    
    const [brokers, total] = await Promise.all([
      db.brokerProfile.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          user: true,
          billingAddress: true,
          documents: true,
          bids: {
            include: {
              auction: true
            }
          }
        }
      }),
      db.brokerProfile.count({ where })
    ]);

    return { brokers, total };
  },

  // Get verified brokers
  async getVerifiedBrokers(): Promise<BrokerProfile[]> {
    return db.brokerProfile.findMany({
      where: {
        kycStatus: KYCStatus.VERIFIED
      },
      include: {
        user: true,
        billingAddress: true
      }
    });
  }
};