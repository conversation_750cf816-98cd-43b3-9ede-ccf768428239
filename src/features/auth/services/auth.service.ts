import { createClient } from "@/lib/supabase/server";
import { createServerAdminClient } from "@/lib/supabase/server-admin";
import { Role } from "@prisma/client";
import { ROLES_CONFIG } from "@/features/auth/config/roles";
import { UserService } from "./user.service";
import { isPhoneAlreadyInUse } from "@/lib/validation/phone-validation";

export const AuthService = {
  async checkEmailExists(email: string) {
    const user = await UserService.getByEmail(email);
    return !!user;
  },

  async signIn(credentials: { email: string; password: string }) {
    const supabase = await createClient();
    const emailExists = await this.checkEmailExists(credentials.email);
    if (!emailExists) {
      return { error: "Este correo electrónico no está registrado." };
    }
    const { data, error } = await supabase.auth.signInWithPassword(credentials);

    if (error) {
      switch (error.message) {
        case "Invalid login credentials":
          return { error: "Correo electrónico o contraseña incorrectos" };
        case "Email not confirmed":
          return { error: "Por favor, confirma tu correo electrónico antes de iniciar sesión" };
        default:
          return { error: error.message };
      }
    }

    return { data };
  },

  async signUp(userData: {
    email: string;
    password?: string;
    firstName: string;
    lastName: string;
    phone: string;
  }) {
    const supabase = await createClient();
    const { email, password, firstName, lastName, phone } = userData;

    const emailExists = await this.checkEmailExists(email);
    if (emailExists) {
      return { success: false, error: "Este correo electrónico ya está registrado." };
    }

    // Check if phone number is already in use
    if (phone && phone.trim().length > 0) {
      const phoneInUse = await isPhoneAlreadyInUse(phone.trim());
      if (phoneInUse) {
        return { success: false, error: "Este número de teléfono ya está registrado." };
      }
    }

    const metadata = {
      first_name: firstName.trim(),
      last_name: lastName.trim(),
      phone: phone ? phone.trim() : "",
    };

    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: email.trim(),
      password: password ?? "",
      phone: phone,
      options: {
        data: {
          ...metadata,
          role: Role.ACCOUNT_HOLDER,
        },
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirm`,
      },
    });

    if (authError) {
      let errorMessage = "Error al crear la cuenta. Por favor, inténtalo de nuevo.";
      if (authError.message.includes("User already registered")) {
        errorMessage = "Este correo electrónico ya está registrado.";
      } else if (authError.message.includes("Password should be at least 6 characters")) {
        errorMessage = "La contraseña debe tener al menos 6 caracteres.";
      } else if (authError.message.includes("Unable to validate email address")) {
        errorMessage = "El correo electrónico no es válido.";
      }
      return { success: false, error: errorMessage };
    }

    return { success: true, data: authData };
  },

  async signInWithOtp(email: string) {
    const supabase = await createClient();
    const emailExists = await this.checkEmailExists(email);
    if (!emailExists) {
      return { success: false, error: "Este correo electrónico no está registrado." };
    }
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirm`,
      },
    });

    if (error) {
      let errorMessage = "No se pudo enviar el enlace mágico. Por favor, inténtalo de nuevo.";
      if (error.message.includes("rate limit")) {
        errorMessage = "Has intentado enviar demasiados enlaces mágicos. Por favor, espera un momento antes de volver a intentarlo.";
      }
      return { success: false, error: errorMessage };
    }

    return { success: true };
  },

  async getUser() {
    const supabase = await createClient();
    return supabase.auth.getUser();
  },

  async getUserRole() {
    const { data } = await this.getUser();
    return data.user?.user_metadata?.role as Role | undefined;
  },

  getHomeRouteForRole(role?: Role) {
    return role ? ROLES_CONFIG[role].home : "/";
  },

  async signInWithGoogle() {
    const supabase = await createClient();
    const redirectTo = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirm`;

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo,
      },
    });

    if (error) {
      return { success: false, error: error.message, url: null };
    }

    return { success: true, url: data.url };
  },

  async resetPassword(email: string) {
    const supabase = await createClient();
    const emailExists = await this.checkEmailExists(email);
    if (!emailExists) {
      return { error: "Este correo electrónico no está registrado." };
    }
    const redirectTo = `${process.env.NEXT_PUBLIC_SITE_URL}/reset-password`;

    return supabase.auth.resetPasswordForEmail(email, {
      redirectTo,
    });
  },

  async updateUserPassword(password: string) {
    const supabase = await createClient();
    const { data: userData } = await this.getUser();
    const currentUserMetadata = userData.user?.user_metadata ?? {};
    
    const { error } = await supabase.auth.updateUser({ 
      password,
      data: {
        ...currentUserMetadata,
        password_set: true, // Ensure flag is set when updating password
        has_email_password: true // Additional flag for consistency
      }
    });
    
    if (error) {
      let errorMessage = "No se pudo actualizar la contraseña. Por favor, inténtalo de nuevo.";
      if (error.message.includes("New password should be different from the old password")) {
        errorMessage = "La nueva contraseña debe ser diferente a la anterior.";
      }
      return { error: errorMessage };
    }
    return { error: null };
  },

  async updateUserPasswordWithVerification(email: string, currentPassword: string, newPassword: string) {
    const supabase = await createClient();
    
    // Verify current password by attempting to sign in with it
    try {
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password: currentPassword,
      });

      if (signInError) {
        return { error: "La contraseña actual es incorrecta." };
      }
    } catch {
      return { error: "No se pudo verificar la contraseña actual." };
    }

    // Update password using existing method
    return this.updateUserPassword(newPassword);
  },

  async checkUserHasPassword() {
    const supabase = await createClient();
    const { data } = await supabase.auth.getUser();
    if (!data.user) return false;
    
    // Check user_metadata for our custom flags first (most reliable)
    const hasEmailPassword = data.user.user_metadata?.has_email_password === true;
    const passwordSet = data.user.user_metadata?.password_set === true;
    
    if (hasEmailPassword || passwordSet) return true;
    
    // For users created via email/password (non-OAuth), check identities
    const hasEmailIdentity = data.user.identities?.some(identity => identity.provider === "email") ?? false;
    if (hasEmailIdentity) return true;
    
    // Check providers as fallback
    const hasEmailInProviders = data.user.app_metadata?.providers?.includes("email") ?? false;
    if (hasEmailInProviders) return true;
    
    // Additional check: if user was created with email and has email confirmed, they likely have password
    // This handles cases where metadata flags aren't set but user registered with email/password
    if (data.user.email && data.user.email_confirmed_at && data.user.identities?.length === 1) {
      const emailIdentity = data.user.identities[0];
      if (emailIdentity?.provider === "email") return true;
    }
    
    // If none of the above, user doesn't have password
    return false;
  },

  async setInitialPassword(newPassword: string) {
    // For OAuth users setting password for the first time
    const supabase = await createClient();
    
    // Get current user data
    const { data: userData } = await this.getUser();
    if (!userData.user?.email) {
      return { error: "No se pudo obtener el correo electrónico del usuario." };
    }
    
    // Get current metadata
    const currentUserMetadata = userData.user.user_metadata ?? {};
    
    // Set the password and user metadata flags
    const { error: passwordError } = await supabase.auth.updateUser({ 
      password: newPassword,
      data: {
        ...currentUserMetadata,
        password_set: true, // Set flag indicating password has been set
        has_email_password: true // Additional flag for OAuth users with password
      }
    });
    
    if (passwordError) {
      let errorMessage = "No se pudo establecer la contraseña. Por favor, inténtalo de nuevo.";
      if (passwordError.message.includes("Password should be different from the old password")) {
        errorMessage = "La nueva contraseña debe ser diferente a la anterior.";
      }
      return { error: errorMessage };
    }

    // Update app_metadata providers array using admin API
    // Ensure email is first in the array, followed by other providers
    const currentProviders = userData.user.app_metadata?.providers ?? [];
    const otherProviders = currentProviders.filter((provider: string) => provider !== "email");
    const updatedProviders = ["email", ...otherProviders]; // Email first, then others
    
    const providersUpdateResult = await this.updateProvidersArray(
      userData.user.id, 
      updatedProviders
    );
    
    if (providersUpdateResult.error) {
      // Log error but don't fail the entire operation since password was set
    }
    
    // Refresh user session to ensure changes are reflected
    await supabase.auth.refreshSession();
    
    return { error: null };
  },

  async signOut() {
    const supabase = await createClient();
    return supabase.auth.signOut();
  },

  async updateProvidersArray(userId: string, providers: string[]) {
    try {
      const adminClient = await createServerAdminClient();
      
      // Get current user to preserve existing data
      const { data: currentUser, error: fetchError } = await adminClient.auth.admin.getUserById(userId);
      
      if (fetchError || !currentUser.user) {
        return { error: "No se pudo obtener la información del usuario." };
      }
      
      const currentAppMetadata = currentUser.user.app_metadata ?? {};
      const userEmail = currentUser.user.email;
      
      // Update both app_metadata and email_confirm to trigger email provider addition
      const { data, error: updateError } = await adminClient.auth.admin.updateUserById(userId, {
        email: userEmail, // Re-set email to trigger provider update
        email_confirm: true, // Confirm email to ensure it's properly set
        app_metadata: {
          ...currentAppMetadata,
          provider: "email",      // Set primary provider to email
          providers: providers,   // Update providers array
        }
      });
      
      if (updateError) {
        return { error: "No se pudo actualizar la información del proveedor." };
      }
      
      return { error: null, data };
    } catch {
      return { error: "No se pudo actualizar la información del proveedor." };
    }
  },
};