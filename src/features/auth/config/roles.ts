// Use Prisma-generated Role enum as single source of truth
import { Role } from "@prisma/client";

// Define the structure for each role's configuration.
export interface RoleConfig {
  home: string; // The default page for a user with this role.
  allowedPaths: string[]; // The URL prefixes this role can access.
}

// Master configuration for role-based access control.
// This object is the single source of truth for all role-based rules.
export const ROLES_CONFIG: Record<Role, RoleConfig> = {
  [Role.ADMIN]: {
    home: "/admin/dashboard",
    allowedPaths: ["/admin"],
  },
  [Role.BROKER]: {
    home: "/broker/auctions",
    allowedPaths: ["/broker"],
  },
  [Role.ACCOUNT_HOLDER]: {
    home: "/account-holder/policies",
    allowedPaths: ["/account-holder", "/support"],
  },
};