import { UserService } from "../services/user.service";
import type { User as SupabaseUser } from "@supabase/supabase-js";
import type { SupabaseClient } from "@supabase/supabase-js";
import { Role } from "@prisma/client";

export async function syncUserWithDatabase(supabaseUser: SupabaseUser, supabase?: SupabaseClient) {
  try {
    if (!supabaseUser.id || !supabaseUser.email) {
      throw new Error("Invalid Supabase user object");
    }

    const existingUser = await UserService.getById(supabaseUser.id);

    if (existingUser) {
      // Update metadata for existing users if missing
      const needsRoleSync = !supabaseUser.user_metadata?.role;
      const needsPhoneSync = supabaseUser.user_metadata?.phone_number_is_set === undefined;
      
      if (supabase && (needsRoleSync || needsPhoneSync)) {
        try {
          const metadataUpdate = {
            ...supabaseUser.user_metadata,
            role: existingUser.role,
            phone_number_is_set: !!existingUser.phone,
            ...(existingUser.phone && { phone: existingUser.phone })
          };

          await supabase.auth.updateUser({
            data: metadataUpdate
          });
        } catch {
          // Continue anyway - user exists in database
        }
      }
      return existingUser;
    }

    // Extract name information - handle both OAuth (full_name) and regular signup (first_name, last_name)
    const metadata = supabaseUser.user_metadata;
    let firstName = "";
    let lastName = "";
    let displayName = "";
    
    // OAuth providers typically provide full_name
    if (metadata.full_name) {
      const [firstPart, ...lastParts] = metadata.full_name.split(" ");
      firstName = firstPart ?? "";
      lastName = lastParts.join(" ") ?? "";
      displayName = metadata.full_name;
    } else {
      // Regular signup provides first_name and last_name separately
      firstName = metadata.first_name ?? "";
      lastName = metadata.last_name ?? "";
    }
    
    // Build display name if not set
    if (!displayName) {
      const nameFromParts = `${firstName} ${lastName}`.trim();
      // Use logical OR here intentionally to handle empty strings
      if (nameFromParts) {
        displayName = nameFromParts;
      } else if (supabaseUser.email) {
        displayName = supabaseUser.email.split("@")[0] ?? "Usuario";
      } else {
        displayName = "Usuario";
      }
    }

    // For regular signup, phone is already in metadata; for OAuth, it's null
    const phoneFromMetadata = metadata.phone;
    const hasPhone = phoneFromMetadata && phoneFromMetadata.trim().length > 0;
    
    const newUser = await UserService.create({
      id: supabaseUser.id,
      email: supabaseUser.email,
      firstName: firstName.length > 0 ? firstName : null,
      lastName: lastName.length > 0 ? lastName : null,
      displayName: displayName,
      phone: hasPhone ? phoneFromMetadata : null,
      role: Role.ACCOUNT_HOLDER,
      optIn: true,
    });

    // Update Supabase metadata to reflect the new user state
    if (supabase) {
      try {
        const metadataUpdate = {
          ...supabaseUser.user_metadata,
          role: newUser.role,
          phone_number_is_set: hasPhone,
          ...(hasPhone && { phone: phoneFromMetadata })
        };

        await supabase.auth.updateUser({
          data: metadataUpdate
        });
      } catch {
        // Continue anyway - user was created successfully in database
      }
    }

    return newUser;

  } catch {
    return null;
  }
}