import { db } from "@/lib/db";
import { User } from "@supabase/supabase-js";
import { logger } from "@/lib/logger";
import { AccountHolderProfile, Role } from "@prisma/client";

/**
 * Error thrown when account holder profile operations fail
 */
export class ProfileResolverError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = "ProfileResolverError";
  }
}

/**
 * Resolve account holder profile for a user
 * Creates profile if it doesn't exist (fallback for missed trigger execution)
 */
export async function resolveAccountHolderProfile(
  user: User
): Promise<AccountHolderProfile> {
  try {
    // First, try to find existing profile
    let accountHolderProfile = await db.accountHolderProfile.findUnique({
      where: { userId: user.id }
    });

    // If profile doesn't exist and user is ACCOUNT_HOLDER, create it
    if (!accountHolderProfile && user.user_metadata?.role === Role.ACCOUNT_HOLDER) {
      try {
        accountHolderProfile = await db.accountHolderProfile.create({
          data: {
            userId: user.id
          }
        });
        logger.info(`Created missing AccountHolderProfile for user ${user.id}`, { userId: user.id });
      } catch (createError) {
        logger.error("Error creating AccountHolderProfile", createError, { userId: user.id });
        throw new ProfileResolverError(
          "Error creando perfil de titular de cuenta",
          500,
          "PROFILE_CREATION_FAILED"
        );
      }
    }

    // If still no profile, throw error
    if (!accountHolderProfile) {
      throw new ProfileResolverError(
        "Perfil de titular de cuenta no encontrado",
        404,
        "PROFILE_NOT_FOUND"
      );
    }

    return accountHolderProfile;
  } catch (error) {
    if (error instanceof ProfileResolverError) {
      throw error;
    }
    
    logger.error("Error resolving AccountHolderProfile", error, { userId: user.id });
    throw new ProfileResolverError(
      "Error interno resolviendo perfil",
      500,
      "PROFILE_RESOLUTION_FAILED"
    );
  }
}

/**
 * Get account holder profile with optional creation
 * More explicit version that allows controlling creation behavior
 */
export async function getAccountHolderProfile(
  userId: string,
  createIfMissing: boolean = true
): Promise<AccountHolderProfile | null> {
  try {
    let profile = await db.accountHolderProfile.findUnique({
      where: { userId }
    });

    if (!profile && createIfMissing) {
      profile = await db.accountHolderProfile.create({
        data: { userId }
      });
      logger.info(`Created AccountHolderProfile for user ${userId}`, { userId });
    }

    return profile;
  } catch (error) {
    logger.error("Error getting AccountHolderProfile", error, { userId });
    throw new ProfileResolverError(
      "Error obteniendo perfil de titular de cuenta",
      500,
      "PROFILE_GET_FAILED"
    );
  }
}

/**
 * Ensure account holder profile exists for user
 * Throws error if profile cannot be created or found
 */
export async function ensureAccountHolderProfile(
  user: User
): Promise<AccountHolderProfile> {
  const profile = await resolveAccountHolderProfile(user);
  return profile;
}

/**
 * Type guard for profile resolver errors
 */
export function isProfileResolverError(error: unknown): error is ProfileResolverError {
  return error instanceof ProfileResolverError;
}