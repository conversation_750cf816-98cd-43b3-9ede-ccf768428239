import { User } from "@supabase/supabase-js";

/**
 * Safely extracts user name from Supabase user metadata
 * Handles different possible metadata structures
 */
export function extractUserName(user: User | null): { firstName: string, lastName: string, fullName: string } {
  if (!user) {
    return { firstName: "", lastName: "", fullName: "Usuario" };
  }

  let firstName = "";
  let lastName = "";

  if (user.user_metadata) {
    if (typeof user.user_metadata.first_name === "string") {
      firstName = user.user_metadata.first_name;
    } else if (typeof user.user_metadata.firstName === "string") {
      firstName = user.user_metadata.firstName;
    } else if (typeof user.user_metadata.name === "string") {
      const nameParts = user.user_metadata.name.split(" ");
      firstName = nameParts[0] ?? "";
      if (nameParts.length > 1) {
        lastName = nameParts.slice(1).join(" ");
      }
    }

    if (!lastName && typeof user.user_metadata.last_name === "string") {
      lastName = user.user_metadata.last_name;
    } else if (!lastName && typeof user.user_metadata.lastName === "string") {
      lastName = user.user_metadata.lastName;
    }
  }

  if (!firstName && user.email) {
    const emailName = user.email.split("@")[0] ?? "";
    const nameParts = emailName.split(/[._-]/);
    firstName = nameParts.map(part =>
      part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
    ).join(" ");
  }

  const fullName = [firstName, lastName].filter(Boolean).join(" ") || "Usuario";

  return { firstName, lastName, fullName };
}


