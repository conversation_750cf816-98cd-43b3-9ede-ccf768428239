import { createServerAdminClient } from "@/lib/supabase/server-admin";
import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { Role } from "@prisma/client";
import { redirect } from "next/navigation";
import { NextRequest, NextResponse } from "next/server";

/**
 * Server-side authentication utilities for secure user validation.
 * These utilities ensure proper type safety and role validation.
 */

export interface AuthenticatedUser {
  id: string
  email: string
  role: Role
  phone?: string
}

/**
 * SERVER-SIDE UTILITY: Get current user with enhanced type safety and role validation
 * 
 * Use case: When you need user data in SERVER-SIDE code (API routes, middleware, server components)
 * - Direct Supabase admin client access for server-side operations
 * - Enhanced type safety with AuthenticatedUser interface
 * - Built-in role validation and type guards
 * - Provides utilities like requireAuth(), requireRole(), hasRoleAccess()
 * 
 * For client components, use getCurrentUser() server action from get-current-user.ts instead
 * 
 * @returns Strongly typed AuthenticatedUser object or null if not authenticated
 */
export async function getCurrentUser(): Promise<AuthenticatedUser | null> {
  try {
    const supabase = await createServerAdminClient();
    
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      return null;
    }

    // Validate user metadata with proper type safety
    const userRole = user.user_metadata?.role as string | undefined;
    
    if (!userRole || !isValidRole(userRole)) {
      return null;
    }

    if (!user.email) {
      return null;
    }

    return {
      id: user.id,
      email: user.email,
      role: userRole as Role,
      phone: user.phone ?? undefined
    };
  } catch {
    return null;
  }
}

/**
 * Type guard to validate if a string is a valid Role
 */
export function isValidRole(role: string): role is Role {
  return [Role.ADMIN, Role.BROKER, Role.ACCOUNT_HOLDER].includes(role as Role);
}

/**
 * Requires authentication and returns the user, redirecting to login if not authenticated.
 */
export async function requireAuth(): Promise<AuthenticatedUser> {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/login");
  }
  
  return user;
}

/**
 * Requires a specific role and returns the user, redirecting appropriately if not authorized.
 */
export async function requireRole(requiredRole: Role): Promise<AuthenticatedUser> {
  const user = await requireAuth();
  
  if (user.role !== requiredRole) {
    // Redirect to appropriate home page based on user's actual role
    const roleHomeMap: Record<Role, string> = {
      [Role.ADMIN]: "/admin/dashboard",
      [Role.BROKER]: "/broker/auctions",
      [Role.ACCOUNT_HOLDER]: "/account-holder/policies"
    };
    
    redirect(roleHomeMap[user.role] || "/unauthorized");
  }
  
  return user;
}

/**
 * Checks if the user has access to a specific role's functionality.
 * Returns the user if authorized, null otherwise.
 */
export async function hasRoleAccess(allowedRoles: Role[]): Promise<AuthenticatedUser | null> {
  const user = await getCurrentUser();
  
  if (!user || !allowedRoles.includes(user.role)) {
    return null;
  }
  
  return user;
}

/**
 * Validates session and returns user info for middleware usage
 */
export async function validateSession(): Promise<{
  user: AuthenticatedUser | null
  isValid: boolean
}> {
  try {
    const user = await getCurrentUser();
    return {
      user,
      isValid: user !== null
    };
  } catch {
    return {
      user: null,
      isValid: false
    };
  }
}
/**
 * Get the current user from the request/response context for middleware
 * This function handles cookie management and user authentication in middleware
 */
export async function getServerUser(request: NextRequest, response: NextResponse) {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error("Missing Supabase environment variables");
  }

  const supabase = createServerClient(
    supabaseUrl,
    supabaseServiceKey,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({ name, value, ...options });
          response.cookies.set({ name, value, ...options });
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({ name, value: "", ...options });
          response.cookies.set({ name, value: "", ...options });
        },
      },
    }
  );

  const { data: { user } } = await supabase.auth.getUser();
  return user;
}