"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useToast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";
import { PasswordInput } from "@/components/ui/password-input";
import { logger } from "@/lib/logger";

const passwordValidation = z
  .string()
  .min(8, {
    message: "La contraseña debe tener al menos 8 caracteres.",
  })
  .regex(/[A-Z]/, {
    message: "La contraseña debe contener al menos una letra mayúscula.",
  })
  .regex(/[a-z]/, {
    message: "La contraseña debe contener al menos una letra minúscula.",
  })
  .regex(/[0-9]/, {
    message: "La contraseña debe contener al menos un número.",
  });

// Schema for users changing existing password
const changePasswordSchema = z
  .object({
    current_password: z.string().min(1, {
      message: "La contraseña actual es requerida.",
    }),
    new_password: passwordValidation,
    confirm_password: z.string(),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: "Las contraseñas no coinciden.",
    path: ["confirm_password"],
  });

// Schema for OAuth users setting initial password
const setPasswordSchema = z
  .object({
    new_password: passwordValidation,
    confirm_password: z.string(),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: "Las contraseñas no coinciden.",
    path: ["confirm_password"],
  });

type ChangePasswordFormValues = z.infer<typeof changePasswordSchema>;
type SetPasswordFormValues = z.infer<typeof setPasswordSchema>;

interface PasswordSettingsFormProps {
  title?: string;
  description?: string;
}

export function PasswordSettingsForm({
  title,
  description,
}: PasswordSettingsFormProps) {
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isCheckingPasswordStatus, setIsCheckingPasswordStatus] = useState(true);
  const [hasPassword, setHasPassword] = useState<boolean>(false);
  const { toast } = useToast();

  // Check if user has password on component mount
  useEffect(() => {
    const checkPasswordStatus = async () => {
      try {
        // Call API endpoint to check password status
        const response = await fetch("/api/profiles/password-status", {
          credentials: "include",
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            // Handle nested data structure (double wrapped response)
            const passwordData = result.data.success ? result.data.data : result.data;
            setHasPassword(passwordData.hasPassword);
          } else {
            // Default to password change form if there's an error
            setHasPassword(true);
          }
        } else {
          // Default to password change form if there's an error
          setHasPassword(true);
        }
      } catch (error) {
        logger.error("Error checking password status", error);
        // Default to password change form if there's an error
        setHasPassword(true);
      } finally {
        setIsCheckingPasswordStatus(false);
      }
    };

    checkPasswordStatus();
  }, []);

  const changePasswordForm = useForm<ChangePasswordFormValues>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      current_password: "",
      new_password: "",
      confirm_password: "",
    },
  });

  const setPasswordForm = useForm<SetPasswordFormValues>({
    resolver: zodResolver(setPasswordSchema),
    defaultValues: {
      new_password: "",
      confirm_password: "",
    },
  });

  const onPasswordSubmit = async (data: ChangePasswordFormValues | SetPasswordFormValues) => {
    try {
      setIsChangingPassword(true);
      
      const requestBody = hasPassword 
        ? { 
            currentPassword: (data as ChangePasswordFormValues).current_password,
            newPassword: data.new_password,
          }
        : { 
            newPassword: data.new_password,
          };
      
      const response = await fetch("/api/profiles/password", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      const responseData = await response.json();
      
      if (!response.ok) {
        throw new Error(responseData.error ?? "No se pudo procesar la solicitud. Por favor, inténtalo de nuevo.");
      }

      if (!responseData.success) {
        throw new Error(responseData.error ?? "No se pudo procesar la solicitud. Por favor, inténtalo de nuevo.");
      }
      
      // Reset the appropriate form
      if (hasPassword) {
        changePasswordForm.reset();
      } else {
        setPasswordForm.reset();
      }
      
      toast({
        title: hasPassword ? "Contraseña actualizada" : "Contraseña establecida",
        description: responseData.message ?? (hasPassword ? "Contraseña actualizada correctamente" : "Contraseña establecida correctamente"),
      });

      // If this was an OAuth user setting initial password, update the state
      if (!hasPassword) {
        setHasPassword(true);
      }
    } catch (error) {
      logger.error("Error updating/setting password", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "No se pudo procesar la solicitud. Por favor, inténtalo de nuevo.",
      });
    } finally {
      setIsChangingPassword(false);
    }
  };

  // Show loading state while checking password status
  if (isCheckingPasswordStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Configuración de Contraseña</CardTitle>
          <CardDescription>Cargando configuración...</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  // Dynamic titles and descriptions
  const cardTitle = title ?? (hasPassword ? "Cambiar Contraseña" : "Establecer Contraseña");
  const cardDescription = description ?? (hasPassword 
    ? "Actualiza tu contraseña para mantener tu cuenta segura." 
    : "Establece una contraseña para poder iniciar sesión también con correo y contraseña.");

  return (
    <Card>
      <CardHeader>
        <CardTitle>{cardTitle}</CardTitle>
        <CardDescription>{cardDescription}</CardDescription>
      </CardHeader>
      <CardContent>
        {hasPassword ? (
          // Form for changing existing password
          <Form {...changePasswordForm}>
            <form
              id="password-form"
              onSubmit={changePasswordForm.handleSubmit(onPasswordSubmit)}
              className="space-y-6"
            >
              <FormField
                control={changePasswordForm.control}
                name="current_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contraseña Actual</FormLabel>
                    <FormControl>
                      <PasswordInput
                        placeholder="••••••••"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={changePasswordForm.control}
                name="new_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nueva Contraseña</FormLabel>
                    <FormControl>
                      <PasswordInput
                        placeholder="••••••••"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      La contraseña debe tener al menos 8 caracteres,
                      incluyendo una letra mayúscula, una minúscula y un
                      número.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={changePasswordForm.control}
                name="confirm_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirmar Contraseña</FormLabel>
                    <FormControl>
                      <PasswordInput
                        placeholder="••••••••"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        ) : (
          // Form for setting initial password (OAuth users)
          <Form {...setPasswordForm}>
            <form
              id="password-form"
              onSubmit={setPasswordForm.handleSubmit(onPasswordSubmit)}
              className="space-y-6"
            >
              <FormField
                control={setPasswordForm.control}
                name="new_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contraseña</FormLabel>
                    <FormControl>
                      <PasswordInput
                        placeholder="••••••••"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      La contraseña debe tener al menos 8 caracteres,
                      incluyendo una letra mayúscula, una minúscula y un
                      número.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={setPasswordForm.control}
                name="confirm_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirmar Contraseña</FormLabel>
                    <FormControl>
                      <PasswordInput
                        placeholder="••••••••"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button
          type="submit"
          form="password-form"
          disabled={
            isChangingPassword || 
            (hasPassword ? !changePasswordForm.formState.isDirty : !setPasswordForm.formState.isDirty)
          }
          variant="default"
        >
          {isChangingPassword && (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          )}
          {isChangingPassword
            ? (hasPassword ? "Actualizando..." : "Estableciendo...")
            : (hasPassword ? "Actualizar Contraseña" : "Establecer Contraseña")}
        </Button>
      </CardFooter>
    </Card>
  );
}