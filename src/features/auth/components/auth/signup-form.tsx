"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { createClient } from "@/lib/supabase/client";
import { signup } from "@/features/auth/actions/signup";
import { PhoneInput } from "@/components/ui/phone-input";
import { GoogleSignInButton } from "./google-sign-in-button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { AuthSchemas, SignupFormValues } from "@/lib/schemas";
import { useToast } from "@/components/ui/use-toast";

interface SignUpFormProps {
  className?: string;
}

export function SignUpForm({
  className,
}: SignUpFormProps) {
  const router = useRouter();
  const supabase = createClient();
  const [isResending, setIsResending] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [userEmail, setUserEmail] = useState<string>("");
  const [cooldownTime, setCooldownTime] = useState(0);
  const [cooldownActive, setCooldownActive] = useState(false);
  const { toast } = useToast();

  const form = useForm<SignupFormValues>({
    resolver: zodResolver(AuthSchemas.SignupForm),
    defaultValues: {
      firstName: "",
      lastName: "",
      phone: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = form;

  const handleResendEmail = async () => {
    if (cooldownActive) { // Prevent multiple calls during cooldown
      return;
    }

    if (!userEmail) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "No se pudo determinar la dirección de correo. Por favor, intenta registrarte nuevamente.",
      });
      return;
    }

    setIsResending(true);
    setCooldownActive(true);
    setCooldownTime(60); // Start cooldown for 60 seconds (default)

    const timer = setInterval(() => {
      setCooldownTime((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer);
          setCooldownActive(false);
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    try {
      const { error } = await supabase.auth.resend({
        type: "signup",
        email: userEmail,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/confirm`,
        },
      });

      if (error) {
        if (error.message.includes("For security purposes, you can only request this after")) {
          const match = error.message.match(/after (\d+) seconds/);
          const seconds = match ? parseInt(match[1] ?? "0", 10) : "unos";
          toast({
            variant: "destructive",
            title: "Error",
            description: `Has solicitado reenviar el correo de confirmación demasiado pronto. Por favor, espera ${seconds} segundos antes de intentarlo de nuevo.`,
          });
        } else {
          toast({
            variant: "destructive",
            title: "Error",
            description: "No se pudo reenviar el correo de confirmación. Por favor, intenta más tarde.",
          });
        }
      }
    } catch {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Ocurrió un error al reenviar el correo. Por favor, intenta más tarde.",
      });
    } finally {
      setIsResending(false);
    }
  };

  if (emailSent) {
    return (
      <div className="p-6 space-y-6 text-center max-w-md mx-auto">
        <h2 className="text-xl font-semibold">
          Correo de confirmación
        </h2>
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 mb-4">
          <AlertCircle className="h-6 w-6 text-green-600" />
        </div>
        <p className="text-gray-600 mb-3">
          Te acabamos de enviar un enlace de confirmación a tu buzón. Puedes tardar hasta 5 minutos en recibirlo, recuerda revisar tu carpeta de <strong>SPAM</strong> 🗑️.
        </p>

        <div className="space-y-3">
          <Button
            onClick={handleResendEmail}
            variant="default"
            className="w-full"
            disabled={isResending || cooldownActive}
          >
            {isResending ? "Enviando..." : cooldownActive ? `Reenviar en ${cooldownTime}s` : "Reenviar correo de confirmación"}
          </Button>

          <Button
            onClick={() => router.push("/login")}
            variant="outline"
            className="w-full"
          >
            Ir a iniciar sesión
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col gap-4 p-6 max-w-md mx-auto", className)}>
      <Form {...form}>
        <form
          onSubmit={handleSubmit(async (data) => {
            const formData = new FormData();
            formData.append("firstName", data.firstName);
            formData.append("lastName", data.lastName);
            formData.append("phone", data.phone);
            formData.append("email", data.email);
            formData.append("password", data.password);
            formData.append("confirmPassword", data.confirmPassword);
            formData.append("isBroker", "false");

            try {
              const result = await signup(formData);

              if (!result.success) {
                toast({
                  variant: "destructive",
                  title: "Error en el registro",
                  description: result.error ?? "Ocurrió un error al crear la cuenta. Por favor, inténtalo de nuevo.",
                });
                return;
              }

              if (result.email) {
                setUserEmail(result.email);
              }
              setEmailSent(true);
            } catch {
              toast({
                variant: "destructive",
                title: "Error",
                description: "Ocurrió un error al crear la cuenta. Por favor, inténtalo de nuevo.",
              });
            }
          })}
          className="flex flex-col gap-4"
        >
          <div className="flex flex-col gap-4">
            <div className="flex flex-col items-center gap-2 mb-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-md">
                <Image
                  src="/logo-short-dark.svg"
                  alt="Zeeguros Logo"
                  className="h-8 w-8"
                  width={56}
                  height={54}
                />
              </div>
              <h1 className="text-xl font-bold">
                Crear cuenta
              </h1>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nombre <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input placeholder="Introduce tu nombre" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Apellido <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input placeholder="Introduce tu apellido" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Teléfono <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <PhoneInput
                      placeholder="Introduce tu número de teléfono"
                      defaultCountry="ES"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Correo electrónico <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" type="email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contraseña <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <PasswordInput
                      placeholder="••••••••"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirmar contraseña <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <PasswordInput
                      placeholder="••••••••"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex items-center space-x-2 mt-1">
              <Checkbox id="acceptTerms" required />
              <label
                htmlFor="acceptTerms"
                className="text-sm text-muted-foreground"
              >
                Acepto la{" "}
                <Link
                  href="https://zeeguros.com/politica-privacidad/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline underline-offset-4 hover:text-primary"
                >
                  política de privacidad
                </Link>{" "}
                <span className="text-red-500">*</span>
              </label>
            </div>

            <Button
              type="submit"
              variant="default"
              className="w-full mt-2"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Creando cuenta..." : "Crear cuenta"}
            </Button>

            <div className="relative my-4">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  O
                </span>
              </div>
            </div>

            <GoogleSignInButton variant="signup" />

            <div className="text-center text-sm mt-6 mb-2">
              ¿Ya tienes una cuenta?{" "}
              <Link
                href="/login"
                className="underline underline-offset-4 hover:text-primary font-medium"
              >
                Iniciar sesión
              </Link>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
