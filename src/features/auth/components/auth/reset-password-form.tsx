"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { PasswordInput } from "@/components/ui/password-input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import { createClient } from "@/lib/supabase/client";
import { logger } from "@/lib/logger";
// Logo is now in public directory

const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, { message: "La contraseña debe tener al menos 8 caracteres" })
      .regex(/[A-Z]/, {
        message: "La contraseña debe contener al menos una letra mayúscula",
      })
      .regex(/[a-z]/, {
        message: "La contraseña debe contener al menos una letra minúscula",
      })
      .regex(/[0-9]/, {
        message: "La contraseña debe contener al menos un número",
      }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Las contraseñas no coinciden",
    path: ["confirmPassword"],
  });

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export function ResetPasswordForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const [isLoading, setIsLoading] = useState(false);
  const [resetComplete, setResetComplete] = useState(false);
  const { toast } = useToast();
  const supabase = createClient();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  // Check if we have a valid hash or code in the URL
  useEffect(() => {
    const checkResetToken = async () => {
      try {
        // Check for hash in URL or code parameter
        const hash = window.location.hash.substring(1);
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get("code");


        if (!hash && !code) {
          logger.error("No hash or code found in URL");
          toast({
            variant: "destructive",
            title: "Error",
            description: "Enlace de restablecimiento inválido o expirado.",
          });
          setTimeout(() => {
            window.location.href = "/login";
          }, 3000);
        }
      } catch (error) {
        logger.error("Error checking reset token", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Ocurrió un error al verificar el enlace de restablecimiento.",
        });
      }
    };

    checkResetToken();
  }, [toast]);

  async function onSubmit(data: ResetPasswordFormValues) {
    setIsLoading(true);

    try {
      const { error } = await supabase.auth.updateUser({
        password: data.password,
      });

      if (error) {
        logger.error("Error updating password", error);
        let errorMessage = "No se pudo actualizar la contraseña. Por favor, inténtalo de nuevo.";
        if (error.message.includes("New password should be different from the old password")) {
          errorMessage = "La nueva contraseña debe ser diferente a la anterior.";
        }
        toast({
          variant: "destructive",
          title: "Error",
          description: errorMessage,
        });
        return;
      }

      setResetComplete(true);
      toast({
        title: "Contraseña actualizada",
        description: "Tu contraseña ha sido actualizada correctamente. Redirigiendo...",
      });
      
      // Automatically redirect to the dashboard after successful password reset
      setTimeout(() => {
        router.push("/account-holder/policies");
      }, 2000);
    } catch (error) {
      logger.error("Unexpected error updating password", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: `Error inesperado: ${error instanceof Error ? error.message : "Error desconocido"}`,
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <div className="flex flex-col items-center gap-2">
        <div className="flex h-8 w-8 items-center justify-center rounded-md">
          <Image
            src="/logo-short-dark.svg"
            alt="Zeeguros Logo"
            className="h-8 w-8"
            width={56}
            height={54}
          />
        </div>
        <h1 className="text-xl font-bold">Restablecer contraseña</h1>
      </div>
      <div className="space-y-6">
        {!resetComplete ? (
          <>
            <p className="text-center text-sm text-gray-400">
              Ingresa tu nueva contraseña a continuación.
            </p>

            <form
              onSubmit={handleSubmit(onSubmit)}
              className="flex flex-col gap-6"
            >
              <div className="grid gap-2">
                <Label htmlFor="password">
                  Nueva contraseña
                </Label>
                <PasswordInput
                  id="password"
                  placeholder="••••••••"
                  autoCapitalize="none"
                  autoComplete="new-password"
                  autoCorrect="off"
                  disabled={isLoading}
                  {...register("password")}
                />
                {errors.password && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.password.message}
                  </p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="confirmPassword">
                  Confirmar contraseña
                </Label>
                <PasswordInput
                  id="confirmPassword"
                  placeholder="••••••••"
                  autoCapitalize="none"
                  autoComplete="new-password"
                  autoCorrect="off"
                  disabled={isLoading}
                  {...register("confirmPassword")}
                />
                {errors.confirmPassword && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>

              <Button
                type="submit"
                variant="default"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? "Actualizando..." : "Actualizar contraseña"}
              </Button>
            </form>
          </>
        ) : (
          <div className="text-center space-y-4">
            <p className="text-muted-foreground">
              Tu contraseña ha sido actualizada correctamente. Serás redirigido automáticamente a tu cuenta.
            </p>
            <Button
              onClick={() => router.push("/account-holder/policies")}
              variant="default"
              className="w-full"
            >
              Ir a mi cuenta
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
