"use client";

import { useState } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { login } from "@/features/auth/actions/login";
import { signInWithMagicLink } from "@/features/auth/actions/magic-link";
import { useToast } from "@/components/ui/use-toast";
import { GoogleSignInButton } from "./google-sign-in-button";

export function AuthLoginForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const searchParams = useSearchParams();
  const _router = useRouter();
  const [isLoginLoading, setIsLoginLoading] = useState(false);
  const [isMagicLinkLoading, setIsMagicLinkLoading] = useState(false);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const { toast } = useToast();

  // Get the returnUrl from the URL query parameters
  const returnUrl = searchParams.get("returnUrl") ?? "/policies";

  return (
    <div
      className={cn("flex flex-col gap-4 p-6 max-w-md mx-auto", className)}
      {...props}
    >
      <form
        onSubmit={async (e) => {
          e.preventDefault();
          setIsLoginLoading(true);
          
          // Clear any existing errors
          setEmailError(null);
          setPasswordError(null);
          
          const formData = new FormData(e.currentTarget);
          formData.append("returnUrl", returnUrl);
          
          try {
            const result = await login(formData);

            if (result?.error) {
              // Set inline errors based on error message
              if (result.error.includes("correo") || result.error.includes("email") || result.error.includes("no está registrado")) {
                setEmailError(result.error);
                setPasswordError(null);
              } else if (result.error.includes("contraseña") || result.error.includes("password") || result.error.includes("incorrectos")) {
                setPasswordError(result.error);
                setEmailError(null);
              } else {
                // Generic error - show in both fields
                setEmailError(result.error);
                setPasswordError(result.error);
              }

              toast({
                variant: "destructive",
                title: "Credenciales incorrectas",
                description: result.error,
              });
              setIsLoginLoading(false);
            } else {
              toast({
                title: "¡Bienvenido/a de nuevo!",
                description: "Has iniciado sesión correctamente.",
              });
              // Don't set loading to false here - let the redirect happen
            }
          } catch (error) {
            if (!(error instanceof Error && (
              error.message.includes("NEXT_REDIRECT") ||
              error.message.includes("navigation")
            ))) {
              toast({
                variant: "destructive",
                title: "Ha ocurrido un error",
                description: "Ha ocurrido un error inesperado. Por favor, actualiza la página e inténtalo de nuevo.",
              });
              setIsLoginLoading(false);
            }
            // For successful login with redirect, keep loading state
          }
        }}
        className="flex flex-col gap-4"
      >
        <div className="flex flex-col gap-4">
          <div className="flex flex-col items-center gap-2 mb-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-md">
              <Image
                src="/logo-short-dark.svg"
                alt="Zeeguros Logo"
                className="h-8 w-8"
                width={56}
                height={54}
              />
            </div>
            <h1 className="text-xl font-bold">
              Iniciar Sesión
            </h1>
          </div>

          <div className="space-y-2">
            <Label 
              htmlFor="email" 
              className={cn(emailError && "text-destructive")}
            >
              Correo electrónico
            </Label>
            <Input
              id="email"
              name="email"
              placeholder="<EMAIL>"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoginLoading || isMagicLinkLoading}
              required
              onChange={() => {
                // Clear email error when user starts typing
                if (emailError) setEmailError(null);
              }}
              className={cn(emailError && "border-destructive focus-visible:ring-destructive")}
            />
            {emailError && <p className="text-sm text-destructive">{emailError}</p>}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label 
                htmlFor="password"
                className={cn(passwordError && "text-destructive")}
              >
                Contraseña
              </Label>
            </div>
            <PasswordInput
              id="password"
              name="password"
              placeholder="••••••••"
              autoCapitalize="none"
              autoComplete="current-password"
              disabled={isLoginLoading || isMagicLinkLoading}
              required
              onChange={() => {
                // Clear password error when user starts typing
                if (passwordError) setPasswordError(null);
              }}
              className={cn(passwordError && "border-destructive focus-visible:ring-destructive")}
            />
            {passwordError && <p className="text-sm text-destructive">{passwordError}</p>}
            <div className="mt-2 grid grid-cols-2">
              <button
                type="button"
                onClick={async () => {
                  const emailInput = document.getElementById("email") as HTMLInputElement;
                  const email = emailInput.value;
                  if (!email) {
                    setEmailError("Por favor, introduce tu correo electrónico.");
                    setPasswordError(null); // Clear password error when focusing on email
                    return;
                  }
                  // Clear all errors when starting magic link process
                  setEmailError(null);
                  setPasswordError(null);
                  setIsMagicLinkLoading(true);

                  try {
                    const result = await signInWithMagicLink(email);
                    if (result.success) {
                      toast({
                        title: "¡Revisa tu correo!",
                        description: "Te hemos enviado un enlace para iniciar sesión.",
                      });
                    } else {
                      toast({
                        variant: "destructive",
                        title: "Error",
                        description: result.error,
                      });
                    }
                  } catch {
                    toast({
                      variant: "destructive",
                      title: "Error inesperado",
                      description: "No se pudo enviar el enlace. Inténtalo de nuevo.",
                    });
                  } finally {
                    setIsMagicLinkLoading(false);
                  }
                }}
                disabled={isLoginLoading || isMagicLinkLoading}
                className="text-sm text-primary hover:underline text-left disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isMagicLinkLoading ? "Enviando..." : "Acceder con enlace mágico"}
              </button>
              <Link
                href="/forgot-password"
                className="text-sm text-primary hover:underline text-right"
              >
                ¿Olvidaste tu contraseña?
              </Link>
            </div>
          </div>
        </div>

        <Button
          type="submit"
          variant="default"
          className="w-full"
          disabled={isLoginLoading || isMagicLinkLoading}
        >
          {isLoginLoading && (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          )}
          {isLoginLoading ? "Iniciando sesión..." : "Iniciar sesión"}
        </Button>
      </form>

      <div className="relative my-4">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            O
          </span>
        </div>
      </div>

      <GoogleSignInButton />

      <div className="text-center text-sm">
        ¿No tienes una cuenta?{" "}
        <Link href="/signup" className="text-primary hover:underline hover:text-primary">
          Regístrate
        </Link>
      </div>
    </div>
  );
}
