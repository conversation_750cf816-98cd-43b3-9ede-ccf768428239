"use client";

import { useState } from "react";
import Image from "next/image";
import { signInWithGoogle } from "@/features/auth/actions/oauth";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { logger } from "@/lib/logger";

interface GoogleSignInButtonProps {
  variant?: "login" | "signup";
}

export function GoogleSignInButton({ variant = "login" }: GoogleSignInButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const buttonText = {
    login: {
      loading: "Iniciando sesión...",
      default: "Iniciar sesión con Google",
    },
    signup: {
      loading: "Creando cuenta...",
      default: "Crear cuenta con Google",
    },
  };

  async function handleGoogleSignIn() {
    setIsLoading(true);
    try {
      const result = await signInWithGoogle();

      if (!result.success || !result.url) {
        toast({
          variant: "destructive",
          title: "Error",
          description: result.error ?? "Failed to initiate Google sign-in",
        });
        setIsLoading(false);
        return;
      }

      window.location.href = result.url;
    } catch (error) {
      logger.error("Google sign-in error", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred",
      });
      setIsLoading(false);
    }
  }

  return (
    <Button
      variant="default"
      type="button"
      className="w-full"
      onClick={handleGoogleSignIn}
      disabled={isLoading}
    >
      <Image
        src="/icons/google.svg"
        alt="Google logo"
        width={16}
        height={16}
        className="mr-2"
      />
      {isLoading ? buttonText[variant].loading : buttonText[variant].default}
    </Button>
  );
}