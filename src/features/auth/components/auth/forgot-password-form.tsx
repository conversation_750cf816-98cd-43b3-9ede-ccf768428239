"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { <PERSON>ertCircle, ArrowLeft, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import { createClient } from "@/lib/supabase/client";
import { resetPassword } from "@/features/auth/actions/reset-password";
import { logger } from "@/lib/logger";
// Logo is now in public directory

const forgotPasswordSchema = z.object({
  email: z
    .string()
    .email({ message: "Por favor, ingresa un correo electrónico válido" }),
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

// Function to translate auth error messages to Spanish
function translateAuthError(errorMessage: string): string {
  if (errorMessage.includes("For security purposes, you can only request this after")) {
    const match = errorMessage.match(/after (\d+) seconds/);
    const seconds = match ? match[1] : "60";
    return `Por motivos de seguridad, solo puedes solicitar esto después de ${seconds} segundos.`;
  } else if (errorMessage.includes("email rate limit exceeded")) {
    return "Límite de envío de correos excedido. Por favor, espera unos minutos antes de intentar nuevamente.";
  } else if (errorMessage.includes("rate limit exceeded")) {
    return "Límite de intentos excedido. Por favor, espera unos minutos antes de intentar nuevamente.";
  } else if (errorMessage.includes("Invalid login credentials")) {
    return "Credenciales de inicio de sesión inválidas.";
  } else if (errorMessage.includes("Email not confirmed")) {
    return "Correo electrónico no confirmado.";
  } else if (errorMessage.includes("User not found")) {
    return "Usuario no encontrado.";
  }
  return errorMessage; // Return original message if no translation found
}

export function ForgotPasswordForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [userEmail, setUserEmail] = useState("");
  const { toast } = useToast();
  const supabase = createClient();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  async function onSubmit(data: ForgotPasswordFormValues) {
    setIsLoading(true);
    setUserEmail(data.email);

    try {
      // Use consistent URL format for password reset
      const siteUrl = process.env.NEXT_PUBLIC_SITE_URL ?? window.location.origin;
      logger.debug("Password reset redirect URL configured", { redirectUrl: `${siteUrl}/reset-password` });

      const result = await resetPassword(data.email);

      if (!result.success) {
        toast({
          variant: "destructive",
          title: "Error",
          description: result.error,
        });
        return;
      }

      setEmailSent(true);
      toast({
        title: "Correo enviado",
        description:
          "Te hemos enviado un correo con instrucciones para restablecer tu contraseña.",
      });
    } catch (error) {
      logger.error("Unexpected error during password reset", error);
      toast({
        variant: "destructive",
        title: "Error",
        description:
          `Error inesperado: ${error instanceof Error ? error.message : "Error desconocido"}`,
      });
    } finally {
      setIsLoading(false);
    }
  }

  const handleResendEmail = async () => {
    setIsLoading(true);

    try {
      // Use consistent URL format for password reset
      const siteUrl = process.env.NEXT_PUBLIC_SITE_URL ?? window.location.origin;
      logger.debug("Password reset resend redirect URL configured", { redirectUrl: `${siteUrl}/reset-password` });

      const { error } = await supabase.auth.resetPasswordForEmail(userEmail, {
        redirectTo: `${siteUrl}/reset-password`,
      });

      if (error) {
        logger.error("Password reset resend error", error);

        // Translate specific error messages to Spanish
        const errorMessage = translateAuthError(error.message || "Error desconocido");

        toast({
          variant: "destructive",
          title: "Error",
          description: `No se pudo reenviar el correo: ${errorMessage}`,
        });
        return;
      }

      toast({
        title: "Correo reenviado",
        description:
          "Te hemos enviado un nuevo correo con instrucciones para restablecer tu contraseña.",
      });
    } catch (error) {
      logger.error("Unexpected error during password reset resend", error);
      toast({
        variant: "destructive",
        title: "Error",
        description:
          `Error inesperado: ${error instanceof Error ? error.message : "Error desconocido"}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <div className="flex flex-col items-center gap-2">
        <div className="flex h-8 w-8 items-center justify-center rounded-md">
          <Image
            src="/logo-short-dark.svg"
            alt="Zeeguros Logo"
            className="h-8 w-8"
            width={56}
            height={54}
          />
        </div>
        <h1 className="text-xl font-bold">Recuperar contraseña</h1>
        <div className="text-center text-sm">
          <Link
            href="/login"
            className="flex items-center justify-center gap-2 text-muted-foreground hover:text-primary"
          >
            <ArrowLeft className="h-4 w-4" />
            Volver a inicio de sesión
          </Link>
        </div>
      </div>
      <div className="space-y-6">
        {!emailSent ? (
          <>
            <p className="text-center text-sm text-gray-400">
              ¡No te preocupes! Introduce tu dirección de correo electrónico
              a continuación y te enviaremos un enlace para restablecer la
              contraseña.
            </p>

            <form
              onSubmit={handleSubmit(onSubmit)}
              className="flex flex-col gap-6"
            >
              <div className="grid gap-2">
                <Label htmlFor="email">
                  Correo electrónico
                </Label>
                <div className="relative">
                  <Input
                    id="email"
                    placeholder="<EMAIL>"
                    type="email"
                    autoCapitalize="none"
                    autoComplete="email"
                    autoCorrect="off"
                    disabled={isLoading}
                    {...register("email")}
                  />

                  <Mail
                    className="absolute right-3 top-2.5 h-5 w-5 text-muted-foreground"
                  />
                </div>
                {errors.email && (
                  <p
                    className="text-sm text-destructive flex items-center gap-1"
                  >
                    <AlertCircle className="h-4 w-4" />
                    {errors.email.message}
                  </p>
                )}
              </div>

              <Button
                type="submit"
                variant="default"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? "Enviando..." : "Recuperar"}
              </Button>
            </form>
          </>
        ) : (
          <div className="text-center space-y-4">
            <p className="text-muted-foreground">
              Hemos enviado un correo a tu dirección con instrucciones para
              restablecer tu contraseña.
            </p>
            <p className="text-muted-foreground">
              Si no lo recibes en unos minutos, revisa tu carpeta de spam.
            </p>
            <Button
              onClick={handleResendEmail}
              variant="default"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? "Enviando..." : "Reenviar correo"}
            </Button>
            <Button
              variant="outline"
              className="w-full mt-2"
              asChild
            >
              <Link href="/login">
                Volver a inicio de sesión
              </Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
