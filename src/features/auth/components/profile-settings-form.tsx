"use client";

import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";
import { PhoneInput } from "@/components/ui/phone-input";
// ClientPhoneValidation now imported through AuthSchemas
import { createClient } from "@/lib/supabase/client";
import { logger } from "@/lib/logger";
import { AuthSchemas } from "@/lib/schemas";

// Form schema for display only - no validation for existing data
const displayFormSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().email(),
  phone: z.string().optional(),
});

// Use centralized profile update schema with enhanced phone validation
const updateFormSchema = AuthSchemas.ProfileUpdate.extend({
  phone: z.string().optional().refine(
    (phone) => !phone || AuthSchemas.ClientPhoneValidation.safeParse(phone).success,
    {
      message: "El número de teléfono no es válido."
    }
  ),
});

type ProfileFormValues = z.infer<typeof displayFormSchema>;

interface ProfileSettingsFormProps {
  title?: string;
  description?: string;
  additionalFields?: React.ReactNode;
}

/**
 * Profile settings form component for updating user personal information.
 * Uses centralized AuthSchemas.ProfileUpdate for validation with enhanced phone validation.
 * 
 * @param props - Component props
 * @param props.title - Form title (default: "Información Personal")
 * @param props.description - Form description (default: "Actualiza tu información personal.")
 * @param props.additionalFields - Optional additional form fields to render
 * @returns JSX element containing the profile settings form
 */
export function ProfileSettingsForm({
  title = "Información Personal",
  description = "Actualiza tu información personal.",
  additionalFields,
}: ProfileSettingsFormProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const { toast } = useToast();

  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(displayFormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
    },
    mode: "onChange", // Add mode to track changes immediately
  });

  useEffect(() => {
    const getUserProfile = async () => {
      try {
        setIsLoading(true);

        const response = await fetch("/api/profiles", {
          credentials: "include",
        });
        
        if (!response.ok) {
          if (response.status === 401) {
            window.location.href = "/login";
            return;
          }
          throw new Error("Failed to fetch profile");
        }

        const result = await response.json();
        
        if (!result.success || !result.data) {
          throw new Error(result.error ?? "Failed to fetch profile");
        }
        
        // Handle nested data structure (double wrapped response)
        const userData = result.data.success ? result.data.data : result.data;
        
        // Set form values individually, handling null/undefined values
        profileForm.setValue("email", userData.email ?? "");
        profileForm.setValue("firstName", userData.firstName ?? "");
        profileForm.setValue("lastName", userData.lastName ?? "");
        profileForm.setValue("phone", userData.phone ?? "");
        setPhoneNumber(userData.phone ?? "");

      } catch (error) {
        logger.error("Error fetching user profile", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "No se pudo cargar tu perfil. Por favor, inténtalo de nuevo.",
        });
      } finally {
        setIsLoading(false);
      }
    };

    getUserProfile();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run only once on mount

  const onProfileSubmit = async (data: ProfileFormValues) => {
    try {
      setIsSaving(true);

      // Validate with update schema before sending
      const updateData = {
        firstName: data.firstName,
        lastName: data.lastName,
        phone: phoneNumber,
      };

      const validation = updateFormSchema.safeParse(updateData);
      if (!validation.success) {
        // Show validation errors
        Object.entries(validation.error.flatten().fieldErrors).forEach(([field, messages]) => {
          profileForm.setError(field as keyof ProfileFormValues, {
            message: messages?.[0] ?? "Error de validación",
          });
        });
        return;
      }

      const response = await fetch("/api/profiles", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(validation.data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error ?? "Failed to update profile");
      }

      toast({
        title: "Perfil actualizado",
        description: "Tu perfil ha sido actualizado correctamente.",
      });

      // Refresh the Supabase session to update sidebar immediately
      try {
        const supabase = createClient();
        await supabase.auth.refreshSession();
      } catch (refreshError) {
        logger.error("Failed to refresh session", refreshError);
        // Continue anyway - the update was successful
      }

      // Reset form state with updated values to clear dirty state
      profileForm.reset({
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName, 
        phone: phoneNumber,
      }, {
        keepValues: true,
        keepDirty: false,
        keepErrors: false,
      });
      
    } catch (error) {
      logger.error("Error updating profile", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "No se pudo actualizar tu perfil. Por favor, inténtalo de nuevo.",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Configuración de Perfil</CardTitle>
          <CardDescription>Cargando configuración...</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...profileForm}>
          <form
            id="profile-form"
            onSubmit={profileForm.handleSubmit(onProfileSubmit)}
            className="space-y-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={profileForm.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nombre</FormLabel>
                    <FormControl>
                      <Input placeholder="Juan" {...field} value={field.value ?? ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={profileForm.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Apellido</FormLabel>
                    <FormControl>
                      <Input placeholder="Perez" {...field} value={field.value ?? ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={profileForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Correo Electrónico</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="<EMAIL>"
                        disabled
                        {...field}
                        value={field.value ?? ""}
                      />
                    </FormControl>
                    <FormDescription>
                      No puedes cambiar tu correo electrónico.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={profileForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Teléfono</FormLabel>
                    <FormControl>
                      <PhoneInput
                        id="phone"
                        placeholder="Introduce tu número de teléfono"
                        defaultCountry="ES"
                        value={phoneNumber}
                        onChange={(value) => {
                          setPhoneNumber(value || "");
                          field.onChange(value || "");
                        }}
                        disabled={isSaving}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {additionalFields}
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button
          type="submit"
          form="profile-form"
          disabled={isSaving || !profileForm.formState.isDirty}
          variant="default"
        >
          {isSaving && (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          )}
          {isSaving ? "Guardando..." : "Guardar Cambios"}
        </Button>
      </CardFooter>
    </Card>
  );
}