"use client";

import { createContext, useContext, ReactNode } from "react";
import type { User } from "@prisma/client";

interface UserData {
  user: User | null;
  userName: string;
}

const UserDataContext = createContext<UserData | undefined>(undefined);

export function UserDataProvider({
  children,
  user,
  userName,
}: {
  children: ReactNode;
  user: User | null;
  userName: string;
}) {
  return (
    <UserDataContext.Provider value={{ user, userName }}>
      {children}
    </UserDataContext.Provider>
  );
}

export function useUserData() {
  const context = useContext(UserDataContext);
  if (context === undefined) {
    throw new Error("useUserData must be used within a UserDataProvider");
  }
  return context;
}