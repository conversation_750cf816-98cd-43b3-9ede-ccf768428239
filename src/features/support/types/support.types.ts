import { LucideIcon } from "lucide-react";
import { Role } from "@prisma/client";

export type UserRole = Role;

export interface SupportContact {
  id: string;
  title: string;
  description: string;
  icon: LucideIcon;
  buttonText: string;
  buttonHref: string;
  buttonVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  target?: "_blank" | "_self";
  roles: UserRole[];
}

export interface SupportResource {
  id: string;
  title: string;
  description: string;
  icon: LucideIcon;
  buttonText: string;
  buttonHref?: string;
  buttonVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  target?: "_blank" | "_self";
  roles: UserRole[];
}

export interface SupportConfig {
  contacts: SupportContact[];
  resources: SupportResource[];
}

export interface SupportPageProps {
  userRole: UserRole;
  title: string;
  description: string;
}