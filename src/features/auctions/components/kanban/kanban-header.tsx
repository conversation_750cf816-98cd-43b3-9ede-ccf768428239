import type { ComponentProps } from "react";
import { Search, Filter, ArrowUpDown } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

/*
Header Components
*/

export type KanbanBoardHeaderProps = {
  activeTab?: string;
  onTabChange?: (value: string) => void;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  sortBy?: string;
  onSortChange?: (value: string) => void;
  onFiltersClick?: () => void;
};

export function KanbanBoardHeader({
  activeTab = "kanban",
  onTabChange,
  searchValue = "",
  onSearchChange,
  sortBy,
  onSortChange,
  onFiltersClick,
  className,
  ref,
  ...props
}: ComponentProps<"div"> & KanbanBoardHeaderProps) {
  return (
    <div
      className={cn("flex items-center justify-between p-4 border-b bg-background", className)}
      ref={ref}
      {...props}
    >
      {/* Left side - Tabs */}
      <Tabs value={activeTab} onValueChange={onTabChange} className="flex-1">
        <TabsList className="grid w-fit grid-cols-3">
          <TabsTrigger value="kanban">Kanban</TabsTrigger>
          <TabsTrigger 
            value="list" 
            disabled 
            className="disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none"
          >
            Lista
          </TabsTrigger>
          <TabsTrigger 
            value="metrics" 
            disabled 
            className="disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none"
          >
            Métricas
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Right side - Tools */}
      <div className="flex items-center gap-2">
        {/* Sort by */}
        <Select value={sortBy} onValueChange={onSortChange}>
          <SelectTrigger className="w-[140px] h-9">
            <ArrowUpDown className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Ordenar por" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="date">Fecha</SelectItem>
            <SelectItem value="priority">Prioridad</SelectItem>
            <SelectItem value="status">Estado</SelectItem>
            <SelectItem value="assignee">Asignado</SelectItem>
          </SelectContent>
        </Select>

        {/* Filters */}
        <Button
          variant="outline"
          size="sm"
          onClick={onFiltersClick}
          className="h-9 px-3"
        >
          <Filter className="h-4 w-4 mr-2" />
          Filtros
        </Button>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar..."
            value={searchValue}
            onChange={(e) => onSearchChange?.(e.target.value)}
            className="pl-9 w-[200px] h-9"
          />
        </div>
      </div>
    </div>
  );
}

export type KanbanBoardContainerProps = {
  header?: React.ReactNode;
  children: React.ReactNode;
};

export function KanbanBoardContainer({
  header,
  children,
  className,
  ref,
  ...props
}: ComponentProps<"div"> & KanbanBoardContainerProps) {
  return (
    <div
      className={cn("flex flex-col h-full", className)}
      ref={ref}
      {...props}
    >
      {header}
      <div className="flex-1 overflow-hidden py-4">
        {children}
      </div>
    </div>
  );
}