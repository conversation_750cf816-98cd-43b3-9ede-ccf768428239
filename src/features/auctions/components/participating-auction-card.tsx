"use client";

import { ParticipatingAuction } from "../types/kanban";
import { BaseAuctionCard } from "./base/BaseAuctionCard";
import { AuctionCardButton } from "./base/AuctionCardButton";
import { AUCTION_CARD_TYPOGRAPHY } from "../utils/auction-card-utils";

interface ParticipatingAuctionCardProps {
  auction: ParticipatingAuction;
  onModifyBid?: (auctionId: string) => void;
  onOpenDrawer?: (auctionId: string) => void;
  className?: string;
  isDragging?: boolean;
  onDragStart?: () => void;
  onDragEnd?: () => void;
  isNestedInInteractiveElement?: boolean;
}

/**
 * Participating auction card component - refactored to use BaseAuctionCard
 * Eliminates ~95 lines of duplicated code by using shared base component
 */
export function ParticipatingAuctionCard({
  auction,
  onModifyBid,
  onOpenDrawer,
  className,
  isDragging = false,
  onDragStart,
  onDragEnd,
  isNestedInInteractiveElement = false,
}: ParticipatingAuctionCardProps) {
  const handleActionClick = () => {
    onModifyBid?.(auction.id);
    onOpenDrawer?.(auction.id);
  };

  return (
    <BaseAuctionCard
      auctionId={auction.id}
      statusText={auction.timeRemaining}
      statusVariant="default"
      assetType={auction.assetType}
      coverageType={auction.coverageType}
      currentPremium={auction.currentPremium}
      clientName={auction.clientName}
      className={className}
      actionButton={
        <AuctionCardButton
          onClick={handleActionClick}
          isNestedInInteractiveElement={isNestedInInteractiveElement}
          variant="success"
        >
          PUJAR DE NUEVO
        </AuctionCardButton>
      }
    >
      {/* Custom bidding information for participating auctions */}
      <div className="grid grid-cols-2 gap-1 text-[8px] text-gray-700 mt-1 mb-1">
        {/* My Bid */}
        <div className="bg-white border border-gray-200 rounded px-1 py-1 text-center">
          <div className="text-xs text-gray-900 font-semibold">{auction.myBidAmount}€</div>
          <div className="text-xs text-gray-500">Mi Oferta</div>
        </div>

        {/* Leading Bid */}
        <div className="bg-gray-50 border border-gray-100 rounded px-1 py-1 text-center">
          <div className="text-xs text-gray-900 font-semibold">{auction.leadingBid}€</div>
          <div className="text-xs text-gray-500">Líder</div>
        </div>
      </div>
    </BaseAuctionCard>
  );
}