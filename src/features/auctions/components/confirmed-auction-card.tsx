"use client";

import { cn } from "@/lib/utils";
import { ConfirmedAuction } from "../types/kanban";
import {
  AUCTION_CARD_TYPOGRAPHY,
  getAssetTypeText,
  getCoverageTypeDisplay
} from "../utils/auction-card-utils";
import { useState, useEffect } from "react";

interface ConfirmedAuctionCardProps {
  auction: ConfirmedAuction;
  onManageClient?: (auctionId: string) => void;
  onOpenDrawer?: (auctionId: string) => void;
  className?: string;
  isNestedInInteractiveElement?: boolean;
}

export function ConfirmedAuctionCard({
  auction,
  onManageClient,
  onOpenDrawer,
  className,
  isNestedInInteractiveElement = false,
}: ConfirmedAuctionCardProps) {
  const [hasMounted, setHasMounted] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const dragActive = hasMounted && isDragging;
  const getAssetTypeEmoji = (assetType: string) => {
    switch (assetType) {
      case "CAR":
        return "⚖️";
      case "MOTORCYCLE":
        return "⚖️";
      default:
        return "⚖️";
    }
  };

  return (
    <div
      className={cn(
        "relative bg-white rounded-xl p-1.5",
        "cursor-grab active:cursor-grabbing",
        "flex flex-col h-full justify-between min-h-[140px]",
        dragActive && [
          "border-2 border-green-400",
          "shadow-[0_0_20px_rgba(34,197,94,0.3)]",
          "z-50",
        ],
        !dragActive && "border border-gray-200 shadow-sm hover:shadow-md hover:border-gray-300",
        className
      )}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {/* 1. Auction ID and Firmada Status */}
      <div className="flex items-center flex-nowrap gap-x-0.5 mb-0.5">
        <div className="text-xs font-semibold text-gray-900 flex-shrink-0 min-w-0 overflow-hidden text-ellipsis max-w-full">
          {auction.id}
        </div>
        <div className="bg-gray-100 px-1 py-0.5 rounded text-xs text-gray-600 font-medium flex-shrink-0">
          Firmada
        </div>
      </div>

      <div className="flex-grow py-0.5 flex flex-col gap-y-0.5">
        {/* 2. Asset Type */}
        <div className="flex items-center gap-0.5 text-gray-500 text-[9px]">
          <span className="text-[10px]">{getAssetTypeEmoji(auction.assetType)}</span>
          <span className={AUCTION_CARD_TYPOGRAPHY.assetType}>{getAssetTypeText(auction.assetType)}</span>
        </div>

        {/* 3. Coverage Type */}
        <div className="flex items-center gap-0.5 text-gray-500 text-[9px]">
          <span className="text-[10px]">🛡️</span>
          <span className={cn(AUCTION_CARD_TYPOGRAPHY.coverageType, "truncate")}>{getCoverageTypeDisplay(auction.coverageType)}</span>
        </div>

        {/* 4. My AuctionBid */}
        <div className="flex items-center gap-0.5 text-gray-500 text-[9px]">
          <span className="text-[10px]">💰</span>
          <span className={AUCTION_CARD_TYPOGRAPHY.premium}>Mi oferta: <span className="text-green-600">{auction.myBidAmount}€</span></span>
        </div>

        {/* 5. Client Name */}
        <div className="flex items-center gap-0.5 text-gray-500 text-[9px]">
          <span className="text-[10px]">👤</span>
          <span className={AUCTION_CARD_TYPOGRAPHY.clientName}>{auction.clientName}</span>
        </div>
      </div>

      {/* 7. Manage Client Button */}
      {isNestedInInteractiveElement ? (
        <div
          className={cn(
            "w-full bg-green-500 hover:bg-green-600 text-white font-medium py-0.5 px-1 rounded",
            "transition-colors duration-200",
            "mt-0.5 whitespace-nowrap text-xs text-center",
            "cursor-pointer"
           )}
           onClick={(e) => {
             e.preventDefault();
             e.stopPropagation();
             onManageClient?.(auction.id);
             onOpenDrawer?.(auction.id);
           }}
        >
          GESTIONAR CLIENTE
        </div>
      ) : (
        <button
          className={cn(
            "w-full bg-green-500 hover:bg-green-600 text-white font-medium py-0.5 px-1 rounded",
            "transition-colors duration-200",
            "focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1",
            "mt-0.5 whitespace-nowrap text-xs text-center"
          )}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onManageClient?.(auction.id);
            onOpenDrawer?.(auction.id);
          }}
        >
          GESTIONAR CLIENTE
        </button>
      )}
    </div>
  );
}