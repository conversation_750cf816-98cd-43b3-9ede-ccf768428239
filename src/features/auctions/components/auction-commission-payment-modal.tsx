"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { 
  CreditCard, 
  Shield, 
  Lock, 
  Euro, 
  CheckCircle, 
  AlertCircle,
  Phone,
  Mail,
  User,
  Calendar,
  Building
} from "lucide-react";
import { WonAuction } from "../types/kanban";

interface AuctionCommissionPaymentModalProps {
  auction: WonAuction;
  isOpen: boolean;
  onClose: () => void;
  onPaymentComplete?: (auctionId: string) => void;
}

export function AuctionCommissionPaymentModal({ 
  auction, 
  isOpen, 
  onClose, 
  onPaymentComplete 
}: AuctionCommissionPaymentModalProps) {
  const handlePayment = async () => {
    // TODO: Implement actual Stripe payment processing
    // For now, simulate payment success
    setTimeout(() => {
      onPaymentComplete?.(auction.id);
      onClose();
    }, 2000);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Euro className="h-5 w-5 text-green-600" />
            Pago de Comisión
          </DialogTitle>
          <DialogDescription>
            Realiza el pago de comisión para acceder a la información de contacto del cliente
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Auction Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Resumen de la Subasta</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Tipo de cobertura:</span>
                </div>
                <span className="text-sm">{auction.coverageType}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Cliente:</span>
                </div>
                <span className="text-sm">{auction.clientName}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Tiempo restante:</span>
                </div>
                <span className="text-sm">{auction.timeRemaining}</span>
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Tu oferta ganadora:</span>
                <Badge variant="default" className="bg-green-600">
                  €{auction.winningAmount.toLocaleString()}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Comisión Zeeguros (10%):</span>
                <Badge variant="outline" className="text-blue-600 border-blue-600">
                  €{auction.auctionCommissionAmount.toLocaleString()}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Payment Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Información de Pago
              </CardTitle>
              <CardDescription>
                Introduce los datos de tu tarjeta para proceder con el pago
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Card Number */}
              <div className="space-y-2">
                <Label htmlFor="card-number">Número de Tarjeta</Label>
                <Input
                  id="card-number"
                  placeholder="1234 5678 9012 3456"
                  className="text-center tracking-wider"
                />
              </div>

              {/* Expiry and CVC */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expiry">Fecha de Expiración</Label>
                  <Input
                    id="expiry"
                    placeholder="MM/AA"
                    className="text-center"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cvc">CVC</Label>
                  <Input
                    id="cvc"
                    placeholder="123"
                    className="text-center"
                  />
                </div>
              </div>

              {/* Cardholder Name */}
              <div className="space-y-2">
                <Label htmlFor="cardholder">Nombre del Titular</Label>
                <Input
                  id="cardholder"
                  placeholder="Nombre del titular"
                />
              </div>

              {/* Security Notice */}
              <div className="flex items-start gap-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-blue-800">
                    Pago Seguro
                  </p>
                  <p className="text-xs text-blue-700">
                    Tus datos están protegidos con encriptación SSL de 256 bits y procesados por Stripe
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* What You'll Get */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Acceso Inmediato
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className="text-sm text-gray-600">
                  Una vez completado el pago, tendrás acceso inmediato a:
                </p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Número de teléfono del cliente</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Dirección de email del cliente</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Lock className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Acceso exclusivo para contactar al cliente</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Important Notice */}
          <div className="flex items-start gap-3 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium text-yellow-800">
                Importante
              </p>
              <p className="text-xs text-yellow-700">
                El pago de comisión es no reembolsable. Asegúrate de contactar al cliente 
                dentro de las 48 horas posteriores al pago para confirmar la póliza.
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button 
              variant="outline" 
              className="flex-1"
              onClick={onClose}
            >
              Cancelar
            </Button>
            <Button 
              className="flex-1 bg-green-600 hover:bg-green-700"
              onClick={handlePayment}
            >
              <CreditCard className="h-4 w-4 mr-2" />
              Pagar €{auction.auctionCommissionAmount.toLocaleString()}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}