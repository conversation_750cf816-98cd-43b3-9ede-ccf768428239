"use client";

import { AvailableAuction } from "../types/kanban";
import { BaseAuctionCard } from "./base/BaseAuctionCard";
import { AuctionCardButton } from "./base/AuctionCardButton";

interface AvailableAuctionCardProps {
  auction: AvailableAuction;
  onParticipate?: (auctionId: string) => void;
  onOpenDrawer?: (auctionId: string) => void;
  className?: string;
  isNestedInInteractiveElement?: boolean;
}

/**
 * Available auction card component - refactored to use BaseAuctionCard
 * Eliminates ~85 lines of duplicated code by using shared base component
 */
export function AvailableAuctionCard({
  auction,
  onParticipate,
  onOpenDrawer,
  className,
  isNestedInInteractiveElement = false,
}: AvailableAuctionCardProps) {
  const handleActionClick = () => {
    onParticipate?.(auction.id);
    onOpenDrawer?.(auction.id);
  };

  return (
    <BaseAuctionCard
      auctionId={auction.id}
      statusText={auction.timeRemaining}
      statusVariant="default"
      assetType={auction.assetType}
      coverageType={auction.coverageType}
      currentPremium={auction.currentPremium}
      clientName={auction.clientName}
      className={className}
      actionButton={
        <AuctionCardButton
          onClick={handleActionClick}
          isNestedInInteractiveElement={isNestedInInteractiveElement}
          variant="success"
        >
          PUJAR
        </AuctionCardButton>
      }
    />
  );
}