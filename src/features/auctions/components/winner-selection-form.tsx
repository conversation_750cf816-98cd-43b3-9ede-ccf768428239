"use client";

import React, { useState, useTransition } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Auction, Bid } from "@prisma/client";

type AuctionWithBids = Auction & {
  bids: Bid[];
};

interface WinnerSelectionFormProps {
  auction: AuctionWithBids;
}

export function WinnerSelectionForm({ auction }: WinnerSelectionFormProps) {
  const [selectedBids, setSelectedBids] = useState<string[]>([]);
  const [isPending, startTransition] = useTransition();
  const { toast } = useToast();

  const handleCheckboxChange = (bidId: string) => {
    setSelectedBids((prev) => {
      if (prev.includes(bidId)) {
        return prev.filter((id) => id !== bidId);
      }
      if (prev.length < 3) {
        return [...prev, bidId];
      }
      return prev;
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    startTransition(async () => {
      try {
        const response = await fetch(`/api/auctions/${auction.id}/winners`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ selectedBidIds: selectedBids }),
        });

        const result = await response.json();

        if (result.success) {
          toast({
            title: "Éxito",
            description: result.message ?? "¡Ganadores seleccionados con éxito!",
          });
        } else {
          toast({
            variant: "destructive",
            title: "Error",
            description: result.error ?? "Error al seleccionar ganadores.",
          });
        }
      } catch {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Ocurrió un error inesperado.",
        });
      }
    });
  };


  return (
    <Card>
      <form onSubmit={handleSubmit}>
        <CardHeader>
          <CardTitle>Seleccionar Ganadores</CardTitle>
          <p className="text-sm text-gray-500">Elige hasta 3 de las mejores ofertas.</p>
        </CardHeader>
        <CardContent className="space-y-4">
          {auction.bids.map((bid) => (
            <div key={bid.id} className="flex items-center space-x-2 p-2 rounded-md border">
              <Checkbox
                id={bid.id}
                checked={selectedBids.includes(bid.id)}
                onCheckedChange={() => handleCheckboxChange(bid.id)}
                disabled={selectedBids.length >= 3 && !selectedBids.includes(bid.id)}
              />
              <Label htmlFor={bid.id} className="flex-1 text-sm font-medium">
                Oferta de €{bid.amount.toFixed(2)}
              </Label>
            </div>
          ))}
        </CardContent>
        <CardFooter>
          <Button type="submit" disabled={selectedBids.length === 0 || isPending}>
            {isPending ? "Procesando..." : "Confirmar Selección"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}