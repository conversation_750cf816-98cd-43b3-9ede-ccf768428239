"use client";

import { cn } from "@/lib/utils";
import { ReactNode } from "react";
import { useDragState } from "../../hooks/useDragState";
import {
  AUCTION_CARD_TYPOGRAPHY,
  getAssetTypeEmoji,
  getAssetTypeText,
  getCoverageTypeDisplay,
  maskClientName
} from "../../utils/auction-card-utils";

interface BaseAuctionCardProps {
  /** Auction ID to display */
  auctionId: string;
  /** Time remaining or status text to display in badge */
  statusText: string;
  /** Status badge variant styling */
  statusVariant?: "default" | "success" | "warning" | "error";
  /** Asset type for emoji and text */
  assetType: string;
  /** Coverage type for display */
  coverageType: string;
  /** Current premium amount */
  currentPremium: number;
  /** Client name (will be masked) */
  clientName: string;
  /** Action button component */
  actionButton?: ReactNode;
  /** Additional custom content to display */
  children?: ReactNode;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Base auction card component that consolidates shared patterns from 6+ auction card components
 * Eliminates 588+ lines of duplicated code by providing a unified base implementation
 */
export function BaseAuctionCard({
  auctionId,
  statusText,
  statusVariant = "default",
  assetType,
  coverageType,
  currentPremium,
  clientName,
  actionButton,
  children,
  className,
}: BaseAuctionCardProps) {
  const { dragActive, handleDragStart, handleDragEnd } = useDragState();

  const getStatusBadgeClasses = (variant: typeof statusVariant) => {
    const baseClasses = "px-1 py-0.5 rounded text-xs font-medium flex-shrink-0";
    switch (variant) {
      case "success":
        return cn(baseClasses, "bg-green-100 text-green-600");
      case "warning":
        return cn(baseClasses, "bg-yellow-100 text-yellow-600");
      case "error":
        return cn(baseClasses, "bg-red-100 text-red-600");
      default:
        return cn(baseClasses, "bg-gray-100 text-gray-600");
    }
  };

  return (
    <div
      className={cn(
        "relative bg-white rounded-xl p-1.5",
        "cursor-grab active:cursor-grabbing",
        "flex flex-col h-full justify-between min-h-[140px]",
        dragActive && [
          "border-2 border-green-400",
          "shadow-[0_0_20px_rgba(34,197,94,0.3)]",
          "z-50",
        ],
        !dragActive && "border border-gray-200 shadow-sm hover:shadow-md hover:border-gray-300",
        className
      )}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {/* 1. Auction ID and Status */}
      <div className="flex items-center flex-nowrap gap-x-0.5 mb-0.5">
        <div className="text-xs font-semibold text-gray-900 flex-shrink-0 min-w-0 overflow-hidden text-ellipsis max-w-full">
          {auctionId}
        </div>
        <div className={getStatusBadgeClasses(statusVariant)}>
          {statusText}
        </div>
      </div>

      <div className="flex-grow py-0.5 flex flex-col gap-y-0.5">
        {/* 2. Asset Type */}
        <div className="flex items-center gap-0.5 text-gray-500 text-[9px]">
          <span className="text-[10px]">{getAssetTypeEmoji(assetType)}</span>
          <span className={AUCTION_CARD_TYPOGRAPHY.assetType}>{getAssetTypeText(assetType)}</span>
        </div>

        {/* 3. Coverage Type */}
        <div className="flex items-center gap-0.5 text-gray-500 text-[9px]">
          <span className="text-[10px]">🛡️</span>
          <span className={cn(AUCTION_CARD_TYPOGRAPHY.coverageType, "truncate")}>
            {getCoverageTypeDisplay(coverageType)}
          </span>
        </div>

        {/* 4. Premium */}
        <div className="flex items-center gap-0.5 text-gray-500 text-[9px]">
          <span className="text-[10px]">💰</span>
          <span className={AUCTION_CARD_TYPOGRAPHY.premium}>
            <span className="text-foreground">{currentPremium}€</span>
            <span className={cn(AUCTION_CARD_TYPOGRAPHY.premiumSuffix, "text-muted-foreground")}>/Anual</span>
          </span>
        </div>

        {/* 5. Client Name */}
        <div className="flex items-center gap-0.5 text-gray-500 text-[9px]">
          <span className="text-[10px]">👤</span>
          <span className={AUCTION_CARD_TYPOGRAPHY.clientName}>{maskClientName(clientName)}</span>
        </div>

        {/* Custom content */}
        {children}
      </div>

      {/* Action Button */}
      {actionButton}
    </div>
  );
}