"use client";

import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface AuctionCardButtonProps {
  /** Button text or content */
  children: ReactNode;
  /** Click handler */
  onClick: (e: React.MouseEvent) => void;
  /** Whether this button is nested in an interactive element (affects rendering) */
  isNestedInInteractiveElement?: boolean;
  /** Button variant styling */
  variant?: "primary" | "secondary" | "success" | "warning" | "error";
  /** Whether button is disabled */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Reusable action button component for auction cards
 * Consolidates duplicated button patterns from 5+ auction card components
 */
export function AuctionCardButton({
  children,
  onClick,
  isNestedInInteractiveElement = false,
  variant = "success",
  disabled = false,
  className,
}: AuctionCardButtonProps) {
  const getVariantClasses = (variantType: "primary" | "secondary" | "success" | "warning" | "error") => {
    switch (variantType) {
      case "primary":
        return "bg-blue-500 hover:bg-blue-600 focus:ring-blue-300";
      case "secondary":
        return "bg-gray-500 hover:bg-gray-600 focus:ring-gray-300";
      case "success":
        return "bg-green-500 hover:bg-green-600 focus:ring-green-300";
      case "warning":
        return "bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-300";
      case "error":
        return "bg-red-500 hover:bg-red-600 focus:ring-red-300";
      default:
        return "bg-green-500 hover:bg-green-600 focus:ring-green-300";
    }
  };

  const baseClasses = cn(
    "w-full text-white font-medium py-0.5 px-1 rounded-md text-xs text-center",
    "transition-colors duration-200 mt-0.5",
    getVariantClasses(variant),
    disabled && "opacity-50 cursor-not-allowed",
    className
  );

  const handleClick = (e: React.MouseEvent) => {
    if (disabled) return;
    e.preventDefault();
    e.stopPropagation();
    onClick(e);
  };

  // For nested interactive elements, use div to avoid button-in-button issues
  if (isNestedInInteractiveElement) {
    return (
      <div
        className={cn(baseClasses, !disabled && "cursor-pointer")}
        onClick={handleClick}
        role="button"
        tabIndex={disabled ? -1 : 0}
        onKeyDown={(e) => {
          if (!disabled && (e.key === "Enter" || e.key === " ")) {
            e.preventDefault();
            // Call handleClick which properly handles the event
            handleClick(e as unknown as React.MouseEvent);
          }
        }}
      >
        {children}
      </div>
    );
  }

  // Standard button element
  return (
    <button
      className={cn(
        baseClasses,
        "focus:outline-none focus:ring-2 focus:ring-offset-1"
      )}
      onClick={handleClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}