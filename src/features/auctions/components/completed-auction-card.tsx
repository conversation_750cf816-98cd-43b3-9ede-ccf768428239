"use client";

import React from "react";

interface CompletedAuctionCardProps {
  clientName: string;
  coverageType: string;
  price: number;
  status: "won" | "lost";
}

export function CompletedAuctionCard({
  clientName,
  coverageType,
  price,
  status,
}: CompletedAuctionCardProps) {
  return (
    <div
      className={`p-4 border rounded-lg ${
        status === "won"
          ? "border-l-4 border-l-green-500"
          : "border-l-4 border-l-red-500"
      }`}
    >
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-medium">{clientName}</h3>
          <p className="text-sm text-muted-foreground">{coverageType}</p>
        </div>
        <div className="text-right">
          <div
            className={`text-xs font-medium px-2 py-1 rounded ${
              status === "won"
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            }`}
          >
            {status === "won" ? "Ganada" : "Perdida"}
          </div>
          <div className="mt-2 font-bold">{price}€</div>
        </div>
      </div>
    </div>
  );
}