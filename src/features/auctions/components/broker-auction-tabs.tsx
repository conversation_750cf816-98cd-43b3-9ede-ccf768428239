"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { ZBadge } from "@/components/shared/z-badge";
import { Car } from "lucide-react";
import { AuctionCard, AuctionCardData } from "@/features/auctions/components/auction-card";
import { CompletedAuctionCard } from "./completed-auction-card";

// Empty arrays - data will come from API
const mockAuctions: AuctionCardData[] = [];
const mockCompletedAuctions: {
  clientName: string;
  coverageType: string;
  price: number;
  status: "won" | "lost";
}[] = [];

export function BrokerAuctionTabs() {
  const [_activeTab, setActiveTab] = useState("active");

  return (
    <Tabs
      defaultValue="active"
      className="w-full"
      onValueChange={setActiveTab}
    >
      <div className="flex justify-between items-center mb-4">
        <TabsList>
          <TabsTrigger value="active">Subastas Activas</TabsTrigger>
          <TabsTrigger value="completed">Completadas</TabsTrigger>
          <TabsTrigger value="leads">Leads</TabsTrigger>
        </TabsList>
        <Button>Nueva Subasta</Button>
      </div>

      <TabsContent value="active" className="mt-0">
        <div className="flex gap-2 mb-4">
          <ZBadge variant="default" size="md">
            Todas ({mockAuctions.length})
          </ZBadge>
          <ZBadge variant="info" size="md">
            <Car className="mr-1 h-3 w-3" />
            Auto ({mockAuctions.length})
          </ZBadge>
          <ZBadge variant="warning" size="md">
            Urgentes (2)
          </ZBadge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {mockAuctions.map((auction) => (
            <AuctionCard
              key={auction.id}
              auction={auction}
              className="cursor-pointer"
            />
          ))}
        </div>
      </TabsContent>

      <TabsContent value="completed" className="mt-0">
        <div className="flex gap-2 mb-4">
          <ZBadge variant="default" size="md">
            Todas ({mockCompletedAuctions.length})
          </ZBadge>
          <ZBadge variant="success" size="md">
            Ganadas (2)
          </ZBadge>
          <ZBadge variant="danger" size="md">
            Perdidas (1)
          </ZBadge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {mockCompletedAuctions.map((auction, index) => (
            <CompletedAuctionCard
              key={`completed-${index}`}
              clientName={auction.clientName}
              coverageType={auction.coverageType}
              price={auction.price}
              status={auction.status}
            />
          ))}
        </div>
      </TabsContent>

      <TabsContent value="leads" className="mt-0">
        <div className="p-8 text-center text-muted-foreground">
          <p>No hay leads disponibles actualmente.</p>
          <Button variant="outline" className="mt-4">
            Importar Leads
          </Button>
        </div>
      </TabsContent>
    </Tabs>
  );
}