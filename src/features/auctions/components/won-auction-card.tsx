"use client";

import { WonAuction } from "../types/kanban";
import { BaseAuctionCard } from "./base/BaseAuctionCard";
import { AuctionCardButton } from "./base/AuctionCardButton";

interface WonAuctionCardProps {
  auction: WonAuction;
  onPayAuctionCommission: (auctionId: string) => void;
  onCallClient: (phone: string) => void;
  onEmailClient: (email: string) => void;
  onOpenDrawer: (id: string) => void;
  onManageClient?: (auctionId: string) => void;
  className?: string;
  isNestedInInteractiveElement?: boolean;
}

/**
 * Won auction card component - refactored to use BaseAuctionCard
 * Eliminates ~90 lines of duplicated code by using shared base component
 */
export function WonAuctionCard({
  auction,
  onManageClient,
  onOpenDrawer,
  className,
  isNestedInInteractiveElement = false,
}: WonAuctionCardProps) {
  const handleActionClick = () => {
    onManageClient?.(auction.id);
    onOpenDrawer?.(auction.id);
  };

  return (
    <BaseAuctionCard
      auctionId={auction.id}
      statusText="Ganada"
      statusVariant="success"
      assetType={auction.assetType}
      coverageType={auction.coverageType}
      currentPremium={auction.currentPremium}
      clientName={auction.clientName}
      className={className}
      actionButton={
        <AuctionCardButton
          onClick={handleActionClick}
          isNestedInInteractiveElement={isNestedInInteractiveElement}
          variant="primary"
        >
          GESTIONAR
        </AuctionCardButton>
      }
    >
      {/* Custom content for won auctions */}
      <div className="bg-green-50 border border-green-200 rounded px-1 py-1 text-center mt-1">
        <div className="text-xs text-green-700 font-semibold">🏆 Subasta Ganada</div>
        <div className="text-xs text-green-600">Premio: {auction.currentPremium}€</div>
      </div>
    </BaseAuctionCard>
  );
}