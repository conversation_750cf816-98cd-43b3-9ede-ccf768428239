import { db } from "@/lib/db";
import { AuctionStateEnum, type Auction, type Prisma } from "@prisma/client";
import { calculateWorkingHoursClosedAt, DEFAULT_AUCTION_DURATION_HOURS } from "@/lib/auction/working-hours";
import { AuctionStateService } from "@/lib/services/auction-state.service";

export const AuctionService = {
  // Create a new auction
  async create(data: Prisma.AuctionCreateInput): Promise<Auction> {
    const auction = await db.auction.create({
      data,
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });

    // Create initial state history entry
    await AuctionStateService.createState(auction.id, AuctionStateEnum.OPEN);

    return auction;
  },

  // Get an auction by ID with current state
  async getById(id: string): Promise<(Auction & { currentState?: AuctionStateEnum }) | null> {
    const auction = await db.auction.findUnique({
      where: { id },
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });

    if (!auction) return null;

    // Get current state from state history
    try {
      const currentState = await AuctionStateService.getCurrentState(id);
      return { ...auction, currentState };
    } catch {
      // If no state history exists, return auction without currentState
      return auction;
    }
  },

  // Get auctions by account holder ID
  async getByAccountHolderId(accountHolderId: string): Promise<Auction[]> {
    return db.auction.findMany({
      where: { accountHolderId },
      include: {
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Get active auctions
  async getActive(): Promise<Auction[]> {
    const now = new Date();
    
    // Get auctions that are currently in OPEN state
    const openAuctionIds = await AuctionStateService.getAuctionsByCurrentState(AuctionStateEnum.OPEN);
    
    if (openAuctionIds.length === 0) {
      return [];
    }
    
    return db.auction.findMany({
      where: {
        id: { in: openAuctionIds },
        endDate: {
          gt: now
        }
      },
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Update an auction
  async update(id: string, data: Prisma.AuctionUpdateInput): Promise<Auction> {
    return db.auction.update({
      where: { id },
      data,
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Close an auction
  async close(id: string): Promise<Auction> {
    // Transition to CLOSED state
    await AuctionStateService.transitionToState(id, AuctionStateEnum.CLOSED);
    
    return db.auction.findUniqueOrThrow({
      where: { id },
      include: {
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Delete an auction
  async delete(id: string): Promise<Auction> {
    return db.auction.delete({
      where: { id }
    });
  },

  // List auctions with pagination and optional filters
  async list(params: {
    skip?: number;
    take?: number;
    where?: Prisma.AuctionWhereInput;
    orderBy?: Prisma.AuctionOrderByWithRelationInput;
  }): Promise<{ auctions: Auction[]; total: number }> {
    const { skip = 0, take = 10, where, orderBy } = params;

    const [auctions, total] = await Promise.all([
      db.auction.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          accountHolder: true,
          policy: true,
          bids: {
            include: {
              broker: true
            }
          }
        }
      }),
      db.auction.count({ where })
    ]);

    return { auctions, total };
  },

  // Create an auction from a policy
  async createAuctionFromPolicy(policyId: string): Promise<Auction> {
    const policy = await db.policy.findUnique({
      where: { id: policyId },
      include: {
        accountHolder: true
      }
    });

    if (!policy || !policy.accountHolder || !policy.accountHolderId) {
      throw new Error("Policy or account holder not found for the given policy ID.");
    }

    const startDate = new Date();
    // Calculate end date using working hours business logic (48 working hours, Mon-Fri 06:00-23:59 Madrid time)
    const endDate = calculateWorkingHoursClosedAt(startDate, DEFAULT_AUCTION_DURATION_HOURS);

    const auctionData: Prisma.AuctionCreateInput = {
      accountHolder: { connect: { id: policy.accountHolderId } },
      policy: { connect: { id: policyId } },
      startDate,
      endDate,
    };

    const auction = await db.auction.create({ data: auctionData });
    
    // Create initial state history entry
    await AuctionStateService.createState(auction.id, AuctionStateEnum.OPEN);
    
    return auction;
  },

  // Cancel an auction
  async cancel(id: string, reason?: string): Promise<Auction> {
    // Transition to CANCELED state
    await AuctionStateService.transitionToState(id, AuctionStateEnum.CANCELED);
    
    // Update cancellation reason if provided
    const updateData: Prisma.AuctionUpdateInput = {};
    if (reason) {
      updateData.cancellationReason = reason;
    }
    
    return db.auction.update({
      where: { id },
      data: updateData,
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Mark auction as signed (policy uploaded)
  async markAsSigned(id: string): Promise<Auction> {
    // Transition to SIGNED_POLICY state
    await AuctionStateService.transitionToState(id, AuctionStateEnum.SIGNED_POLICY);
    
    return db.auction.findUniqueOrThrow({
      where: { id },
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Mark auction as expired
  async markAsExpired(id: string): Promise<Auction> {
    // Transition to EXPIRED state
    await AuctionStateService.transitionToState(id, AuctionStateEnum.EXPIRED);
    
    return db.auction.findUniqueOrThrow({
      where: { id },
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  },

  // Get current state of an auction
  async getCurrentState(id: string): Promise<AuctionStateEnum> {
    return AuctionStateService.getCurrentState(id);
  },

  // Get state history of an auction
  async getStateHistory(id: string) {
    return AuctionStateService.getStateHistory(id);
  },

  // Get auctions by current state
  async getByCurrentState(state: AuctionStateEnum): Promise<Auction[]> {
    const auctionIds = await AuctionStateService.getAuctionsByCurrentState(state);
    
    if (auctionIds.length === 0) {
      return [];
    }
    
    return db.auction.findMany({
      where: {
        id: { in: auctionIds }
      },
      include: {
        accountHolder: true,
        policy: true,
        bids: {
          include: {
            broker: true
          }
        }
      }
    });
  }
};

// Note: Auction expiration is now handled by Supabase cron jobs
// The closeExpiredAuctions function has been removed as it's replaced by:
// - Database cron job: 'close-expired-auctions' (runs every 5 minutes)
// - Notification cron job: 'auction-notifications' (runs every 5 minutes)
// See supabase/migrations/ for the implementation
//
// Migration Note: This service now uses AuctionStateService for state management
// All state transitions are tracked in the auction_state table with timestamps
// The old 'status' field and related timestamp fields have been removed from the Auction model