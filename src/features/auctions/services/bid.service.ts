import { db } from "@/lib/db";
import type { Bid, Prisma } from "@prisma/client";

export const BidService = {
  // Create a new bid
  async create(data: Prisma.BidCreateInput): Promise<Bid> {
    return db.bid.create({
      data,
      include: {
        auction: true,
        broker: true
      }
    });
  },

  // Get a bid by ID
  async getById(id: string): Promise<Bid | null> {
    return db.bid.findUnique({
      where: { id },
      include: {
        auction: true,
        broker: true
      }
    });
  },

  // Get bids by auction ID
  async getByAuctionId(auctionId: string): Promise<Bid[]> {
    return db.bid.findMany({
      where: { auctionId },
      include: {
        broker: true
      },
      orderBy: {
        amount: "desc"
      }
    });
  },

  // Get bids by broker ID
  async getByBrokerId(brokerId: string): Promise<Bid[]> {
    return db.bid.findMany({
      where: { brokerId },
      include: {
        auction: {
          include: {
            policy: true
          }
        }
      }
    });
  },

  // Update a bid
  async update(id: string, data: Prisma.BidUpdateInput): Promise<Bid> {
    return db.bid.update({
      where: { id },
      data,
      include: {
        auction: true,
        broker: true
      }
    });
  },

  // Delete a bid
  async delete(id: string): Promise<Bid> {
    return db.bid.delete({
      where: { id }
    });
  },

  // Get highest bid for an auction
  async getHighestBid(auctionId: string): Promise<Bid | null> {
    return db.bid.findFirst({
      where: { auctionId },
      orderBy: {
        amount: "desc"
      },
      include: {
        broker: true
      }
    });
  },

  // List bids with pagination and optional filters
  async list(params: {
    skip?: number;
    take?: number;
    where?: Prisma.BidWhereInput;
    orderBy?: Prisma.BidOrderByWithRelationInput;
  }): Promise<{ bids: Bid[]; total: number }> {
    const { skip = 0, take = 10, where, orderBy } = params;
    
    const [bids, total] = await Promise.all([
      db.bid.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          auction: true,
          broker: true
        }
      }),
      db.bid.count({ where })
    ]);

    return { bids, total };
  }
};