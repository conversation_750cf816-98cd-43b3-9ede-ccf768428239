import { AssetType } from "@prisma/client";

// Base auction interface
export interface BaseAuction {
  id: string;
  policyNumber: string;
  clientName: string;
  assetType: AssetType;
  brand: string;
  model: string;
  year: number;
  currentPremium: number;
  coverageType: "COMPREHENSIVE" | "THIRD_PARTY" | "THIRD_PARTY_EXTENDED" | "COMPREHENSIVE_WITH_DEDUCTIBLE";
  location: string;
  timeRemaining: string;
  participantCount: number;
  urgency: "high" | "medium" | "low" | "completed";
  hasContactInfo: boolean;
}

// Available auction (DISPONIBLES column)
export interface AvailableAuction extends BaseAuction {
  type: "available";
}

// Participating auction (PARTICIPANDO column)
export interface ParticipatingAuction extends BaseAuction {
  type: "participating";
  myBidAmount: number;
  myBidRank: number;
  leadingBid: number;
}

// Won auction (GANADAS column)
export interface WonAuction extends BaseAuction {
  type: "won";
  myBidAmount: number;
  winningAmount: number;
  auctionCommissionAmount: number;
  contactRevealRequired: boolean;
  clientPhone?: string;
  clientEmail?: string;
}

// Confirmed auction (CONFIRMADAS column)
export interface ConfirmedAuction extends BaseAuction {
  type: "confirmed";
  myBidAmount: number;
  winningAmount: number;
  auctionCommissionAmount: number;
  contactRevealRequired: false;
  clientPhone: string;
  clientEmail: string;
  licensePlate: string;
  dealConfirmedAt: string;
}

// Lost auction (PERDIDAS column)
export interface LostAuction extends BaseAuction {
  type: "lost";
  myBidAmount: number;
  winningAmount: number;
  lostBy: number;
}

// Union type for all auction types
export type BrokerAuction = AvailableAuction | ParticipatingAuction | WonAuction | ConfirmedAuction | LostAuction;

// Column type for Kanban board
export type AuctionColumn = "disponibles" | "participando" | "ganadas" | "confirmadas" | "perdidas";