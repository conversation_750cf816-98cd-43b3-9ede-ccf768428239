/**
 * Shared type definitions for auction-related components
 * Uses Prisma schema types as single source of truth per CLAUDE.md
 */
import { AuctionStateEnum, InsurerCompany, GuaranteeType } from "@prisma/client";


export interface AuctionBid {
  id: string;
  annualPremium: number;
  brokerName: string;
  brokerCompany: string;
  createdAt: string;
  hasDocument: boolean;
  bidCoverages?: unknown[];
  brokerPhone?: string | null;
  brokerEmail?: string | null;
  brokerIdentifier?: string | null;
  quoteDocument?: {
    id: string;
    fileName: string | null;
    fileSize: number | null;
    uploadedAt: string;
    url: string;
  } | null;
}

export interface BidDetail {
  id: string;
  brokerName: string;
  annualPremium?: number;
  createdAt: Date | string;
}

export interface TimelineEvent {
  id: string;
  title: string;
  time: string;
  status: "completed" | "pending";
  bidDetails?: BidDetail[];
}

export interface ExtractedPolicy {
  id: string;
  premium: number;
  insurerCompany: InsurerCompany;
  endDate: string;
  policyNumber: string;
}

export interface NewPolicyDocument {
  id: string;
  fileName: string | null;
  extractedPolicy?: ExtractedPolicy;
}

export interface AuctionDetails {
  id: string;
  identifier: string;
  status: AuctionStateEnum;
  startDate: string | null;
  endDate: string | null;
  annualPremium: number;
  currency: string;
  currentInsurer: string | null;
  assetDisplayName: string;
  assetType: string | null;
  quotesReceived: number;
  bids: AuctionBid[];
  policy: {
    id: string;
    policyNumber: string;
    insurerCompany: string;
    premium: number;
    startDate: string | null;
    endDate: string | null;
    productName: string | null;
    asset: {
      id: string;
      assetType: string;
      description: string;
      vehicleDetails: {
        brand: string;
        model: string;
        year: number;
        licensePlate: string;
        chassisNumber: string;
        firstRegistrationDate: string | null;
        version: string | null;
        fuelType: string | null;
        powerCv: number | null;
        seats: number | null;
        usageType: string | null;
        garageType: string | null;
        kmPerYear: number | null;
        isLeased: boolean | null;
      } | null;
    } | null;
    insuredParties: {
      id: string;
      fullName: string;
      firstName: string;
      lastName: string;
      identification: string;
      roles: string[];
      gender: string;
      email: string;
      phone: string;
      birthDate: string | null;
      address: string;
      postalCode: string;
      regionName: string;
      country: string;
    }[];
    coverages: {
      id: string;
      customName?: string | null;
      guaranteeType: GuaranteeType;
      limit?: number | null;
      description?: string | null;
      limitIsUnlimited?: boolean | null;
      limitIsFullCost?: boolean | null;
      limitPerDay?: number | null;
      limitMaxDays?: number | null;
      limitMaxMonths?: number | null;
      liabilityBodilyCap?: number | null;
      liabilityPropertyCap?: number | null;
      deductible?: number | null;
      deductiblePercent?: number | null;
    }[];
    document?: {
      id: string;
      fileName: string | null;
      fileSize: number | null;
      mimeType: string | null;
      url: string;
      uploadedAt: string;
    } | null;
  } | null; // Policy data from API
  selectedBid?: AuctionBid | null; // Selected bid for SIGNED_POLICY auctions
  newPolicyDocument?: {
    id: string;
    fileName: string | null;
    extractedPolicy?: {
      id: string;
      premium: number;
      insurerCompany: string;
      endDate: string;
      policyNumber: string;
    };
  } | null; // New policy document with extracted data for SIGNED_POLICY auctions
  events: TimelineEvent[];
}