"use client";

import { useState, useEffect } from "react";

/**
 * Shared hook for drag and drop state management across auction card components
 * Consolidates duplicated drag state logic from 5+ auction card components
 */
export function useDragState() {
  const [hasMounted, setHasMounted] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const dragActive = hasMounted && isDragging;

  return {
    hasMounted,
    isDragging,
    dragActive,
    handleDragStart,
    handleDragEnd,
  };
}