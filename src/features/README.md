# Features Directory - Screaming Architecture

This directory organizes code by **business domains** following Uncle <PERSON>'s **Screaming Architecture** principles. The structure immediately reveals that this is an **Insurance Marketplace Platform** focused on **policies** and **auctions**.

## 🎯 Business Domain Organization

Each feature represents a **business domain**, not a technical layer or user role:

```
features/
├── 🏛️ policies/        # Insurance policy management
├── 🎯 auctions/        # Auction marketplace for policies  
├── 👤 profiles/        # User profile management
├── 👥 clients/         # Client relationship management
├── 🔐 auth/            # Authentication & authorization
├── ⚙️ settings/        # Application settings
└── 🆘 support/         # Customer support functionality
```

## 📁 Standard Feature Structure

Every feature follows the **same consistent structure**:

```
feature-name/
├── actions/           # Server actions & form handling
├── components/        # React components specific to this domain
├── config/            # Feature-specific configuration
├── hooks/             # Custom React hooks for this domain
├── schemas/           # Zod validation schemas
├── services/          # Business logic & external API calls  
├── types/             # TypeScript type definitions
└── utils/             # Pure utility functions
```

### 📝 Directory Descriptions

| Directory | Purpose | Examples |
|-----------|---------|----------|
| `actions/` | Server actions, form submissions | `createPolicy.ts`, `placeBid.ts` |
| `components/` | React UI components | `PolicyCard.tsx`, `AuctionList.tsx` |
| `config/` | Feature configuration | `policyTypes.ts`, `auctionSettings.ts` |
| `hooks/` | React hooks for data fetching | `usePolicies.ts`, `useAuctions.ts` |
| `schemas/` | Zod validation schemas | `policySchema.ts`, `bidSchema.ts` |
| `services/` | Business logic services | `policyService.ts`, `auctionService.ts` |
| `types/` | TypeScript interfaces | `Policy.ts`, `Auction.ts` |
| `utils/` | Pure utility functions | `formatCurrency.ts`, `calculatePremium.ts` |

## 🔗 Relationship with Shared Code

Features contain **domain-specific** code, while **shared** code lives in:

- `src/lib/` - Shared utilities, database, configurations
- `src/components/ui/` - Generic UI components (buttons, inputs, etc.)
- `src/components/shared/` - App-wide components used across domains

## 🎨 Role-Aware Components

Components in features handle **multiple user roles** internally rather than being duplicated:

```typescript
// ✅ CORRECT - Single component, role-aware
// features/policies/components/PolicyCard.tsx
export function PolicyCard({ policy, userRole }: Props) {
  switch(userRole) {
    case Role.ACCOUNT_HOLDER:
      return <OwnerView policy={policy} />;
    case Role.BROKER: 
      return <BrokerView policy={policy} />;
    case Role.ADMIN:
      return <AdminView policy={policy} />;
  }
}
```

```typescript
// ❌ WRONG - Role-based directories (violates Screaming Architecture)
// features/account-holder/components/PolicyCard.tsx
// features/broker/components/PolicyCard.tsx  
// features/admin/components/PolicyCard.tsx
```

## 🚀 Adding New Features

When adding a new business domain (e.g., `claims`, `renewals`):

1. **Create directory**: `features/new-domain/`
2. **Add standard structure**: Use the 8 standard directories
3. **Add .gitkeep files**: For empty directories
4. **Follow naming**: Domain-focused, not role-focused
5. **Update this README**: Document the new domain

```bash
# Example: Adding claims feature
mkdir -p features/claims/{actions,components,config,hooks,schemas,services,types,utils}
touch features/claims/{actions,config,hooks,schemas,services,types,utils}/.gitkeep
```

## 🏗️ Architecture Benefits

This structure **SCREAMS** the business purpose:

- ✅ **New developers immediately understand**: "This is an insurance platform"
- ✅ **Business domains are clear**: Policies, auctions, profiles
- ✅ **Maintainable**: Changes to policy logic happen in one place
- ✅ **Scalable**: Easy to add new domains (claims, renewals, etc.)
- ✅ **DRY**: No duplication across roles
- ✅ **Uncle Bob Compliant**: Architecture reveals use cases, not frameworks

---

**Remember**: Organize by **WHAT** the system does (business domains), not **WHO** uses it (user roles). 🎯