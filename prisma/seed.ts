import { PrismaClient, Prisma, Role, Gender, InsurerCompany, PolicyStateEnum, AuctionStateEnum, DocumentType, PaymentPeriod, GuaranteeType } from '@prisma/client';
import { createClient } from '@supabase/supabase-js';

import { calculateWorkingHoursClosedAt, DEFAULT_AUCTION_DURATION_HOURS } from '../src/lib/auction/working-hours';
import { AuctionStateService } from '../src/lib/services/auction-state.service';
import { PolicyStateService } from '../src/lib/services/policy-state.service';
import { allEnumCoverages } from './sample-structered-coverages';


const prisma = new PrismaClient();

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Function to get simplified coverage data (20 coverages per policy)
function getSimplifiedCoverages() {
  // Use only the first 20 coverages from the comprehensive set
  return allEnumCoverages.slice(0, 20);
}

// Normalization function for mandatory liability coverage
function normalizeMandatoryLiability(coverage: any) {
  if (coverage.type === 'MANDATORY_LIABILITY') {
    return {
      ...coverage,
      liabilityBodilyCap: 70000000,
      liabilityPropertyCap: 15000000,
    };
  }
  return coverage;
}

// Helper function to create bid coverages efficiently
async function createBidCoveragesForBid(
  prisma: any,
  bidId: string,
  policyCoverages: any[],
  strategy: any,
  brokerIndex: number
) {
  const bidCoverageData: any[] = [];

  // Process policy coverages
  for (const coverage of policyCoverages) {
    const isIncluded = Math.random() < strategy.includeRate;

    if (isIncluded) {
      // Enhanced coverage terms based on broker strategy
      const enhancedLimit = coverage.limit ? Math.round(Number(coverage.limit) * strategy.limitMultiplier * 100) / 100 : coverage.limit;
      const enhancedDeductible = coverage.deductible ? Math.round(Number(coverage.deductible) * strategy.deductibleMultiplier * 100) / 100 : coverage.deductible;

      // Add broker-specific enhancements based on coverage type
      let enhancedDescription = coverage.description || '';
      let customName = coverage.customName;

      switch (coverage.type) {
        case 'MANDATORY_LIABILITY':
          enhancedDescription = `${enhancedDescription} - ${strategy.description} con gestión 24/7`;
          break;
        case 'VEHICLE_DAMAGE':
          enhancedDescription = `${enhancedDescription} - ${strategy.description}, talleres concertados premium`;
          break;
        case 'THEFT':
          enhancedDescription = `${enhancedDescription} - ${strategy.description}, localización GPS incluida`;
          break;
        case 'FIRE':
          enhancedDescription = `${enhancedDescription} - ${strategy.description}, peritaje express`;
          break;
        case 'GLASS_BREAKAGE':
          enhancedDescription = `${enhancedDescription} - ${strategy.description}, reparación a domicilio`;
          break;
        case 'LEGAL_DEFENSE':
          enhancedDescription = `${enhancedDescription} - ${strategy.description}, asesoría jurídica especializada`;
          break;
        default:
          enhancedDescription = `${enhancedDescription} - ${strategy.description}`;
      }

      bidCoverageData.push({
        bidId: bidId,
        type: coverage.type,
        customName: customName,
        limit: coverage.type === 'MANDATORY_LIABILITY' ? null : enhancedLimit,
        limitIsUnlimited: coverage.type === 'LEGAL_DEFENSE',
        limitIsFullCost: ['VEHICLE_DAMAGE', 'THEFT', 'FIRE'].includes(coverage.type),
        limitPerDay: coverage.type === 'VEHICLE_REPLACEMENT' ? enhancedLimit : null,
        limitMaxDays: coverage.type === 'VEHICLE_REPLACEMENT' ? 30 : null,
        limitMaxMonths: null,
        liabilityBodilyCap: coverage.type === 'MANDATORY_LIABILITY' ? 70000000 : null,
        liabilityPropertyCap: coverage.type === 'MANDATORY_LIABILITY' ? 15000000 : null,
        deductible: enhancedDeductible,
        deductiblePercent: null,
        description: enhancedDescription
      });
    }
  }

  // Add premium extras for first broker
  if (brokerIndex === 0) {
    const premiumExtras = [
      {
        type: 'VEHICLE_REPLACEMENT',
        customName: 'Vehículo de Sustitución Premium',
        limit: null,
        limitPerDay: 50.00,
        limitMaxDays: 30,
        deductible: null,
        description: 'Vehículo de sustitución de gama similar durante reparaciones, hasta 30 días'
      },
      {
        type: 'TRAVEL_ASSISTANCE',
        customName: 'Asistencia en Viaje Europa+',
        limit: 2000.00,
        deductible: null,
        description: 'Asistencia en viaje extendida a toda Europa con repatriación incluida'
      },
      {
        type: 'PERSONAL_BELONGINGS',
        customName: 'Efectos Personales Plus',
        limit: 800.00,
        deductible: 50.00,
        description: 'Cobertura de efectos personales en el vehículo, incluyendo equipos electrónicos'
      }
    ];

    for (const extra of premiumExtras) {
      bidCoverageData.push({
        bidId: bidId,
        type: extra.type,
        customName: extra.customName,
        limit: extra.limit,
        limitIsUnlimited: false,
        limitIsFullCost: false,
        limitPerDay: extra.limitPerDay || null,
        limitMaxDays: extra.limitMaxDays || null,
        limitMaxMonths: null,
        liabilityBodilyCap: null,
        liabilityPropertyCap: null,
        deductible: extra.deductible,
        deductiblePercent: null,
        description: extra.description
      });
    }
  }

  // Bulk create all bid coverages
  if (bidCoverageData.length > 0) {
    await prisma.bidCoverage.createMany({
      data: bidCoverageData
    });
  }
}

async function main() {
  console.log('Starting database seeding...');

  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.ACCOUNT_HOLDER,
      phone: '+***********',
      profile: {
        firstName: 'María',
        lastName: 'García López',
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+***********',
      profile: {
        firstName: 'Pedro',
        lastName: 'Sánchez',
        registrationClass: 'Clase A',
        registrationKey: 'REG_001_2024',
        registrationDate: new Date('2024-01-15'),
        legalName: 'Seguros Martínez S.L.',
        identifier: 'B12345678',
        insurerCompany: InsurerCompany.GENERALI, // Changed to match extracted policy
        isAuthorizedByOther: false,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+***********',
      profile: {
        firstName: 'Ana',
        lastName: 'Rodríguez',
        registrationClass: 'Clase A',
        registrationKey: 'REG_002_2024',
        registrationDate: new Date('2024-02-20'),
        legalName: null,
        identifier: 'B87654321',
        insurerCompany: InsurerCompany.ALLIANZ,
        isAuthorizedByOther: false,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+34611223344',
      profile: {
        firstName: 'Laura',
        lastName: 'Gómez',
        registrationClass: 'Clase B',
        registrationKey: 'REG_003_2024',
        registrationDate: new Date('2024-03-10'),
        legalName: null,
        identifier: 'C98765432',
        insurerCompany: InsurerCompany.AXA,
        isAuthorizedByOther: true,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.ADMIN,
      phone: '+34911234567',
      profile: {
        firstName: 'Admin',
        lastName: 'User',
      },
    },
  ];

  // To ensure a clean slate, delete all existing data in the correct order (respecting foreign key constraints)
  console.log('🗑️  Deleting existing data in correct order...');

  // Delete dependent records first (in correct order based on foreign key constraints)
  await Promise.all([
    prisma.auctionCommission.deleteMany({}),
    prisma.auctionWinner.deleteMany({}),
    prisma.bidCoverage.deleteMany({}),
  ]);
  console.log('Deleted auction-related dependent records.');

  await prisma.bid.deleteMany({});
  console.log('Deleted all existing bids.');

  await prisma.auction.deleteMany({});
  console.log('Deleted all existing auctions.');

  await prisma.coverage.deleteMany({});
  console.log('Deleted all existing coverages.');

  await prisma.policyInsuredParty.deleteMany({});
  console.log('Deleted all existing policy insured parties.');

  await prisma.policy.deleteMany({});
  console.log('Deleted all existing policies.');

  await prisma.vehicle.deleteMany({});
  console.log('Deleted all existing vehicles.');

  await prisma.asset.deleteMany({});
  console.log('Deleted all existing assets.');

  await prisma.documentation.deleteMany({});
  console.log('Deleted all existing documentation.');

  await prisma.subscription.deleteMany({});
  console.log('Deleted all existing subscriptions.');

  await prisma.address.deleteMany({});
  console.log('Deleted all existing addresses.');

  await prisma.insuredParty.deleteMany({});
  console.log('Deleted all existing insured parties.');

  // Delete profile tables
  await Promise.all([
    prisma.accountHolderProfile.deleteMany({}),
    prisma.brokerProfile.deleteMany({}),
    prisma.adminProfile.deleteMany({}),
  ]);
  console.log('Deleted all existing profiles.');

  // Finally delete users
  await prisma.user.deleteMany({});
  console.log('Deleted all existing users from the public.User table.');
  
  // Bulk delete Supabase Auth users
  const { data: { users: allUsers }, error: listError } = await supabase.auth.admin.listUsers();

  if (listError) {
    console.error('Error fetching users to delete:', listError);
  } else if (allUsers.length > 0) {
    // Delete users in parallel batches
    const deletePromises = allUsers.map(user =>
      supabase.auth.admin.deleteUser(user.id).then(({ error }) => {
        if (error) {
          console.error(`Error deleting user ${user.email}:`, error);
        } else {
          console.log(`Deleted existing user ${user.email} from Supabase Auth.`);
        }
      })
    );
    await Promise.all(deletePromises);
  }

  // Create users in parallel with optimized approach
  console.log('👥 Creating users and profiles...');
  const userCreationPromises = testUsers.map(async (userData) => {
    try {
      // Create user in Supabase Auth
      const { data: newAuthUser, error: newAuthError } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true, // Auto-confirm email for testing
      // Note: phone is NOT set in Supabase Auth to match real user behavior
      // Phone will only exist in database and user_metadata (unverified)
      user_metadata: {
        display_name: `${userData.profile.firstName} ${userData.profile.lastName}`,
        role: userData.role,
        first_name: userData.profile.firstName,
        last_name: userData.profile.lastName,
        phone: userData.phone,
        phone_number_is_set: true, // Indicates phone exists in database
      },
    });

      if (newAuthError) {
        console.error(`Error creating user ${userData.email} in Supabase Auth:`, newAuthError);
        return null;
      }

      const userId = newAuthUser.user.id;
      console.log(`Created user ${userData.email} in Supabase Auth with ID: ${userId}`);

      // Create user and profile in database transaction
      await prisma.$transaction(async (tx) => {
        // Create user
        await tx.user.create({
          data: {
            id: userId,
            email: userData.email,
            phone: userData.phone,
            firstName: userData.profile.firstName,
            lastName: userData.profile.lastName,
            displayName: `${userData.profile.firstName} ${userData.profile.lastName}`,
            role: userData.role,
          },
        });

        // Create profile based on role
        switch (userData.role) {
          case Role.ACCOUNT_HOLDER:
            await tx.accountHolderProfile.create({
              data: { userId },
            });
            break;

          case Role.BROKER:
            const profileData = userData.profile as any;
            await tx.brokerProfile.create({
              data: {
                userId,
                registrationClass: profileData.registrationClass,
                registrationKey: profileData.registrationKey,
                registrationDate: profileData.registrationDate,
                legalName: profileData.legalName,
                identifier: profileData.identifier,
                insurerCompany: profileData.insurerCompany,
                isAuthorizedByOther: profileData.isAuthorizedByOther,
                isComplementary: profileData.isComplementary,
                isGroupAgent: profileData.isGroupAgent,
              },
            });
            break;

          case Role.ADMIN:
            await tx.adminProfile.create({
              data: { userId },
            });
            break;
        }
      });

      console.log(`Created user ${userData.email} and profile in database.`);
      return { userData, userId };
    } catch (error) {
      console.error(`Failed to create user ${userData.email}:`, error);
      return null;
    }
  });

  await Promise.all(userCreationPromises);

  // ============================================================================
  // CREATE COMPREHENSIVE TEST DATA
  // ============================================================================

  console.log('\n🏗️  Creating comprehensive test data...');

  // Use current date for "today" to ensure auctions are always in the future
  const today = new Date();
  today.setHours(10, 0, 0, 0); // Set to 10:00 AM for consistency
  console.log(`🗓️  Using current date for "today": ${today.toISOString()}`);

  // Get the created users for relationships
  const accountHolderUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { accountHolderProfile: true }
  });

  const broker1User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  const broker2User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  const broker3User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  if (!accountHolderUser?.accountHolderProfile || !broker1User?.brokerProfile || !broker2User?.brokerProfile || !broker3User?.brokerProfile) {
    throw new Error('Required user profiles not found');
  }

  // Create addresses for brokers using bulk operation
  console.log('📍 Creating broker addresses...');
  const brokerAddresses = [
    {
      brokerId: broker1User.brokerProfile.id,
      street: 'Calle Gran Vía, 45, 3º A',
      city: 'Madrid',
      province: 'MADRID' as const,
      region: 'MADRID' as const,
      country: 'SPAIN' as const,
      postalCode: '28013'
    },
    {
      brokerId: broker2User.brokerProfile.id,
      street: 'Avenida Diagonal, 123, 2º B',
      city: 'Barcelona',
      province: 'BARCELONA' as const,
      region: 'CATALONIA' as const,
      country: 'SPAIN' as const,
      postalCode: '08028'
    },
    {
      brokerId: broker3User.brokerProfile.id,
      street: 'Plaza del Ayuntamiento, 1',
      city: 'Valencia',
      province: 'VALENCIA' as const,
      region: 'VALENCIAN_COMMUNITY' as const,
      country: 'SPAIN' as const,
      postalCode: '46002'
    }
  ];

  await prisma.address.createMany({
    data: brokerAddresses
  });

  console.log(`✅ Created addresses for ${broker1User.firstName}, ${broker2User.firstName} and ${broker3User.firstName}`);

  // Create assets for the account holder
  console.log('🚗 Creating assets...');
  const carAsset = await prisma.asset.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      assetType: 'CAR',
      description: 'Seat León 1.5 TSI FR',
      value: 28500.00,
      vehicleDetails: {
        create: {
          brand: 'Seat',
          model: 'León',
          year: 2022,
          firstRegistrationDate: new Date('2022-03-15'),
          licensePlate: '1234ABC',
          version: '1.5 TSI FR',
          fuelType: 'GASOLINE',
          powerCv: 150,
          chassisNumber: 'VSSZZZKJZHXXXXXX',
          isLeased: false,
          seats: 5,
          garageType: 'PRIVATE',
          usageType: 'PRIVATE_REGULAR',
          kmPerYear: 'FROM_10000_TO_12000'
        }
      }
    }
  });

  const motorcycleAsset = await prisma.asset.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      assetType: 'MOTORCYCLE',
      description: 'Yamaha MT-07',
      value: 8500.00,
      vehicleDetails: {
        create: {
          brand: 'Yamaha',
          model: 'MT-07',
          year: 2023,
          firstRegistrationDate: new Date('2023-05-20'),
          licensePlate: '5678DEF',
          version: 'ABS',
          fuelType: 'GASOLINE',
          powerCv: 74.8,
          chassisNumber: 'JYARM321000XXXXXX',
          isLeased: false,
          seats: 2,
          garageType: 'SHARED_GUARDED',
          usageType: 'PRIVATE_OCCASIONAL',
          kmPerYear: 'FROM_4000_TO_6000'
        }
      }
    }
  });

  console.log(`✅ Created car asset: ${carAsset.description} and motorcycle: ${motorcycleAsset.description}`);

  // Create insured parties
  console.log('👥 Creating insured parties...');
  const mainInsuredParty = await prisma.insuredParty.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      firstName: 'María',
      lastName: 'García López',
      displayName: 'María García López',
      identification: '12345678A',
      roles: ['POLICYHOLDER','MAIN_DRIVER','OWNER'],
      gender: Gender.FEMALE,
      birthDate: new Date('1985-03-15'),
      driverLicenseNumber: '12345678A',
      driverLicenseIssuedAt: new Date('2003-04-01'),
      address: {
        create: {
          street: 'Calle Alcalá, 123, 4º C',
          city: 'Madrid',
          province: 'MADRID',
          region: 'MADRID',
          country: 'SPAIN',
          postalCode: '28009'
        }
      }
    }
  });

  const additionalDriver = await prisma.insuredParty.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      firstName: 'Juan',
      lastName: 'García Martín',
      displayName: 'Juan García Martín',
      identification: '87654321B',
      roles: ['ADDITIONAL_DRIVER'],
      gender: Gender.MALE,
      birthDate: new Date('1982-07-22'),
      driverLicenseNumber: '87654321B',
      driverLicenseIssuedAt: new Date('2000-10-15'),
      address: {
        create: {
          street: 'Calle Alcalá, 123, 4º C',
          city: 'Madrid',
          province: 'MADRID',
          region: 'MADRID',
          country: 'SPAIN',
          postalCode: '28009'
        }
      }
    }
  });

  console.log(`✅ Created insured parties: ${mainInsuredParty.firstName} ${mainInsuredParty.lastName} and ${additionalDriver.firstName} ${additionalDriver.lastName}`);

  // Create documentation records using bulk operations
  console.log('📄 Creating documentation...');

  // Create policy documents first (needed for policies)
  const policyDocuments = await prisma.$transaction(async (tx) => {
    const carDoc = await tx.documentation.create({
      data: {
        accountHolderId: accountHolderUser.accountHolderProfile!.id,
        type: 'POLICY_DOCUMENT',
        url: '/documents/policies/poliza_coche_2024.pdf',
        fileName: 'poliza_coche_2024.pdf',
        fileSize: 2048576, // 2MB
        mimeType: 'application/pdf'
      }
    });

    const motoDoc = await tx.documentation.create({
      data: {
        accountHolderId: accountHolderUser.accountHolderProfile!.id,
        type: 'POLICY_DOCUMENT',
        url: '/documents/policies/poliza_moto_2024.pdf',
        fileName: 'poliza_moto_2024.pdf',
        fileSize: 1536123, // 1.5MB
        mimeType: 'application/pdf'
      }
    });

    return { carDoc, motoDoc };
  });

  // Create broker quote documents in parallel
  const brokerQuotePromises = [
    prisma.documentation.create({
      data: {
        brokerId: broker1User.brokerProfile.id,
        type: 'QUOTE_DOCUMENT',
        url: '/documents/quotes/cotizacion_moto_broker1.pdf',
        fileName: 'cotizacion_moto_broker1.pdf',
        fileSize: 1024768, // 1MB
        mimeType: 'application/pdf'
      }
    }),
    prisma.documentation.create({
      data: {
        brokerId: broker2User.brokerProfile.id,
        type: 'QUOTE_DOCUMENT',
        url: '/documents/quotes/cotizacion_moto_broker2.pdf',
        fileName: 'cotizacion_moto_broker2.pdf',
        fileSize: 1124768, // 1.1MB
        mimeType: 'application/pdf'
      }
    }),
    prisma.documentation.create({
      data: {
        brokerId: broker3User.brokerProfile.id,
        type: 'QUOTE_DOCUMENT',
        url: '/documents/quotes/cotizacion_moto_broker3.pdf',
        fileName: 'cotizacion_moto_broker3.pdf',
        fileSize: 924768, // 0.9MB
        mimeType: 'application/pdf'
      }
    })
  ];

  await Promise.all(brokerQuotePromises);

  const carPolicyDocument = policyDocuments.carDoc;
  const motorcyclePolicyDocument = policyDocuments.motoDoc;

  console.log('✅ Created documentation records');

  // --- Policy & Auction Creation ---
  console.log('🔄 Creating policies and auctions based on scenarios...');

  const policyScenarios = [
    // Scenario 1: OPEN auction with 8 bids - ACTIVE policy
    {
      policyStatus: PolicyStateEnum.ACTIVE,
      policyEndDateOffset: 30, // Expires in 30 days
      auctionStatus: AuctionStateEnum.OPEN,
      auctionDates: { startOffset: 0 }, // Starts today, ends based on working hours
      bidCount: 8,
      asset: carAsset,
      document: carPolicyDocument,
      insuredParties: [mainInsuredParty, additionalDriver],
      insurer: InsurerCompany.MAPFRE,
      policyNumber: 'POL-OPEN-2024-001',
      bidStrategy: 'varied' // Mix of better and worse conditions
    },
    // Scenario 2: CLOSED auction with 8 bids - RENEW_SOON policy
    {
      policyStatus: PolicyStateEnum.RENEW_SOON,
      policyEndDateOffset: 25, // Expires in 25 days
      auctionStatus: AuctionStateEnum.CLOSED,
      auctionDates: { startOffset: -5, endOffset: -1 }, // Recently closed
      bidCount: 8,
      asset: motorcycleAsset,
      document: motorcyclePolicyDocument,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.ALLIANZ,
      policyNumber: 'POL-CLOSED-2024-002',
      bidStrategy: 'competitive' // Mostly better conditions
    },
    // Scenario 3: SIGNED_POLICY auction with 8 bids - ACTIVE policy
    {
      policyStatus: PolicyStateEnum.ACTIVE,
      policyEndDateOffset: 50, // Expires in 50 days
      auctionStatus: AuctionStateEnum.SIGNED_POLICY,
      auctionDates: { startOffset: -20, endOffset: -13 }, // In the past
      bidCount: 8,
      asset: null, // Create new asset
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.AXA,
      policyNumber: 'POL-SIGNED-2024-003',
      bidStrategy: 'premium', // Better conditions with premium extras
      // Special flags for workflow demonstration
      demonstrateWorkflow: true,
      selectedBidIndex: 0, // Select the best (lowest) bid
      hasNewPolicyDocument: true,
      isFinalized: true,
    },
    // Scenario 4: CANCELED auction with 0 bids - DRAFT policy
    {
      policyStatus: PolicyStateEnum.DRAFT,
      policyEndDateOffset: 40, // Expires in 40 days
      auctionStatus: AuctionStateEnum.CANCELED,
      auctionDates: { startOffset: -15, endOffset: -8 }, // In the past
      bidCount: 0,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.GENERALI,
      policyNumber: 'POL-CANCELED-2024-004',
      bidStrategy: 'none'
    },
    // Scenario 5: EXPIRED auction with 0 bids - EXPIRED policy
    {
      policyStatus: PolicyStateEnum.EXPIRED,
      policyEndDateOffset: -5, // Expired 5 days ago
      auctionStatus: AuctionStateEnum.EXPIRED,
      auctionDates: { startOffset: -40, endOffset: -33 }, // In the past
      bidCount: 0,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.ZURICH,
      policyNumber: 'POL-EXPIRED-2024-005',
      bidStrategy: 'none'
    },
  ];

  let policyCounter = 1;

  for (const scenario of policyScenarios) {
    console.log(`  - Creating policy #${policyCounter} with status: ${scenario.policyStatus}`);

    let asset = scenario.asset;
    if (!asset) {
      const assetType = policyCounter % 2 === 0 ? 'MOTORCYCLE' : 'CAR';
      const brand = assetType === 'CAR' ? 'Audi' : 'Ducati';
      const model = assetType === 'CAR' ? 'A3' : 'Panigale';
      asset = await prisma.asset.create({
        data: {
          accountHolderId: accountHolderUser.accountHolderProfile.id,
          assetType,
          description: `${brand} ${model} (Policy #${policyCounter})`,
          value: 35000.00,
          vehicleDetails: {
            create: {
              brand,
              model,
              year: 2023,
              firstRegistrationDate: new Date('2023-01-01'),
              licensePlate: `AU${policyCounter.toString().padStart(2, '0')}${scenario.policyStatus.substring(0, 2)}`,
              version: 'Sportback',
              fuelType: 'GASOLINE',
              powerCv: 150,
              chassisNumber: `VSSZZZAUZHXXXX${policyCounter.toString().padStart(2, '0')}`,
              isLeased: false,
              seats: 5,
              garageType: 'PRIVATE',
              usageType: 'PRIVATE_REGULAR',
              kmPerYear: 'FROM_10000_TO_12000'
            }
          }
        }
      });
    }

    let document = scenario.document;
    if (!document) {
        document = await prisma.documentation.create({
            data: {
                accountHolderId: accountHolderUser.accountHolderProfile.id,
                type: 'POLICY_DOCUMENT',
                url: `/documents/policies/poliza_scenario_${policyCounter}.pdf`,
                fileName: `poliza_scenario_${policyCounter}.pdf`,
                fileSize: 1000000,
                mimeType: 'application/pdf',
            },
        });
    }

    const policyData: any = {
        document: { connect: { id: document.id } },
        accountHolder: { connect: { id: accountHolderUser.accountHolderProfile.id } },
        asset: { connect: { id: asset.id } },
        policyNumber: scenario.policyNumber,
        insurerCompany: scenario.insurer,
        isAssetsTypeConfirmed: true,
        paymentPeriod: 'ANNUAL',
        premium: 600.00 + policyCounter * 10,
        productName: `Seguro ${asset.assetType === 'CAR' ? 'Coche' : 'Moto'} #${policyCounter}`,
        termsAccepted: true,
        termsAcceptedAt: new Date('2024-01-01T10:00:00Z'),
        insuredParties: {
            create: scenario.insuredParties.map(p => ({ insuredPartyId: p.id })),
        },
    };

    if (scenario.policyEndDateOffset !== null) {
        const policyEndDate = new Date(today);
        policyEndDate.setDate(today.getDate() + scenario.policyEndDateOffset);
        const policyStartDate = new Date(policyEndDate);
        policyStartDate.setFullYear(policyEndDate.getFullYear() - 1);
        policyData.startDate = policyStartDate;
        policyData.endDate = policyEndDate;
    }

    const newPolicy = await prisma.policy.create({ data: policyData });
    console.log(`    ✅ Created policy ${newPolicy.policyNumber}`);
    
    // Create proper state history for policy following state machine rules
    if (scenario.policyStatus === PolicyStateEnum.DRAFT) {
      // For DRAFT policies, just create the initial DRAFT state
      await PolicyStateService.createState(newPolicy.id, PolicyStateEnum.DRAFT);
    } else {
      // For all other states, start with DRAFT and then transition
      await PolicyStateService.createState(newPolicy.id, PolicyStateEnum.DRAFT);
      
      // Add a small delay to ensure proper chronological ordering
      await new Promise(resolve => setTimeout(resolve, 10));
      
      // Now transition to the target state
      if (scenario.policyStatus === PolicyStateEnum.ACTIVE) {
        await PolicyStateService.transitionToState(newPolicy.id, PolicyStateEnum.ACTIVE);
      } else if (scenario.policyStatus === PolicyStateEnum.RENEW_SOON) {
        await PolicyStateService.transitionToState(newPolicy.id, PolicyStateEnum.ACTIVE);
        await new Promise(resolve => setTimeout(resolve, 10));
        await PolicyStateService.transitionToState(newPolicy.id, PolicyStateEnum.RENEW_SOON);
      } else if (scenario.policyStatus === PolicyStateEnum.EXPIRED) {
        // Could come from either ACTIVE or RENEW_SOON, let's use ACTIVE path
        await PolicyStateService.transitionToState(newPolicy.id, PolicyStateEnum.ACTIVE);
        await new Promise(resolve => setTimeout(resolve, 10));
        await PolicyStateService.transitionToState(newPolicy.id, PolicyStateEnum.EXPIRED);
      }
    }

    // Create sample coverages for the policy using bulk operation
    console.log(`    📋 Adding coverages to policy ${newPolicy.policyNumber}...`);

    // Use simplified coverage data with dynamic asset value for specific coverage types
    const processedSampleCoverages = getSimplifiedCoverages().map((coverage: any) => {
      // Normalize mandatory liability coverage
      const normalizedCoverage = normalizeMandatoryLiability(coverage);

      // Set asset value for vehicle-related coverages
      if (normalizedCoverage.type === 'VEHICLE_DAMAGE' || normalizedCoverage.type === 'FIRE' || normalizedCoverage.type === 'THEFT') {
        return {
          ...normalizedCoverage,
          limit: asset.value
        };
      }
      return normalizedCoverage;
    });

    // Create coverages for the policy using bulk operation
    const coverageData = processedSampleCoverages.map((coverage: any) => ({
      policyId: newPolicy.id,
      type: coverage.type as any,
      customName: coverage.customName || null,
      description: coverage.description || null,
      limit: coverage.limit || null,
      limitIsUnlimited: coverage.limitIsUnlimited || false,
      limitIsFullCost: coverage.limitIsFullCost || false,
      limitPerDay: coverage.limitPerDay || null,
      limitMaxDays: coverage.limitMaxDays || null,
      limitMaxMonths: coverage.limitMaxMonths || null,
      liabilityBodilyCap: coverage.liabilityBodilyCap || null,
      liabilityPropertyCap: coverage.liabilityPropertyCap || null,
      deductible: coverage.deductible || null,
      deductiblePercent: coverage.deductiblePercent || null,
    }));

    await prisma.coverage.createMany({
      data: coverageData
    });

    console.log(`    ✅ Added ${processedSampleCoverages.length} coverages to policy ${newPolicy.policyNumber}`);

    if (scenario.auctionStatus) {
        console.log(`    - Creating auction with status: ${scenario.auctionStatus}`);
        let auctionStartDate = new Date(today);

        if (scenario.auctionDates.startOffset !== undefined) {
          auctionStartDate.setDate(today.getDate() + scenario.auctionDates.startOffset);
        }

        // Calculate auction end date using working hours business logic (48 working hours, Mon-Fri 06:00-23:59 Madrid time)
        const auctionEndDate = calculateWorkingHoursClosedAt(auctionStartDate, DEFAULT_AUCTION_DURATION_HOURS);

        console.log(`    - Auction start: ${auctionStartDate.toISOString()} (Madrid: ${auctionStartDate.toLocaleString('sv-SE', { timeZone: 'Europe/Madrid' })})`);
        console.log(`    - Auction end date: ${auctionEndDate.toISOString()} (Madrid: ${auctionEndDate.toLocaleString('sv-SE', { timeZone: 'Europe/Madrid' })})`);

        // For SIGNED_POLICY auctions, we need to prepare additional data first
        let auctionCreateData: any = {
            accountHolderId: accountHolderUser.accountHolderProfile.id,
            policyId: newPolicy.id,
            startDate: auctionStartDate,
            endDate: auctionEndDate,
            maxWinners: 3,
            minWinners: 1,
        };

        // For SIGNED_POLICY auctions, create with complete data from the start
        let newAuction;
        if (scenario.isFinalized && scenario.auctionStatus === AuctionStateEnum.SIGNED_POLICY) {
            console.log(`    🔄 Pre-creating SIGNED_POLICY workflow data...`);

            // Find the selected broker
            const selectedBroker = await prisma.brokerProfile.findFirst({
                where: { user: { email: '<EMAIL>' } },
                include: { user: true }
            });

            if (selectedBroker) {
                // Create the new policy document and extracted policy first
                const newPolicyDoc = await prisma.documentation.create({
                    data: {
                        type: DocumentType.NEW_POLICY_DOCUMENT,
                        fileName: 'nueva-poliza-generali.pdf',
                        mimeType: 'application/pdf',
                        fileSize: 256000,
                        url: '/documents/nueva-poliza-generali.pdf',
                        accountHolderId: accountHolderUser.accountHolderProfile.id,
                    },
                });

                // Create extracted policy with consistent data
                const extractedPolicy = await prisma.policy.create({
                    data: {
                        policyNumber: 'NEW-GEN-2024-001',
                        accountHolderId: accountHolderUser.accountHolderProfile.id,
                        documentId: newPolicyDoc.id,
                        premium: 950.00,
                        insurerCompany: InsurerCompany.GENERALI, // Consistent with broker
                        startDate: new Date(),
                        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
                        productName: 'Seguro de Automóvil Generali Integral',
                        assetId: newPolicy.assetId, // Link to the same asset
                    },
                });

                // Add simplified coverages to extracted policy
                const simplifiedCoverages = [
                    {
                        type: GuaranteeType.MANDATORY_LIABILITY,
                        customName: 'Responsabilidad Civil Obligatoria',
                        limit: 1200000.0,
                        description: 'Cobertura obligatoria de responsabilidad civil',
                    },
                    {
                        type: GuaranteeType.GLASS_BREAKAGE,
                        customName: 'Rotura de Lunas',
                        limit: 600.0,
                        description: 'Cobertura para rotura de lunas del vehículo',
                    },
                    {
                        type: GuaranteeType.THEFT,
                        customName: 'Robo del Vehículo',
                        limit: 15000.0,
                        description: 'Cobertura contra robo del vehículo',
                    }
                ];

                const extractedCoverageData = simplifiedCoverages.map(coverage => ({
                    policyId: extractedPolicy.id,
                    type: coverage.type,
                    customName: coverage.customName,
                    limit: coverage.limit,
                    description: coverage.description,
                    limitIsFullCost: false,
                    limitPerDay: null,
                    limitMaxDays: null,
                    limitMaxMonths: null,
                    liabilityBodilyCap: null,
                    liabilityPropertyCap: null,
                    deductible: null,
                    deductiblePercent: null,
                }));

                await prisma.coverage.createMany({
                    data: extractedCoverageData
                });

                // Create the auction first without selected bid
                newAuction = await prisma.auction.create({
                    data: {
                        ...auctionCreateData,
                        newPolicyDocumentId: newPolicyDoc.id,
                    },
                });

                // Create the selected bid with the real auction ID
                const bidCreatedAt = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000); // 2 days ago
                const selectedBid = await prisma.bid.create({
                    data: {
                        auctionId: newAuction.id,
                        brokerId: selectedBroker.id,
                        amount: 950.00,
                        createdAt: bidCreatedAt,
                    },
                });

                // Update the auction to reference the selected bid using raw SQL to bypass Prisma issue
                try {
                    await prisma.$executeRaw`
                        UPDATE auction
                        SET selected_bid_id = ${selectedBid.id}::uuid
                        WHERE id = ${newAuction.id}::uuid
                    `;
                    console.log(`      ✅ Successfully updated auction with selectedBidId: ${selectedBid.id}`);
                } catch (error) {
                    console.error(`      ❌ Failed to update auction with selectedBidId:`, error);
                    // Continue without failing the entire seed process
                }

                console.log(`      ✅ Created complete SIGNED_POLICY auction with all data`);
                console.log(`      📄 Selected bid: ${selectedBid.id} (${selectedBroker.user.displayName})`);
                console.log(`      📄 New policy document: ${newPolicyDoc.id}`);
            } else {
                // Fallback if broker not found
                newAuction = await prisma.auction.create({
                    data: auctionCreateData,
                });
            }
        } else {
            // For non-SIGNED_POLICY auctions, create normally
            newAuction = await prisma.auction.create({
                data: auctionCreateData,
            });
        }

        // Create proper state history for auction following state machine rules
        if (scenario.auctionStatus === AuctionStateEnum.OPEN) {
          // For OPEN auctions, just create the initial OPEN state
          await AuctionStateService.createState(newAuction.id, AuctionStateEnum.OPEN);
        } else {
          // For all other states, start with OPEN and then transition
          await AuctionStateService.createState(newAuction.id, AuctionStateEnum.OPEN);
          
          // Add a small delay to ensure proper chronological ordering
          await new Promise(resolve => setTimeout(resolve, 10));
          
          // Now transition to the target state
          if (scenario.auctionStatus === AuctionStateEnum.CLOSED) {
            await AuctionStateService.transitionToState(newAuction.id, AuctionStateEnum.CLOSED);
          } else if (scenario.auctionStatus === AuctionStateEnum.SIGNED_POLICY) {
            await AuctionStateService.transitionToState(newAuction.id, AuctionStateEnum.CLOSED);
            await new Promise(resolve => setTimeout(resolve, 10));
            await AuctionStateService.transitionToState(newAuction.id, AuctionStateEnum.SIGNED_POLICY);
          } else if (scenario.auctionStatus === AuctionStateEnum.EXPIRED) {
            await AuctionStateService.transitionToState(newAuction.id, AuctionStateEnum.CLOSED);
            await new Promise(resolve => setTimeout(resolve, 10));
            await AuctionStateService.transitionToState(newAuction.id, AuctionStateEnum.EXPIRED);
          } else if (scenario.auctionStatus === AuctionStateEnum.CANCELED) {
            await AuctionStateService.transitionToState(newAuction.id, AuctionStateEnum.CANCELED);
          }
        }
        console.log(`    ✅ Created auction ${newAuction.id} for policy ${newPolicy.policyNumber}`);

        await prisma.documentation.update({
            where: { id: document.id },
            data: { relatedAuctionId: newAuction.id },
        });

        if (scenario.bidCount > 0) {
            console.log(`    💰 Adding ${scenario.bidCount} bids to auction ${newAuction.id}...`);

            // Get policy coverages once for all bids
            const policyCoverages = await prisma.coverage.findMany({
              where: { policyId: newPolicy.id }
            });

            // Define varied broker strategies based on scenario
            const getStrategiesForScenario = (strategyType: string) => {
              switch (strategyType) {
                case 'varied':
                  return [
                    { name: 'Premium Plus', description: 'Cobertura premium con beneficios adicionales', limitMultiplier: 1.15, deductibleMultiplier: 0.8, includeRate: 0.95 },
                    { name: 'Basic Coverage', description: 'Cobertura básica económica', limitMultiplier: 0.85, deductibleMultiplier: 1.2, includeRate: 0.75 },
                    { name: 'Standard Plus', description: 'Cobertura estándar mejorada', limitMultiplier: 1.05, deductibleMultiplier: 0.9, includeRate: 0.9 },
                    { name: 'Economy', description: 'Opción económica', limitMultiplier: 0.8, deductibleMultiplier: 1.3, includeRate: 0.7 },
                    { name: 'Comprehensive', description: 'Cobertura integral', limitMultiplier: 1.2, deductibleMultiplier: 0.75, includeRate: 0.98 },
                    { name: 'Value', description: 'Mejor relación calidad-precio', limitMultiplier: 0.95, deductibleMultiplier: 1.0, includeRate: 0.85 },
                    { name: 'Enhanced', description: 'Cobertura mejorada', limitMultiplier: 1.1, deductibleMultiplier: 0.85, includeRate: 0.92 },
                    { name: 'Essential', description: 'Lo esencial cubierto', limitMultiplier: 0.9, deductibleMultiplier: 1.1, includeRate: 0.8 }
                  ];
                case 'competitive':
                  return [
                    { name: 'Premium Elite', description: 'Cobertura elite con servicios premium', limitMultiplier: 1.25, deductibleMultiplier: 0.7, includeRate: 0.98 },
                    { name: 'Superior Plus', description: 'Cobertura superior mejorada', limitMultiplier: 1.18, deductibleMultiplier: 0.75, includeRate: 0.95 },
                    { name: 'Advanced Pro', description: 'Cobertura avanzada profesional', limitMultiplier: 1.12, deductibleMultiplier: 0.8, includeRate: 0.92 },
                    { name: 'Premium Standard', description: 'Estándar premium', limitMultiplier: 1.08, deductibleMultiplier: 0.85, includeRate: 0.9 },
                    { name: 'Enhanced Plus', description: 'Cobertura mejorada plus', limitMultiplier: 1.15, deductibleMultiplier: 0.78, includeRate: 0.94 },
                    { name: 'Quality Pro', description: 'Calidad profesional', limitMultiplier: 1.1, deductibleMultiplier: 0.82, includeRate: 0.91 },
                    { name: 'Complete Care', description: 'Cuidado completo', limitMultiplier: 1.2, deductibleMultiplier: 0.76, includeRate: 0.96 },
                    { name: 'Premium Care', description: 'Cuidado premium', limitMultiplier: 1.13, deductibleMultiplier: 0.79, includeRate: 0.93 }
                  ];
                case 'premium':
                  return [
                    { name: 'Platinum Elite', description: 'Cobertura platino elite', limitMultiplier: 1.3, deductibleMultiplier: 0.65, includeRate: 0.99 },
                    { name: 'Gold Premium', description: 'Premium oro', limitMultiplier: 1.22, deductibleMultiplier: 0.72, includeRate: 0.97 },
                    { name: 'Diamond Care', description: 'Cuidado diamante', limitMultiplier: 1.28, deductibleMultiplier: 0.68, includeRate: 0.98 },
                    { name: 'Executive Plus', description: 'Ejecutivo plus', limitMultiplier: 1.25, deductibleMultiplier: 0.7, includeRate: 0.96 },
                    { name: 'VIP Protection', description: 'Protección VIP', limitMultiplier: 1.35, deductibleMultiplier: 0.6, includeRate: 1.0 },
                    { name: 'Luxury Coverage', description: 'Cobertura de lujo', limitMultiplier: 1.27, deductibleMultiplier: 0.69, includeRate: 0.97 },
                    { name: 'Supreme Care', description: 'Cuidado supremo', limitMultiplier: 1.32, deductibleMultiplier: 0.63, includeRate: 0.99 },
                    { name: 'Elite Protection', description: 'Protección elite', limitMultiplier: 1.24, deductibleMultiplier: 0.71, includeRate: 0.95 }
                  ];
                default:
                  return [];
              }
            };

            const strategies = getStrategiesForScenario(scenario.bidStrategy);
            const brokers = [broker1User, broker2User, broker3User];

            // Create the specified number of bids
            const bidPromises = Array.from({ length: scenario.bidCount }, async (_, i) => {
              const broker = brokers[i % brokers.length];
              if (!broker?.brokerProfile) return;

              const bidDate = new Date(newAuction.startDate);
              bidDate.setHours(bidDate.getHours() + (i + 1) * 2);

              // Create varied pricing based on strategy
              let discountFactor = 0.9 - (i * 0.02); // Start at 10% discount, increase by 2% each bid
              if (scenario.bidStrategy === 'premium') {
                // Special case: For SIGNED_POLICY scenario, ensure Pedro's bid (i=0) matches extracted policy premium
                if (scenario.auctionStatus === AuctionStateEnum.SIGNED_POLICY && i === 0) {
                  // Pedro's bid should be €450, current policy is €630, so factor = 450/630 = 0.714285...
                  discountFactor = 0.714285; // This will give us exactly €450 for Pedro's bid
                } else {
                  discountFactor = 0.85 - (i * 0.015); // Better discounts for premium
                }
              } else if (scenario.bidStrategy === 'competitive') {
                discountFactor = 0.88 - (i * 0.018); // Competitive pricing
              }

              const initialBid = await prisma.bid.create({
                data: {
                  auctionId: newAuction.id,
                  brokerId: broker.brokerProfile.id,
                  amount: (newPolicy.premium?.toNumber() ?? 600) * discountFactor,
                  createdAt: bidDate,
                },
              });

              const strategy = strategies[i % strategies.length] || strategies[0];

              // Create bid coverages using optimized helper function
              await createBidCoveragesForBid(
                prisma,
                initialBid.id,
                policyCoverages,
                strategy,
                i
              );

              return initialBid;
            });

            await Promise.all(bidPromises);
            console.log(`    ✅ Added ${scenario.bidCount} bids to auction ${newAuction.id}`);
        }

        // Handle workflow demonstration scenario
        if (scenario.demonstrateWorkflow && scenario.bidCount > 0) {
            console.log(`    🎯 Setting up workflow demonstration for auction ${newAuction.id}...`);

            // Get the bids for this auction to select the best one
            const auctionBids = await prisma.bid.findMany({
                where: { auctionId: newAuction.id },
                orderBy: { amount: 'asc' }, // Get lowest price first
                include: {
                    broker: {
                        include: { user: true }
                    }
                }
            });

            if (auctionBids.length > 0) {
                const selectedBidIndex = scenario.selectedBidIndex || 0;
                const selectedBid = auctionBids[selectedBidIndex];

                if (selectedBid) {
                    // Create a new policy document for the workflow
                    let newPolicyDocumentId = null;
                    if (scenario.hasNewPolicyDocument) {
                        // First create the new policy record with extracted data from AI processing
                        const extractedPolicyData = {
                            accountHolderId: accountHolderUser.accountHolderProfile.id,
                            assetId: asset.id,
                            policyNumber: `EXTRACTED-${newPolicy.policyNumber}`,
                            insurerCompany: InsurerCompany.GENERALI, // Extracted: Changed from AXA to GENERALI
                            premium: 450.00, // Extracted: €450 vs original €630 = €180 savings
                            productName: `Nueva Póliza Generali - ${asset.assetType === 'CAR' ? 'Coche' : 'Moto'}`,
                            paymentPeriod: PaymentPeriod.ANNUAL,
                            startDate: new Date('2024-10-14'), // Start immediately
                            endDate: new Date('2025-10-14'), // Extracted: End date 14/10/2025
                            isAssetsTypeConfirmed: true,
                            termsAccepted: true,
                            termsAcceptedAt: new Date(),
                        };

                        const extractedPolicy = await prisma.policy.create({
                            data: extractedPolicyData,
                        });

                        console.log(`      📋 Created extracted policy ${extractedPolicy.id} with premium €${extractedPolicy.premium} (Generali)`);
                        
                        // Create state history for the extracted policy
                        await PolicyStateService.createState(extractedPolicy.id, PolicyStateEnum.ACTIVE);
                        
                        // Now create the policy document and link it to the extracted policy
                        const newPolicyDoc = await prisma.documentation.create({
                            data: {
                                fileName: `nueva-poliza-${newPolicy.policyNumber}.pdf`,
                                url: `https://example.com/documents/nueva-poliza-${newPolicy.policyNumber}.pdf`,
                                fileSize: 2048576, // 2MB
                                mimeType: 'application/pdf',
                                type: DocumentType.NEW_POLICY_DOCUMENT,
                                accountHolderId: accountHolderUser.accountHolderProfile.id,
                                relatedAuctionId: newAuction.id,
                                isPolicyAttested: true,
                                policyAttestedAt: new Date(),
                            },
                        });
                        
                        // Link the extracted policy to the document
                        await prisma.policy.update({
                            where: { id: extractedPolicy.id },
                            data: { documentId: newPolicyDoc.id }
                        });
                        
                        // Add basic coverages to the extracted policy (reuse existing coverage logic)
                        const simplifiedCoverages = getSimplifiedCoverages().slice(0, 10); // Subset for new policy
                        const extractedCoverageData = simplifiedCoverages.map((coverage: any) => ({
                            policyId: extractedPolicy.id,
                            type: coverage.type as any,
                            customName: coverage.customName || null,
                            description: coverage.description || null,
                            limit: coverage.limit || null,
                            limitIsUnlimited: coverage.limitIsUnlimited || false,
                            limitIsFullCost: coverage.limitIsFullCost || false,
                            limitPerDay: coverage.limitPerDay || null,
                            limitMaxDays: coverage.limitMaxDays || null,
                            limitMaxMonths: coverage.limitMaxMonths || null,
                            liabilityBodilyCap: coverage.liabilityBodilyCap || null,
                            liabilityPropertyCap: coverage.liabilityPropertyCap || null,
                            deductible: coverage.deductible || null,
                            deductiblePercent: coverage.deductiblePercent || null,
                        }));

                        await prisma.coverage.createMany({
                            data: extractedCoverageData
                        });
                        
                        console.log(`      📋 Added ${simplifiedCoverages.length} coverages to extracted policy`);
                        
                        newPolicyDocumentId = newPolicyDoc.id;
                        console.log(`      📄 Created new policy document ${newPolicyDoc.id} linked to extracted policy`);
                    }
                    
                    // Store the data for later use - we'll recreate the auction with the correct data
                    console.log(`      📝 SIGNED_POLICY auction workflow completed`);
                    console.log(`      💾 Selected bid: ${selectedBid.id} (${selectedBid.broker.user.displayName})`);
                    console.log(`      📄 New policy document: ${newPolicyDocumentId}`);

                    // Update the auction with the new policy document and selected bid using raw SQL
                    if (newPolicyDocumentId) {
                        try {
                            await prisma.$executeRaw`
                                UPDATE auction
                                SET selected_bid_id = ${selectedBid.id}::uuid,
                                    new_policy_document_id = ${newPolicyDocumentId}::uuid
                                WHERE id = ${newAuction.id}::uuid
                            `;
                            console.log(`      ✅ Successfully updated auction with selectedBidId and newPolicyDocumentId`);
                        } catch (error) {
                            console.error(`      ❌ Failed to update auction:`, error);
                            // Continue without failing the entire seed process
                        }

                        // Recreate the auction states for proper state history
                        try {
                            await AuctionStateService.createState(newAuction.id, AuctionStateEnum.OPEN);
                            await new Promise(resolve => setTimeout(resolve, 10));
                            await AuctionStateService.transitionToState(newAuction.id, AuctionStateEnum.CLOSED);
                            await new Promise(resolve => setTimeout(resolve, 10));
                            await AuctionStateService.transitionToState(newAuction.id, AuctionStateEnum.SIGNED_POLICY);
                            console.log(`      ✅ Successfully created auction state transitions`);
                        } catch (error) {
                            console.error(`      ❌ Failed to create auction state transitions:`, error);
                            // Continue without failing the entire seed process
                        }
                    }
                    
                    console.log(`      ✅ Completed workflow update (selectedBid: ${selectedBid.id})`);

                    // Create state transition for SIGNED_POLICY auctions
                    // Note: Auction is already created with SIGNED_POLICY state, so no transition needed
                    if (scenario.isFinalized) {
                        console.log(`      ✅ Auction already in SIGNED_POLICY state - no transition needed`);
                    }

                    console.log(`      ✅ Updated auction with selected bid ${selectedBid.id} from ${selectedBid.broker.user.displayName}`);
                    console.log(`      💰 Selected bid amount: €${selectedBid.amount}`);
                    console.log(`      📋 Workflow status: ${scenario.isFinalized ? 'Finalized' : 'In Progress'}`);
                }
            }
        }
    }
    policyCounter++;
  }

  // Create auction winners for the CLOSED auction
  console.log('🏆 Creating auction winners...');
  const closedAuction = await prisma.auction.findFirst({
    where: {
      policy: {
        policyNumber: 'POL-CLOSED-2024-002'
      }
    },
    include: {
      bids: {
        orderBy: {
          amount: 'asc'
        }
      },
      policy: true
    }
  });

  if (closedAuction && closedAuction.bids.length >= 3) {
    const bestBids = closedAuction.bids;
    const winnerBroker1 = bestBids[0];
    const winnerBroker2 = bestBids[1];
    const winnerBroker3 = bestBids[2];

    if (winnerBroker1 && winnerBroker2 && winnerBroker3) {
        const winner1 = await prisma.auctionWinner.create({
          data: {
            auctionId: closedAuction.id,
            brokerId: winnerBroker1.brokerId,
            bidId: winnerBroker1.id,
            position: 1
          }
        });
        await prisma.auctionWinner.create({
          data: {
            auctionId: closedAuction.id,
            brokerId: winnerBroker2.brokerId,
            bidId: winnerBroker2.id,
            position: 2
          }
        });
        await prisma.auctionWinner.create({
          data: {
            auctionId: closedAuction.id,
            brokerId: winnerBroker3.brokerId,
            bidId: winnerBroker3.id,
            position: 3
          }
        });
        console.log(`✅ Created auction winners for auction ${closedAuction.id}`);

        // Simulate one winner paying the commission
        console.log('💸 Simulating commission payment...');
        await prisma.auctionCommission.create({
          data: {
            auctionId: closedAuction.id,
            winnerId: winner1.id,
            brokerId: winner1.brokerId,
            amount: (closedAuction.policy.premium?.toNumber() ?? 0) * 0.1, // 10% commission
            status: 'PAID',
            paidAt: new Date(),
            stripePaymentIntentId: 'pi_3P...' // Fake payment intent
          }
        });

        // Update winner to reflect data reveal
        await prisma.auctionWinner.update({
          where: { id: winner1.id },
          data: { contactDataRevealedAt: new Date() }
        });
        console.log(`✅ Commission paid by winner and contact data revealed.`);
    }
  }




  // Create a subscription for a broker
  console.log('💳 Creating broker subscription...');
  await prisma.subscription.create({
    data: {
      brokerId: broker1User.brokerProfile.id,
      stripeSubscriptionId: 'sub_1P...',
      status: 'active',
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    }
  });

  console.log(`✅ Created subscription for ${broker1User.firstName}`);

  // Calculate dynamic counts for accurate summary
  const totalUsers = testUsers.length;
  const totalBrokers = testUsers.filter(user => user.role === Role.BROKER).length;
  const accountHolders = testUsers.filter(user => user.role === Role.ACCOUNT_HOLDER).length;
  const admins = testUsers.filter(user => user.role === Role.ADMIN).length;

  console.log('\n🎉 Simplified test data creation completed!');
  console.log('📊 Summary of created data:');
  console.log(`   - ${totalUsers} Users (${accountHolders} account holder, ${totalBrokers} brokers, ${admins} admin)`);
  console.log(`   - ${policyScenarios.length + 2} Assets (cars, motorcycles) with vehicle details`);
  console.log('   - 2 Insured parties with addresses');
  console.log(`   - ${policyScenarios.length} Policies with 20 coverages each`);
  console.log(`   - ${policyScenarios.filter(p => p.auctionStatus).length} Auctions (1 OPEN, 1 CLOSED, 1 SIGNED_POLICY, 1 CANCELED, 1 EXPIRED)`);
  console.log('   - 32 total bids across all auctions (8+8+8+0+0)');
  console.log('   - 3 Broker addresses');
  console.log('   - Multiple documentation records (policies and quotes)');
  console.log('   - 1 Broker subscription');
  console.log('   - Auction winners and commission payments for CLOSED auction');

  console.log('\n🔧 Infrastructure components deployed:');
  console.log('   - Auction expiration cron job (every 5 minutes)');
  console.log('   - Comprehensive notification system');
  console.log('   - Auction winner failsafe trigger (migration 006)');
  console.log('   - sendAuctionNotification Edge Function');
  console.log('   - Environment variable security configuration');

  console.log('\n✅ Simplified database seeding completed successfully!');
  console.log('🚀 Minimal test dataset ready for development and testing!');
}

main()
  .catch((e) => {
    console.error('Error during database seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
