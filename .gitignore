# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage
logs.txt

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
.env.development
.env.test
.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Prisma
/prisma/migrations/
/prisma/dev.db
/prisma/dev.db-journal

# Supabase
.supabase/
.branches
.temp

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Temporary files
*.tmp
*.temp
.cache/

# Generated files
/generated/

# Zod Schemes
src/lib/zod

# Claude - Track commands and CLAUDE.md but ignore MCP files and local settings
.claude/mcp*
.claude/settings.json
.claude/local-settings.json