# Account Management & Authentication Module Refactor Summary

This document provides a detailed summary of the changes made to the authentication and account management module in the `zee-next-app` project.

## 1. Project Goals

The primary goal of this task was to refactor and enhance the authentication module to create a robust, user-friendly, and secure system using Supabase Auth. The key objectives included:

-   Implementing client-side validation with Zod.
-   Integrating multiple Supabase authentication methods (email/password, magic link, Google OAuth).
-   Automating user profile synchronization between `auth.users` and the public `user` table.
-   Providing clear user feedback with `sonner` toast notifications.
-   Aligning the UI with the design standards of the `loterix-suite` reference project.

## 2. Implemented Features & Changes

### 2.1. Client-Side Validation

-   **Zod Schema:** A Zod schema was created at [`src/features/auth/schemas/index.ts`](src/features/auth/schemas/index.ts) to define validation rules for the sign-up form. This includes checks for non-empty fields, valid email and phone number formats, and password confirmation.
-   **React Hook Form:** The sign-up form at [`src/features/auth/components/auth/signup-form.tsx`](src/features/auth/components/auth/signup-form.tsx) was refactored to use `react-hook-form` with the `@hookform/resolvers/zod` adapter. This simplified form state management and enabled automatic validation with inline error messages.

### 2.2. Supabase Auth Integration

-   **Email/Password:** The sign-up and login flows were enhanced to be more secure and provide better user feedback. Server actions now perform server-side validation and return detailed error messages.
-   **Magic Link Login:** The main login form at [`src/features/auth/components/auth/auth-login-form.tsx`](src/features/auth/components/auth/auth-login-form.tsx) now includes a "Acceder con enlace mágico" option. This flow includes client-side validation to ensure an email is provided before sending the link.
-   **Google OAuth:** A reusable [`GoogleSignInButton`](src/features/auth/components/auth/google-sign-in-button.tsx) component was created and added to both the login and sign-up forms. This component handles the client-side logic for the Google OAuth flow.

### 2.3. User Profile Synchronization

-   **Database Trigger:** A new SQL migration file was created at [`supabase/migrations/20250821152000_create_user_profile_sync.sql`](supabase/migrations/20250821152000_create_user_profile_sync.sql). This migration creates a PostgreSQL function and trigger that automatically synchronizes new users from `auth.users` to the existing `public.user` table, as defined in [`prisma/schema.prisma`](prisma/schema.prisma).

### 2.4. User Feedback & UI

-   **Sonner Toasts:** The project now uses the `sonner` library for toast notifications. A centralized toast utility was created at [`src/lib/toast.ts`](src/lib/toast.ts) to provide a consistent way to display success and error messages.
-   **UI Redesign:** The login and sign-up forms were redesigned to match the layout and styling of the provided screenshots and reference project. This included adjustments to button colors, link placements, and the creation of a custom Google sign-in button.

## 3. Pending Issue: Foreign Key Constraint Violation

During testing, a runtime error was identified that occurs after a new user is successfully registered.

-   **Error:** `Foreign key constraint violated on the constraint: account_holder_profile_user_id_fkey`
-   **Location:** The error occurred in the `GET` handler of the `/api/account-holder/policies/list` route, specifically when trying to create an `AccountHolderProfile` record.
-   **Analysis:** The error indicated that the `userId` being used to create the `AccountHolderProfile` did not exist in the `user` table at the time of creation. This was caused by a race condition where the profile creation was attempted before the database trigger had synchronized the new user from `auth.users` to `public.user`.

### 3.1. Resolution

The issue has been resolved by removing the database trigger and implementing a server-side user synchronization utility, following the pattern used in the `loterix-suite` reference project.

-   **`syncUserWithDatabase` Utility:** A new utility function has been created at [`src/features/auth/utils/sync-user.ts`](src/features/auth/utils/sync-user.ts). This function checks if a user exists in the `public.user` table and creates them if they do not.
-   **Server-Side Integration:** The `signup` server action and the new `/auth/confirm` route handler now call `syncUserWithDatabase` immediately after a successful authentication event. This ensures that the user record is created in the `public.user` table before any subsequent operations that might depend on it, thus resolving the race condition.