# RLS Testing Scenarios

## 🔐 Authentication Tests

### Test 1: Valid Authentication
**Steps:**
1. POST `/auth/v1/token` with valid credentials
2. Verify `access_token` is received
3. Use token in subsequent requests

**Expected:** 200 response with JWT token

### Test 2: Invalid Authentication  
**Steps:**
1. POST `/auth/v1/token` with invalid credentials
2. Try API call without token

**Expected:** 401 Unauthorized

## 🚫 RLS Policy Tests

### Test 3: Account Holder Data Isolation
**Setup:** Login as Account Holder
**Test:**
```
GET /api/account-holder/policies/list
```
**Expected:** Only policies belonging to this account holder

### Test 4: Broker Access Restrictions
**Setup:** Login as Broker
**Test:**
```
GET /api/account-holder/policies/list
```
**Expected:** 403 Forbidden OR empty results (RLS filtering)

### Test 5: Cross-User Policy Access
**Setup:** Login as Account Holder A
**Test:** Try to access specific policy belonging to Account Holder B
```
GET /api/account-holder/auctions/{auction-id-from-other-user}
```
**Expected:** 404 Not Found or 403 Forbidden

### Test 6: Admin Bypass
**Setup:** Login as Admin
**Test:**
```
GET /api/account-holder/policies/list
```
**Expected:** Should see policies from all users (admin bypass)

## 🔧 JWT Role Validation Tests

### Test 7: Role Mismatch in JWT
**Setup:** Manually modify JWT payload (if possible)
**Test:** Access broker-only endpoints with account holder role
**Expected:** 403 Forbidden

### Test 8: Expired Token
**Setup:** Wait for token expiration or use expired token
**Test:** Any protected endpoint
**Expected:** 401 Unauthorized

## 📊 Data Filtering Tests

### Test 9: Search with RLS
**Setup:** Login as Account Holder
**Test:**
```
GET /api/account-holder/policies/list?search=test
```
**Expected:** Only results from current user's policies

### Test 10: Pagination with RLS
**Setup:** Login as Account Holder  
**Test:**
```
GET /api/account-holder/policies/list?page=1&limit=5
```
**Expected:** Count and results respect user boundaries

## 🎯 Business Logic Tests

### Test 11: Auction Access Control
**Setup:** Login as Account Holder A
**Test:** Try to access auction created by Account Holder B
**Expected:** No access to other user's auctions

### Test 12: Bid Visibility
**Setup:** Login as Broker A
**Test:** Try to see bids placed by Broker B on same auction
**Expected:** Should see auction details but not other brokers' sensitive bid info

## 🔍 Debugging RLS Issues

### Common Issues:
1. **Empty Results**: Check if RLS is filtering too aggressively
2. **403 Errors**: Verify JWT role matches expected role
3. **Token Issues**: Ensure Bearer token is properly set
4. **Database Policies**: Check if policies exist and are enabled

### Debug Commands:
```sql
-- Check active policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';

-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = true;
```

## 📋 Test Checklist

- [ ] Account Holder can access own policies
- [ ] Account Holder cannot access other users' policies  
- [ ] Broker can send bids but not see account holder policies
- [ ] Admin can access all data
- [ ] Unauthenticated requests are rejected
- [ ] Cross-user auction access is blocked
- [ ] Search respects user boundaries
- [ ] Pagination works with RLS filtering
- [ ] JWT token expiration is handled
- [ ] Invalid tokens are rejected

## 🚀 Advanced Tests

### Load Testing with RLS
Test performance impact of RLS policies under load:
```bash
# Example with Apache Bench
ab -n 1000 -c 10 -H "Authorization: Bearer YOUR_TOKEN" \
   http://localhost:3000/api/account-holder/policies/list
```

### Concurrent User Testing
Test multiple users accessing system simultaneously to ensure no data leakage between sessions.