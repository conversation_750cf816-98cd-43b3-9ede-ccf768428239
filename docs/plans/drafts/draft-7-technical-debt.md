🟡 MINOR FINDING: Duplicate User Fetching Logic
Issue: Multiple ways to get current user data

Files:
utils/server-auth.ts - getCurrentUser() utility function
actions/get-current-user.ts - Server action wrapper around AuthService
Impact: Slight redundancy in user data access patterns
Priority: LOW (both serve different purposes)


🚨 Auctions: Unused base component (~200 lines cleanup)
🚨 Policies: Server actions vs API routes (~150 lines cleanup)

Low Priority Items (Future Consideration)
Auth Schema Centralization (~50 lines optimization)

Move src/features/auth/schemas/ to centralized architecture
Risk: Medium (active usage, requires import path updates)
Benefit: Architectural consistency improvement
Timeline: Future refactoring phase
Auction Card Consolidation (Future Enhancement)

Consider creating new base component for 7 auction card variants
Note: Previous base component was well-designed but unused
Approach: Incremental refactoring when touching auction cards

AUCTION CARDS ANALYSIS:
 AuctionDetailsView.tsx - ✅ Actively used, large but necessary
 SelectBestOfferModal.tsx - ✅ Specific use case, validated
 auction-card-base.tsx - 🚨 UNUSED BASE COMPONENT
 auction-card.tsx - 🔴 Should extend base component
 available-auction-card.tsx - 🔴 Duplicates asset emoji logic
 participating-auction-card.tsx - 🔴 Duplicates premium formatting
 won-auction-card.tsx - 🔴 Duplicates time display logic
 confirmed-auction-card.tsx - 🔴 Duplicates client masking
 lost-auction-card.tsx - 🔴 Duplicates coverage display
 completed-auction-card.tsx - 🔴 Should use base component

src/lib/db.ts - Database singleton
src/lib/logger.ts - Logging infrastructure
src/lib/utils.ts - Core utilities

Auth schemas (potential duplication)
Time formatting utilities (possible overlap)
Policy transformation utilities (multiple transformers)

components/upload/PolicyProgressStepper.tsx - 🔴 UNUSED COMPONENT

 actions/get-policy-details.ts - 🚨 DUPLICATES API ROUTE
 actions/get-policy-insured-parties.ts - 🚨 DUPLICATES API ROUTE