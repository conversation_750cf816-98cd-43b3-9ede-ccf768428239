I. <PERSON><PERSON> en el modelo de datos

Los datos son lo más importante, ahorita la estructura esta basada en domains.

Ver ejemplo de Password settings que se reutiliza de forma adecuada tanto la vista de broker, como la de admin y el account holder.

1. Revisar el documento de notion, cambios que se deberían de hacer al prisma schema. Debemos añadir una tabla auxiliar a la tabla principal de Auctions, esta se llamará AuctionStates.
Notas de Notion:

**Notes:**

- Si es desierto, debería ir directo a EXPIRED
- EXPIRED Y CANCELED DEBEN LIBERAR DINERO
- Hacer esto para la relación entre pólizas y creación de subastas.
- Considera la tabla de estados de subasta aparte y para policy.

**Ejemplo de States Auxiliary table**

Ejemplo de como te beneficiaría tener a una tabla auxiliar como AuctionStates para manejar los cambios de estados, puedes evaluar a un auction su vida e historia de cliente.


## Los datos te dicen los eventos y la historia de tu cliente

### Scenario 1: Expired Auction

| id | auction_id | state | created_at | time_lapse |
|---|---|---|---|---|---|
| a | 1 | OPEN | timestamp | - |
| b | 1 | CLOSED | timestamp | 2 days |
| c | 1 | EXPIRED | timestamp | 14 days |

### Scenario 2: Signed Auction

| id | auction_id | state | created_at | time_lapse |
|---|---|---|---|---|---|
| d | 2 | OPEN | timestamp | - |
| e | 2 | CLOSED | timestamp | 2 days |
| f | 2 | SIGNED | timestamp | 1 day |

Los time_lapse se pueden generar usando queries, no hace falta que existan en la base de datos como una columna.

1. Arreglar el estado de la subasta, como vamos a tener esta nueva entidad. Ya no va a ser necesario que estemos usando booleanos y estados en el modelo principal de Auction para guardar los estados de la subasta a lo largo del tiempo; Cuando pasa de Open a closed, cuando es actualizada a signed policy o bien expired o es canceled.

2. Dentro del modelo Auction, con esta nueva forma de manejar los estados debemos de revisar si tiene sentido ciertos campos como pueden ser: Selected_at, signatureConfirmed, signatureConfirmedAt, finalizedAt, startDate, endDate, createdAt, updatedAt

3. Debemos seguir este mismo principio para los estados del modelo de Policy, usar una tabla auxiliar llamada Policystates

II. Proceso de Desarollo.

Antes de hacer estos cambios debemos de hacer un plan prd, guardalo en la carpeta docs/plans en donde se listen todos los archivos que serán impactados con estos cambios. No solo el prisma schema sino también los archivos impactados por este nuevo cambio en el modelo de datos, ver APIs, data transformations, Front, etc.

Así de esa forma se guardará una memoria de que cosas hacer "to dos" así cuando tu memoria se vaya perdiendo puedas volver aquí y retomar sin perder calidad.

Ten en cuenta por favor lo siguiente, también:

Readme

Changelog

archicture.prd

claude.md

III. Control de calidad

Barrido de calidad usando los comandos de claude. 

IV. Crear Changelog

Utiliza el comando generar changelog