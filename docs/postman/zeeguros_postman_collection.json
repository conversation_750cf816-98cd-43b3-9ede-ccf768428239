{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Zeeguros API Collection", "description": "Complete API testing collection with RLS validation for Zeeguros platform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentication", "item": [{"name": "<PERSON>gin - Account Holder", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.access_token);", "    pm.environment.set('refresh_token', response.refresh_token);", "    console.log('✅ Account Holder logged in - Role: ACCOUNT_HOLDER');", "} else {", "    console.log('❌ <PERSON><PERSON> failed:', pm.response.text());", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "apikey", "value": "{{SUPABASE_ANON_KEY}}"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Abcdef7*\"\n}"}, "url": {"raw": "{{SUPABASE_URL}}/auth/v1/token?grant_type=password", "host": ["{{SUPABASE_URL}}"], "path": ["auth", "v1", "token"], "query": [{"key": "grant_type", "value": "password"}]}}}, {"name": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.access_token);", "    pm.environment.set('refresh_token', response.refresh_token);", "    console.log('✅ Broker logged in - Role: BROKER');", "} else {", "    console.log('❌ <PERSON><PERSON> failed:', pm.response.text());", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "apikey", "value": "{{SUPABASE_ANON_KEY}}"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Abcdef7*\"\n}"}, "url": {"raw": "{{SUPABASE_URL}}/auth/v1/token?grant_type=password", "host": ["{{SUPABASE_URL}}"], "path": ["auth", "v1", "token"], "query": [{"key": "grant_type", "value": "password"}]}}}, {"name": "<PERSON><PERSON> - <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.access_token);", "    pm.environment.set('refresh_token', response.refresh_token);", "    console.log('✅ Admin logged in - Role: ADMIN');", "} else {", "    console.log('❌ <PERSON><PERSON> failed:', pm.response.text());", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "apikey", "value": "{{SUPABASE_ANON_KEY}}"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Abcdef7*\"\n}"}, "url": {"raw": "{{SUPABASE_URL}}/auth/v1/token?grant_type=password", "host": ["{{SUPABASE_URL}}"], "path": ["auth", "v1", "token"], "query": [{"key": "grant_type", "value": "password"}]}}}, {"name": "Get User Info", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const user = pm.response.json();", "    console.log('User Role:', user.user_metadata?.role);", "    console.log('User ID:', user.id);", "    console.log('Email:', user.email);", "}"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "apikey", "value": "{{SUPABASE_ANON_KEY}}"}], "url": {"raw": "{{SUPABASE_URL}}/auth/v1/user", "host": ["{{SUPABASE_URL}}"], "path": ["auth", "v1", "user"]}}}]}, {"name": "👤 Account Holder APIs", "item": [{"name": "Select Auction Winners", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is appropriate', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 400, 401, 403, 404]);", "});", "", "if (pm.response.code === 200 || pm.response.code === 201) {", "    const jsonData = pm.response.json();", "    console.log('✅ Winners selected successfully');", "} else {", "    console.log('❌ Winner selection failed:', pm.response.text());", "}"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"selectedBidIds\": [\"bid_id_1\", \"bid_id_2\", \"bid_id_3\"]\n}"}, "url": {"raw": "{{BASE_URL}}/api/account-holder/auctions/AUCTION_ID/winners", "host": ["{{BASE_URL}}"], "path": ["api", "account-holder", "auctions", "AUCTION_ID", "winners"]}, "description": "Select up to 3 winners for a closed auction (Account Holder only)"}}, {"name": "List Policies", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 401/403 for unauthorized', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 401, 403]);", "});", "", "if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    pm.test('Response has policies data', function () {", "        pm.expect(jsonData).to.have.property('data');", "    });", "    console.log('📋 Policies returned:', jsonData.data?.length || 0);", "} else {", "    console.log('🚫 Access denied - RLS working correctly');", "}"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/account-holder/policies/list?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "account-holder", "policies", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "ACTIVE", "disabled": true}, {"key": "search", "value": "test", "disabled": true}]}}}, {"name": "Get Profile", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 401/403', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 401, 403]);", "});", "", "if (pm.response.code === 200) {", "    console.log('✅ Profile access granted');", "} else {", "    console.log('🚫 Profile access denied - RLS working');", "}"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/account-holder/settings/profile", "host": ["{{BASE_URL}}"], "path": ["api", "account-holder", "settings", "profile"]}}}, {"name": "List Auctions", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 401/403', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 401, 403]);", "});", "", "if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    console.log('🏺 Auctions returned:', jsonData.data?.length || 0);", "} else {", "    console.log('🚫 Auction access denied - RLS working');", "}"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/account-holder/auctions/list?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "account-holder", "auctions", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}, {"name": "🏢 Broker APIs", "item": [{"name": "Send Offer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is appropriate', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 400, 401, 403, 404]);", "});", "", "if (pm.response.code === 200 || pm.response.code === 201) {", "    console.log('✅ Offer sent successfully');", "} else {", "    console.log('❌ Offer failed:', pm.response.text());", "}"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"auctionId\": \"REPLACE_WITH_ACTUAL_AUCTION_ID\",\n  \"amount\": 250.50,\n  \"coverages\": [\n    {\n      \"type\": \"MANDATORY_LIABILITY\",\n      \"limit\": 50000,\n      \"description\": \"Mandatory liability coverage\"\n    }\n  ]\n}"}, "url": {"raw": "{{BASE_URL}}/api/auctions/send-offer", "host": ["{{BASE_URL}}"], "path": ["api", "auctions", "send-offer"]}}}]}, {"name": "🔧 Admin APIs", "item": [{"name": "Update Policy Status", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is appropriate', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 401, 403, 404]);", "});", "", "if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    console.log('✅ Policy status updated successfully');", "    console.log('New status:', jsonData.data?.status);", "} else {", "    console.log('❌ Policy status update failed:', pm.response.text());", "}"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/admin/policies/POLICY_ID/status", "host": ["{{BASE_URL}}"], "path": ["api", "admin", "policies", "POLICY_ID", "status"]}, "description": "Update policy status (Admin only). Status can be: ACTIVE, RENEW_SOON, EXPIRED"}}]}, {"name": "📄 General APIs", "item": [{"name": "Get Policy Details", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is appropriate', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 401, 403, 404]);", "});", "", "if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    console.log('✅ Policy details retrieved');", "    console.log('Policy Number:', jsonData.data?.policyNumber);", "    console.log('Status:', jsonData.data?.status);", "} else {", "    console.log('❌ Policy details failed:', pm.response.text());", "}"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/policies/POLICY_ID", "host": ["{{BASE_URL}}"], "path": ["api", "policies", "POLICY_ID"]}, "description": "Get detailed policy information with RLS checks"}}, {"name": "Create Policy", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is appropriate', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 400, 401, 403]);", "});", "", "if (pm.response.code === 200 || pm.response.code === 201) {", "    console.log('✅ Policy created successfully');", "} else {", "    console.log('❌ Policy creation failed');", "}"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "description": "Upload a PDF policy document", "type": "file", "src": []}]}, "url": {"raw": "{{BASE_URL}}/api/policies/create", "host": ["{{BASE_URL}}"], "path": ["api", "policies", "create"]}}}]}, {"name": "🧪 RLS Testing Scenarios", "item": [{"name": "❌ Cross-User Access Test (Should Fail)", "event": [{"listen": "test", "script": {"exec": ["pm.test('RLS prevents cross-user access', function () {", "    // Should return 403, 401, or empty results due to RLS", "    if (pm.response.code === 200) {", "        const jsonData = pm.response.json();", "        // If 200, should have empty or no data due to RLS filtering", "        pm.expect(jsonData.data || jsonData).to.satisfy(function(data) {", "            return Array.isArray(data) ? data.length === 0 : !data;", "        });", "        console.log('✅ RLS working: Empty results returned');", "    } else {", "        pm.expect(pm.response.code).to.be.oneOf([401, 403]);", "        console.log('✅ RLS working: Access denied');", "    }", "});"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/account-holder/policies/list", "host": ["{{BASE_URL}}"], "path": ["api", "account-holder", "policies", "list"]}, "description": "Try to access account holder policies while logged in as broker - should fail or return empty due to RLS"}}, {"name": "🔍 Token Validation Test", "event": [{"listen": "test", "script": {"exec": ["pm.test('Invalid token should be rejected', function () {", "    pm.expect(pm.response.code).to.be.oneOf([401, 403]);", "    console.log('✅ Invalid token correctly rejected');", "});"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "invalid-token-123", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/account-holder/policies/list", "host": ["{{BASE_URL}}"], "path": ["api", "account-holder", "policies", "list"]}, "description": "Test with invalid token - should be rejected"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "BASE_URL", "value": "http://localhost:3000", "type": "string"}, {"key": "SUPABASE_URL", "value": "", "type": "string"}, {"key": "SUPABASE_ANON_KEY", "value": "", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}]}