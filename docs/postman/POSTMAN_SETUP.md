# Postman Setup Guide for Zeeguros API

## 📦 Files to Import

1. **Collection**: `zeeguros_postman_collection.json` - All API endpoints and tests
2. **Environment**: `postman_environment.json` - Variables and configuration

## 🚀 Quick Setup Steps

### Step 1: Import Collection
1. Open **Postman**
2. Click **Import** → **Upload Files**
3. Select `zeeguros_postman_collection.json`
4. Click **Import**

### Step 2: Import Environment
1. Click **Import** → **Upload Files** 
2. Select `postman_environment.json`
3. Click **Import**
4. **Activate Environment**: Top-right dropdown → Select "Zeeguros API Environment"

### Step 3: Configure Your Supabase Values
Click on **Environment** (eye icon) → **Edit** and update:

```
SUPABASE_URL = https://your-project-id.supabase.co
SUPABASE_ANON_KEY = your-actual-anon-key-here
```

## 🔐 Testing Authentication & RLS

### Test Flow 1: Account Holder Access ✅
```bash
1. Run "Login - Account Holder"
   → Should set access_token automatically
   → Console shows: "✅ Account Holder logged in - Role: ACCOUNT_HOLDER"

2. Run "List Policies" 
   → Should return policies owned by this account holder
   → Console shows: "📋 Policies returned: X"

3. Run "Get Profile"
   → Should return account holder profile data
   → Console shows: "✅ Profile access granted"
```

### Test Flow 2: Broker Cross-Access ❌ (Should Fail)
```bash
1. Run "Login - Broker"
   → Should set access_token for broker
   → Console shows: "✅ Broker logged in - Role: BROKER"

2. Run "List Policies" (Account Holder endpoint)
   → Should fail or return empty due to RLS
   → Console shows: "🚫 Access denied - RLS working correctly"

3. Run "❌ Cross-User Access Test"
   → Validates RLS is preventing cross-user data access
   → Console shows: "✅ RLS working: Access denied" OR "✅ RLS working: Empty results returned"
```

### Test Flow 3: Admin Access ✅ (Should Work)
```bash
1. Run "Login - Admin"
   → Should set access_token for admin
   → Console shows: "✅ Admin logged in - Role: ADMIN"

2. Run any endpoint
   → Should have broader access due to admin role
```

## 🧪 Available Test Users

All test users are created by `npm run db:seed`:

| Role | Email | Password | Description |
|------|--------|----------|-------------|
| **ACCOUNT_HOLDER** | `<EMAIL>` | `Abcdef7*` | Policy owner |
| **BROKER** | `<EMAIL>` | `Abcdef7*` | Insurance broker |
| **ADMIN** | `<EMAIL>` | `Abcdef7*` | System administrator |

## 📋 Environment Variables Reference

| Variable | Purpose | Example |
|----------|---------|---------|
| `BASE_URL` | Your app URL | `http://localhost:3000` |
| `SUPABASE_URL` | Supabase project URL | `https://xyz.supabase.co` |
| `SUPABASE_ANON_KEY` | Public API key | `eyJhbGciOiJIUzI1NiI...` |
| `access_token` | JWT token (auto-set) | Set by login requests |
| `CURRENT_USER_ROLE` | Active user role | Updated by login scripts |

## 🔧 Troubleshooting

### Issue: Login Returns 400/401
**Solution:** Check your Supabase URL and anon key are correct

### Issue: API Returns 401 Unauthorized  
**Solution:** 
1. Make sure you ran a login request first
2. Check `access_token` is set in environment
3. Verify your dev server is running: `npm run dev`

### Issue: Getting Empty Results
**This might be correct!** - RLS policies filter data by user. Empty results when logged in as the wrong role means RLS is working.

### Issue: Cross-User Access Working (Bad!)
**Red Flag:** If broker can see account holder data, RLS policies aren't working
**Solution:** Run `npm run db:apply-policies` to ensure RLS is enabled

## 📊 What Each Test Validates

| Test | Purpose | Expected Result |
|------|---------|----------------|
| **Login Requests** | Authentication works | Sets JWT token |
| **List Policies** | RLS filters by user | Only own data returned |
| **Cross-User Access** | RLS blocks unauthorized access | 403 or empty results |
| **Token Validation** | Invalid tokens rejected | 401 for bad tokens |
| **Get User Info** | JWT metadata correct | Shows user role/id |

## 🎯 Success Criteria

✅ **RLS is working correctly when:**
- Account holders only see their own policies
- Brokers cannot access account holder endpoints
- Invalid tokens are rejected
- Admin users can access broader data
- Cross-user API calls fail or return empty

❌ **RLS needs fixing when:**
- Users can see other users' data
- Unauthorized endpoints return data
- No authentication required for protected routes

## 🚀 Ready to Test!

1. **Start your dev server**: `npm run dev`
2. **Seed test data**: `npm run db:seed` 
3. **Import both files** into Postman
4. **Update Supabase environment** variables
5. **Run the test flows** above

Your API security and RLS policies will be thoroughly validated! 🛡️