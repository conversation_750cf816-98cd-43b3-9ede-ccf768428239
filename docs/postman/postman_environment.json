{"id": "zeeguros-api-environment-uuid", "name": "Zeeguros API Environment", "values": [{"key": "BASE_URL", "value": "http://localhost:3000", "type": "default", "enabled": true}, {"key": "SUPABASE_URL", "value": "https://etzouzmoluegjbfikshb.supabase.co", "type": "default", "enabled": true, "description": "Your Supabase project URL from .env.local"}, {"key": "SUPABASE_ANON_KEY", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.u196zNV5jvb6Ipt6BQOA4ZnGWo5bG8PQ_rmcQxqKHW0", "type": "secret", "enabled": true, "description": "Your Supabase anonymous key from .env.local"}, {"key": "access_token", "value": "", "type": "secret", "enabled": true, "description": "JWT access token - automatically set by login requests"}, {"key": "refresh_token", "value": "", "type": "secret", "enabled": true, "description": "JWT refresh token - automatically set by login requests"}, {"key": "ACCOUNT_HOLDER_EMAIL", "value": "<EMAIL>", "type": "default", "enabled": true, "description": "Test account holder email from seeding"}, {"key": "BROKER_EMAIL", "value": "<EMAIL>", "type": "default", "enabled": true, "description": "Test broker email from seeding"}, {"key": "ADMIN_EMAIL", "value": "<EMAIL>", "type": "default", "enabled": true, "description": "Test admin email from seeding"}, {"key": "TEST_PASSWORD", "value": "Abcdef7*", "type": "secret", "enabled": true, "description": "Default password for all test users"}, {"key": "CURRENT_USER_ROLE", "value": "", "type": "default", "enabled": true, "description": "Currently logged in user role - updated by login scripts"}, {"key": "CURRENT_USER_ID", "value": "", "type": "default", "enabled": true, "description": "Currently logged in user ID - updated by login scripts"}, {"key": "SAMPLE_AUCTION_ID", "value": "replace-with-actual-auction-id", "type": "default", "enabled": true, "description": "Sample auction ID for testing - get from auction list API"}, {"key": "SAMPLE_POLICY_ID", "value": "replace-with-actual-policy-id", "type": "default", "enabled": true, "description": "Sample policy ID for testing - get from policy list API"}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-01T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}