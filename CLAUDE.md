# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development
- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build application for production (includes TypeScript checking)
- `npm run start` - Start production server
- `npm run lint && npm run type-check` - **MANDATORY after every file change** - Lint code and check TypeScript types

### Architecture Quality Assurance
- `npm run arch-scan` - **Architecture violations scanner** - Comprehensive automated scan for architectural compliance and code quality
- `npm run arch-scan:verbose` - Detailed architecture scan with verbose output for debugging

### Custom Commands
- `/generate-changelog "description"` - **Generate or update daily changelog** - Use before ending sessions

### Database Operations
- `npm run db:rebuild` - **Complete database rebuild** - Drops all data, applies migrations, sets up infrastructure (extensions, functions, cron jobs), and seeds test data
- `npm run migrate:dev` - Run Prisma migrations for development
- `npx prisma generate` - **MANDATORY after schema changes** - Regenerate Prisma client types
- `npm run db:seed` - Seed database with comprehensive test data
- `npm run db:studio` - Open Prisma Studio for database management

### Database Infrastructure
- `npm run db:setup-infrastructure` - Set up Supabase extensions, functions, and cron jobs
- `npm run db:apply-policies` - Apply Row-Level Security policies

### Test Users (created by seeding)
- Account Holder: `<EMAIL>` / `Abcdef7*`
- Broker: `<EMAIL>` / `Abcdef7*` 
- Admin: `<EMAIL>` / `Abcdef7*`

## Architecture

### Screaming Architecture (95% Compliant)
This codebase follows **Screaming Architecture** principles with business domain organization at its core.

**Core Structure:**
```
src/
├── app/                    # Next.js App Router - role-based organization
│   ├── (public)/          # Public routes (login, signup)
│   ├── account-holder/    # Account holder dashboard and features
│   ├── broker/            # Broker dashboard and features  
│   ├── admin/             # Admin dashboard and features
│   └── api/               # Server-side API routes (domain-based)
│       ├── auctions/      # Auction operations
│       ├── policies/      # Policy management
│       ├── documents/     # Secure document access
│       └── profiles/      # User profile management
├── features/              # Business domain features (primary organization)
│   ├── auctions/          # ✅ Comprehensive auction business logic
│   ├── policies/          # ✅ Complete policy management
│   ├── auth/              # ✅ Authentication & profile management
│   └── support/           # ✅ Unified support functionality
├── components/
│   ├── ui/                # 50+ Generic shadcn/ui components
│   └── shared/            # 16 App-wide, non-domain components
└── lib/                   # Pure infrastructure layer
    ├── api/               # Route factory & middleware
    ├── auction/           # Winner selection & business hours
    ├── email/             # Generic Brevo service
    ├── schemas/           # Centralized validation schemas
    ├── services/          # State management & upload services
    └── supabase/          # Client/server configuration
```

### Key Principles
- **Domain-Driven Structure**: Organize by business domain (user roles: account-holder, broker, admin)
- **DRY Compliance**: Single source of truth for all logic
- **Role-Based Access**: All routes and features organized by user roles
- **Server-Side Security**: All database operations through authenticated API routes

## Technology Stack

### Core Framework
- **Next.js 15+** with App Router and Turbopack
- **TypeScript** (100% coverage, strict mode)
- **Prisma ORM** with PostgreSQL
- **Supabase** for auth, database hosting, and Row-Level Security

### UI/Styling
- **Tailwind CSS** with custom design system
- **Radix UI** components via shadcn/ui
- **React Hook Form** with Zod validation
- **Lucide React** icons

### External Services
- **Google Gemini API** for AI-powered policy document extraction
- **Cloudflare R2** for document storage
- **Brevo** for email notifications

## Database Schema

### Core Models
- **User**: Base user with role (`ACCOUNT_HOLDER`, `BROKER`, `ADMIN`)
- **Policy**: Insurance policy with complete lifecycle (DRAFT → ACTIVE → RENEW_SOON → EXPIRED)
- **Auction**: Reverse auction system for insurance policies with working hours logic
- **Documentation**: File management linked to policies and auctions

### Important Business Logic

#### Auction Working Hours
- **Working Hours**: 06:00-23:59 Madrid timezone, Monday-Friday (18h/day)
- **Duration**: 48 working hours (~2.67 business days)
- **Implementation**: `src/lib/auction/working-hours.ts`

#### Policy Lifecycle Management
- Complete workflow with state transitions: DRAFT → ACTIVE → RENEW_SOON → EXPIRED
- **State History Tracking**: Comprehensive AuctionState and PolicyState tables for complete audit trail
- AI-powered document extraction using Google Gemini API with configurable validation
- Integrated with auction system for policy renewal and winner selection

## Schema Management System (CRITICAL)

### Centralized Schema Architecture
This codebase uses a unified approach where all validation schemas are centralized in `src/lib/schemas/index.ts`:

```
Prisma Types → Centralized App Schemas → API/UI Validation
```

### Usage Patterns (MANDATORY)

#### ✅ CORRECT: Import from centralized schemas
```typescript
// API Routes
import { PolicySchemas, CommonSchemas } from "@/lib/schemas";
.validateBody(PolicySchemas.CreateForm)

// UI Forms  
import { PolicySchemas } from "@/lib/schemas";
const form = useForm({ resolver: zodResolver(PolicySchemas.CreateForm) });

// Type Imports
import { PolicyStatus, AssetType } from "@prisma/client";
```

### Prisma Schema as Single Source of Truth (CRITICAL)

ALL implementations MUST use Prisma schema as the foundation:

#### **✅ CORRECT: Always Use Prisma Schema**
```typescript
// 1. Import types from Prisma (never manual definitions)
import { PolicyStatus, AssetType, Role, User, Policy } from "@prisma/client";

// 2. Use Prisma enums in validation schemas
import { PolicySchemas } from "@/lib/schemas";
const createSchema = z.object({
  status: z.nativeEnum(PolicyStatus), // ✅ Uses Prisma enum
  type: z.nativeEnum(AssetType),      // ✅ Uses Prisma enum
});

// 3. Database operations through Prisma client
import { db } from "@/lib/db";
const policy = await db.policy.create({
  data: {
    status: PolicyStatus.DRAFT,  // ✅ Prisma enum
    accountHolderId: user.id,    // ✅ Prisma relationship
  }
});
```

#### **❌ INCORRECT: Manual Type Definitions**
```typescript
// DON'T DO THIS - Manual enums/types
type PolicyStatus = "DRAFT" | "ACTIVE" | "EXPIRED";
const status = "DRAFT"; // ❌ Hardcoded string

// DON'T DO THIS - Manual interface
interface Policy {
  id: string;
  status: string; // ❌ Should use PolicyStatus enum
}
```

#### **Mandatory Workflow for New Features**
1. **Design Database First**: Define models/enums in `prisma/schema.prisma`
2. **Generate Types**: Run `npx prisma generate` to update Prisma client
3. **Create Validation**: Add schemas to `src/lib/schemas/index.ts` using Prisma enums
4. **Implement Logic**: Use Prisma types throughout API routes and components

### Adding New Schemas
1. **Update Prisma Schema**: Add new models/enums to `prisma/schema.prisma` FIRST
2. **Generate Types**: Run `npx prisma generate` to get updated types
3. **Update Central Schemas**: Add validation schemas to `src/lib/schemas/index.ts`
4. **Import in Components**: Use centralized schemas in API routes and UI forms

## Development Guidelines

### File Organization (CRITICAL)
- **Domain Features**: Place all business logic in appropriate `src/features/` directory
- **Role-Specific Routes**: Place in `src/app/{role}/` (account-holder, broker, admin)
- **Shared Components**: Use `src/components/shared/` for app-wide components
- **UI Components**: Only generic shadcn/ui components in `src/components/ui/`
- **Infrastructure**: Database, auth, and utility code in `src/lib/`

### Security Requirements (NON-NEGOTIABLE)
- **Server-Side Operations**: All database operations must go through API routes
- **No Client-Side DB Access**: NEVER use `@/lib/supabase/client` for database operations
- **Mandatory Pattern**: Client → API Route → Server-side Supabase → Database
- **Row-Level Security**: RLS policies enforce data access controls
- **Authentication**: Supabase Auth with JWT validation server-side

### Code Quality Standards (MANDATORY)

#### **ESLint Workflow - Quality Assurance**
EVERY file creation or modification MUST follow this workflow:

```bash
# 1. After creating/modifying any file, ALWAYS run ESLint
npm run lint

# 2. Fix ALL errors and warnings before proceeding
# 3. Verify no new issues introduced
```

#### **Quality Requirements**
- **TypeScript**: Strict mode enabled, zero build errors tolerance
- **ESLint**: ALL errors and warnings MUST be fixed before committing changes
- **Language Conventions**: 
  - Code (variables, functions, files): English
  - UI text (labels, buttons, messages): Professional Spanish
- **Prisma Schema**: ALL types and enums MUST come from `@prisma/client` (never manual definitions)
- **Schema Validation**: MUST use centralized schemas from `@/lib/schemas`
- **Database First**: Always update `prisma/schema.prisma` BEFORE implementing features

#### **Pre-Commit Checklist (MANDATORY)**
✅ Run `npm run lint && npm run type-check` and fix all issues  
✅ Run `npx prisma generate` if schema was modified  
✅ Test functionality works as expected  
✅ Run `/generate-changelog "session description"` before ending long sessions

### Component Standards
- **Single Responsibility**: Each component has one clear purpose
- **Proper Typing**: All props and functions properly typed with TypeScript
- **Accessibility**: Consider accessibility in all UI components
- **Error Handling**: Consistent error handling patterns throughout

## Service Architecture

### PolicyUploadService
Centralized service for all file upload operations:
- **Location**: `src/lib/services/policy-upload.service.ts`
- **Features**: AI validation, Cloudflare R2 uploads, documentation records
- **Usage**: Both new policy creation and auction policy uploads

### Service Architecture Principles (MANDATORY)

#### **Generic Services - DRY & Factory Pattern**
All services MUST be designed for reusability across the entire codebase:

**✅ CORRECT - Generic Brevo Service:**
```typescript
import { brevoEmailService } from "@/lib/email/brevo.service";

// Single method handles any template + parameters
await brevoEmailService.sendTemplateEmail(
  { email: "<EMAIL>", name: "User" },
  5, // Any Brevo template ID
  { customParam: "any value", anotherParam: 123 }
);
```

**❌ INCORRECT - Domain-Specific Methods:**
```typescript
// Don't create auction-specific methods
await brevoEmailService.sendAuctionClosedNotification(data);
```

#### **Service Design Principles**
- **Generic & Reusable**: Services accept template IDs + key-value parameters
- **Factory Pattern**: Single methods that handle multiple use cases
- **KISS**: Simple interfaces, no domain-specific logic in services
- **Template-Based**: External template management (Brevo dashboard, not code)

### Key Services
- **Email Service**: `src/lib/email/brevo.service.ts` - Generic template-based notifications
- **PolicyUploadService**: `src/lib/services/policy-upload.service.ts` - AI validation, R2 uploads
- **Authentication**: `src/features/auth/` - Supabase Auth integration
- **Document Management**: Cloudflare R2 with secure server-side operations

## API Architecture

### Server-Side Security (MANDATORY)
```
Client → Next.js API Route → Server-side Supabase → Database
```

### API Structure
```
src/app/api/
├── account-holder/        # Account holder operations
├── broker/               # Broker operations  
├── admin/               # Admin operations
├── policies/            # Policy management
├── auctions/            # Auction operations
└── documents/           # Document management
```

### API Architecture Principles (MANDATORY)

#### **Factory Pattern - DRY & KISS**
ALL API routes MUST use the centralized factory pattern:

```typescript
import { createApiRoute } from "@/lib/api/route-factory";
import { PolicySchemas } from "@/lib/schemas";
import { Role } from "@prisma/client";

export const POST = createApiRoute()
  .auth(Role.ACCOUNT_HOLDER) // Type-safe role validation
  .validateBody(PolicySchemas.CreateForm) // Centralized schema validation
  .handler(async ({ user, body }) => {
    // Business logic here
    return result;
  });
```

#### **Core Principles**
- **DRY**: Single source of truth for authentication, validation, error handling
- **KISS**: Simple, consistent API structure across all endpoints  
- **Factory Pattern**: Centralized route creation eliminates boilerplate
- **Type Safety**: Prisma enums, never hardcoded strings
- **Server-Side Only**: All database operations through authenticated API routes

### Security Checklist for API Routes
- ✅ Server-side authentication with role validation
- ✅ Zod schema validation for inputs
- ✅ Proper error handling without exposing internals
- ✅ File operations handled server-side only

## Important Notes

### Known Issues
- Database seeding with `SIGNED_POLICY` status may cause issues (see changelog)
- Edge Function deployment requires Docker for local development

### Recent Updates (September 2025)
- **Auxiliary State Tables**: New AuctionState and PolicyState models for comprehensive state history tracking
- **UI/UX Enhancements**: Consistent button hover states and visual hierarchy improvements  
- **PolicyUploadService**: Unified file upload logic with AI validation and Cloudflare R2 integration
- **Winner Selection System**: Sophisticated algorithm with 70% price, 30% coverage quality weighting
- **Authentication Architecture**: Complete role-based system with profile resolution utilities
- **Brevo Email Service**: Template-based notifications with generic factory pattern
- **Schema Architecture**: Centralized validation schemas with Prisma as single source of truth

### Configuration
- Environment variables defined in `.env.example`
- Prisma generates client to `node_modules/.prisma/client`

### Color Palette (Brand Standards)
- White (`#FFFFFF`)
- Black (`#000000`) 
- Aquamarine Green (`#6BE1A6`)

## Critical Anti-Patterns (DO NOT DO)

### **Architecture Violations**
- ❌ Never create technical domains (`src/app/auctions/`, `src/app/policies/`)
- ❌ Never use client-side Supabase for database operations
- ❌ Never create multiple Prisma clients (use singleton from `src/lib/db.ts`)
- ❌ Never place domain-specific logic in generic locations

### **DRY & Factory Pattern Violations**
- ❌ Never create API routes without `createApiRoute()` factory
- ❌ Never duplicate authentication/validation logic across routes
- ❌ Never create domain-specific service methods (use generic services)
- ❌ Never hardcode strings (use Prisma enums: `Role.ACCOUNT_HOLDER`, not `"ACCOUNT_HOLDER"`)

### **Service Design Violations**
- ❌ Never create HTML boilerplate in services (use external templates)
- ❌ Never create auction-specific, policy-specific methods (use generic APIs)
- ❌ Never duplicate file upload logic (use PolicyUploadService)

### **Schema & Security Violations**
- ❌ Never create manual schema definitions (use `@/lib/schemas` instead)
- ❌ Never create manual type definitions (use Prisma types from `@prisma/client`)
- ❌ Never use hardcoded strings for enums (use `PolicyStatus.DRAFT`, not `"DRAFT"`)
- ❌ Never define interfaces that duplicate Prisma models
- ❌ Never skip `npx prisma generate` after schema changes
- ❌ Never expose sensitive data in logs or error messages
- ❌ Never skip server-side validation or authentication

### **Code Quality Violations**
- ❌ Never skip `npm run lint && npm run type-check` after creating/modifying files
- ❌ Never commit code with ESLint errors or warnings
- ❌ Never use `eslint-disable` comments without strong justification
- ❌ Never ignore TypeScript compilation errors

### **Code Style Rules (ENFORCE STRICTLY)**

#### **Logging Standards**
- ❌ **NEVER use console.log/error/warn/info/debug** - Use structured logging instead
- ✅ **ALWAYS import and use**: `import { logger } from "@/lib/logger";`
- ✅ **Use appropriate log levels**:
  ```typescript
  logger.debug("Development info", { context });    // Development only
  logger.info("General info", { context });         // General flow
  logger.warn("Warning message", { context });      // Potential issues
  logger.error("Error occurred", error, { context }); // Errors
  logger.business("Business event", { context });   // Business logic events
  ```

#### **Type Safety Rules**
- ❌ **NEVER use `any` type** - Use proper TypeScript types
- ❌ **NEVER use non-null assertions (`!`)** - Use type guards instead
- ✅ **Use nullish coalescing (`??`)** instead of logical OR (`||`) for null/undefined
- ✅ **Import types from Prisma**: `import { PolicyStatus, Role } from "@prisma/client";`

#### **Import/Export Rules**
- ❌ **NEVER leave unused imports** - Remove all unused variables/imports
- ❌ **NEVER import entire modules** when only specific functions needed
- ✅ **Use named imports**: `import { specificFunction } from "./module";`
- ✅ **Group imports**: External packages → Internal modules → Relative imports

#### **Code Quality Patterns**
```typescript
// ❌ WRONG - Console statements
console.log("User logged in");
console.error("Error:", error);

// ✅ CORRECT - Structured logging
logger.business("User authentication successful", { userId });
logger.error("Authentication failed", error, { email });

// ❌ WRONG - Logical OR with nullish values
const value = data.field || "default";
const config = process.env.VAR || "fallback";

// ✅ CORRECT - Nullish coalescing
const value = data.field ?? "default";
const config = process.env.VAR ?? "fallback";

// ❌ WRONG - Non-null assertion
const user = getUser()!;
params!.id

// ✅ CORRECT - Type guards
const user = getUser();
if (!user) throw new Error("User not found");
if (!params?.id) throw new Error("ID required");

// ❌ WRONG - Any type
function process(data: any): any {
  return data.something;
}

// ✅ CORRECT - Proper typing
function process(data: UserData): ProcessedData {
  return data.processedField;
}
```

## Winner Bid Selection System

### Automatic Winner Selection
The platform implements a sophisticated automated winner selection system for auction closure:

#### Winner Selection Algorithm
- **Location**: `src/lib/auction/winner-selection.service.ts`
- **Criteria**: 70% price weight, 30% coverage quality weight
- **Winners**: Top 3 bidders selected automatically
- **Processing**: Fully automated when auctions close using Supabase cron jobs

#### Manual Override (Account Holder)
Account holders can override automatic selection through:
- **Component**: Winner selection modal in auction management interface
- **Features**: Bid comparison, manual winner selection, confirmation workflow
- **UI**: Professional interface with medal emojis (🥇🥈🥉) for position tracking

#### Winner Management Workflow
1. **Automatic Selection**: Supabase cron jobs select top 3 winners every 5 minutes on auction closure
2. **Database Storage**: Winners stored in `AuctionWinner` table with position ranking
3. **State Tracking**: Complete auction state history in `AuctionState` table
4. **Contact Revelation**: Automatic contact information sharing between winners and account holders
5. **Commission Processing**: 10% commission tracking in `AuctionCommission` table
6. **Email Notifications**: Template-based notifications to all parties via Brevo service

#### Winner Display System
- **Closed Auctions**: Professional card layout with medal emojis and position tracking
- **Contact Information**: Revealed automatically upon winner selection
- **Commission System**: 10% of policy premium tracked for broker payments
- **State History**: Complete audit trail of auction progression

This platform represents a gold standard implementation of Screaming Architecture with comprehensive business domain visibility and security patterns.