# Claude Code Commands

This directory contains automation scripts and commands for maintaining code quality and architecture compliance.

## /generate-changelog

**Purpose**: Generate or update daily changelog with session improvements

**Usage**:
```bash
/generate-changelog "Brief session description"
```

**Examples**:
```bash
/generate-changelog "API refactoring and service improvements"
/generate-changelog "Email service modernization"
/generate-changelog "Schema architecture migration" 
/generate-changelog  # Uses default description
```

**Features**:
- ✅ **Auto-numbering**: If changelog exists for today, creates numbered version (changelog-2.md, changelog-3.md, etc.)
- ✅ **Multi-developer support**: Multiple developers can work same day without conflicts
- ✅ **Git integration**: Automatically detects modified and staged files
- ✅ **Template structure**: Provides consistent changelog format
- ✅ **Quality reminders**: Includes ESLint, TypeScript, and Prisma checks

**Workflow**:
1. **Before ending session**: Run `/generate-changelog "session description"`
2. **Fill template**: Edit generated file with specific changes
3. **Commit with changes**: Include changelog in your git commit

**Output Location**: `docs/changelog/YYYY-MM/YYYY-MM-DD-changelog[-N].md`

**Template Sections**:
- Session details and timing
- Modified files (auto-detected)
- Key improvements (fill manually)
- Technical changes (fill manually)  
- Quality assurance checklist
- Impact summary
- Next steps

**Best Practices**:
- Use descriptive session descriptions
- Run BEFORE compacting context
- Fill in template sections with actual changes
- Commit changelog with related changes
- Use for any significant development session

**Example Output**:
```
📝 Creating new changelog: 2025-09-06-changelog.md
✅ Creating changelog: docs/changelog/2025-09/2025-09-06-changelog.md

📝 Next Steps:
1. Edit the generated changelog to add specific details  
2. Fill in the template sections with actual changes
3. Commit the changelog with your other changes
```

## Architecture Scanner

### `npm run arch-scan`

Comprehensive automated scanner for architectural violations inspired by the systematic analysis performed in September 2025.

#### Features

- **Screaming Architecture Compliance**: Validates business domain organization
- **Console Statement Detection**: Finds improper logging usage  
- **Hardcoded Enum Detection**: Identifies string literals that should use Prisma enums
- **Type Safety Analysis**: Detects `any` types and unsafe assertions
- **Schema Integrity**: Validates proper Prisma schema usage
- **Markdown Reports**: Human-readable reports generated in `docs/reports/`

#### Usage

```bash
# Basic scan (generates markdown report in docs/reports/)
npm run arch-scan

# Verbose output for debugging
npm run arch-scan:verbose

# Direct script usage
./.claude/commands/architecture-scan.sh

# Custom output file
./.claude/commands/architecture-scan.sh --output-file custom-report.md

# Verbose output
./.claude/commands/architecture-scan.sh --verbose
```

#### Integration Examples

**1. Manual Quality Check**
```bash
# Before major releases
npm run arch-scan
```

**2. CI/CD Pipeline Integration**
```yaml
# GitHub Actions example
- name: Architecture Scan
  run: |
    npm run arch-scan
    # Check if critical violations exist in the report
    if grep -q "🚨 CRITICAL" docs/reports/ARCHITECTURE_VIOLATIONS_REPORT.md; then
      echo "❌ Architecture violations detected"
      cat docs/reports/ARCHITECTURE_VIOLATIONS_REPORT.md
      exit 1
    fi
```

**3. Pre-commit Hook**
```bash
#!/bin/sh
# .git/hooks/pre-commit
npm run arch-scan
if grep -q "🚨.*[1-9][0-9]\+ files" docs/reports/ARCHITECTURE_VIOLATIONS_REPORT.md; then
  echo "❌ Too many architecture violations found"
  echo "Please fix critical issues before committing"
  exit 1
fi
```

**4. Monthly Architecture Review**
```bash
# Cron job for regular monitoring
0 9 1 * * cd /path/to/zeeguros && npm run arch-scan
```

#### Report Output

The scanner generates a comprehensive markdown report at `docs/reports/ARCHITECTURE_VIOLATIONS_REPORT.md` containing:

- **Executive Summary**: Compliance scores and violation counts
- **Critical Findings**: Detailed file-by-file violations
- **Fix Templates**: Before/after code examples
- **Recommended Actions**: Prioritized improvement tasks
- **Scan Metadata**: Timestamps and scan configuration

#### Current Compliance Status

- **🏢 Screaming Architecture**: ✅ 100% compliant (0 violations)
- **🔇 Console Statements**: ✅ 100% compliant (0 violations)
- **🚨 Hardcoded Enums**: ⚠️ 26 critical violations need fixing
- **🔧 Type Safety**: ✅ 4 minor issues (acceptable)
- **📄 Manual Types**: ⚠️ 9 moderate violations need review
- **🏗️ Schema Integrity**: ⚠️ 294 potential fake schemas need investigation

## Recommended Workflow

1. **Weekly Development**: Run `npm run arch-scan` to catch new violations early
2. **Pre-release**: Full scan with manual review of all violations  
3. **Post-refactor**: Verify architectural improvements
4. **Session Completion**: Generate changelog with `/generate-changelog "description"`
5. **Monthly Review**: Track compliance trends over time
6. **CI Integration**: Automated quality gates for pull requests

## File Structure

```
.claude/
├── commands.md              # This documentation
├── commands/
│   ├── architecture-scan.sh # Architecture violations scanner
│   └── generate-changelog.sh # Changelog generator
└── CLAUDE.md               # Development guidelines
```

## Quality Assurance Integration

Both tools integrate seamlessly with the project's quality assurance workflow:

- **Architecture Scanner**: Enforces Screaming Architecture principles and code quality standards
- **Changelog Generator**: Maintains comprehensive development history
- **CI/CD Ready**: Both tools support automated pipeline integration
- **Developer Friendly**: Simple npm scripts for easy execution

These tools help maintain the high architectural standards that make this codebase a gold standard implementation of Screaming Architecture with 100% business domain visibility.