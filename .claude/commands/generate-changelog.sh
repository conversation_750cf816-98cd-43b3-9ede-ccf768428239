#!/bin/bash

# Claude Code Command: Generate or Update Changelog
# Usage: /generate-changelog [session-description]
# Example: /generate-changelog "API refactoring and service improvements"

set -e

# Configuration
CHANGELOG_DIR="docs/changelog"
DATE=$(date +%Y-%m-%d)
YEAR_MONTH=$(date +%Y-%m)
CHANGELOG_DIR_FULL="$CHANGELOG_DIR/$YEAR_MONTH"
CHANGELOG_FILE="$CHANGELOG_DIR_FULL/$DATE-changelog.md"

# Get next sequential changelog number across ALL changelog files (global sequence)
get_next_sequential_number() {
    # Find the highest number across all changelog files in the entire docs/changelog directory
    local highest_number=$(find "$CHANGELOG_DIR" -name "*changelog-*.md" 2>/dev/null | \
                          grep -o 'changelog-[0-9]\+\.md' | \
                          grep -o '[0-9]\+' | \
                          sort -n | \
                          tail -1)
    
    # If no numbered files exist, start with 1
    if [ -z "$highest_number" ]; then
        echo 1
    else
        echo $((highest_number + 1))
    fi
}

# Get session description from arguments or prompt
SESSION_DESCRIPTION="${1:-"Development session improvements"}"

# Create directory if it doesn't exist
mkdir -p "$CHANGELOG_DIR_FULL"

# Determine file name and operation - always use sequential numbering
CHANGELOG_NUMBER=$(get_next_sequential_number)
CHANGELOG_FILE="$CHANGELOG_DIR_FULL/$DATE-changelog-$CHANGELOG_NUMBER.md"
OPERATION="Creating"
echo "📝 Creating new changelog #$CHANGELOG_NUMBER: $(basename "$CHANGELOG_FILE")"

# Get current git status for change detection
GIT_CHANGES=$(git status --porcelain 2>/dev/null | wc -l | tr -d ' ')
MODIFIED_FILES=$(git diff --name-only HEAD 2>/dev/null | head -10 | tr '\n' ', ' | sed 's/,$//')
STAGED_FILES=$(git diff --cached --name-only 2>/dev/null | head -10 | tr '\n' ', ' | sed 's/,$//')

# Generate changelog content based on established pattern
cat > "$CHANGELOG_FILE" << EOF
# Changelog #$CHANGELOG_NUMBER - $(date +"%B %d, %Y")

## $SESSION_DESCRIPTION

### Overview
Development session implementing improvements and maintaining code quality standards.

---

## 📋 **Session Details**
**Time: $(date +"%H:%M %Z")**  
**Scope: Development Session**

### 🎯 **Goal**
Document changes made during development session to maintain project history and architectural consistency.

### 📝 **Changes Made:**

#### **Modified Files:**
EOF

# Add file changes if available with specific file listings (following established pattern)
if [ ! -z "$MODIFIED_FILES" ]; then
    # Convert comma-separated list to bullet points
    echo "$MODIFIED_FILES" | tr ',' '\n' | sed 's/^ *//' | while read -r file; do
        [ ! -z "$file" ] && echo "- \`$file\` - [Add description of changes]" >> "$CHANGELOG_FILE"
    done
fi

if [ ! -z "$STAGED_FILES" ]; then
    echo "" >> "$CHANGELOG_FILE"
    echo "#### **Staged Files:**" >> "$CHANGELOG_FILE"
    echo "$STAGED_FILES" | tr ',' '\n' | sed 's/^ *//' | while read -r file; do
        [ ! -z "$file" ] && echo "- \`$file\` - [Add description of changes]" >> "$CHANGELOG_FILE"
    done
fi

if [ "$GIT_CHANGES" -gt 0 ]; then
    echo "" >> "$CHANGELOG_FILE"
    echo "- 🔧 **Total Changes**: $GIT_CHANGES file(s) modified" >> "$CHANGELOG_FILE"
fi

# Add template sections following established pattern
cat >> "$CHANGELOG_FILE" << 'EOF'

#### **Key Improvements:**
- ✅ **[CATEGORY]**: [Describe critical fixes or features]
- ✅ **[ARCHITECTURE]**: [Document architectural improvements]
- ✅ **[SECURITY]**: [Note security enhancements]
- ✅ **[QUALITY]**: [Describe quality improvements]
- ✅ **[MONITORING]**: [Document monitoring/tooling enhancements]

#### **Code Quality:**
- ✅ **ESLint**: All changes follow code quality standards
- ✅ **TypeScript**: Zero compilation errors across TypeScript files
- ✅ **Architecture Scan**: [Add compliance score and violations]

### 🛡️ **Quality Assurance:**
- ✅ **Code Review**: Changes follow established patterns
- ✅ **Testing**: Functionality verified
- ✅ **Documentation**: Updated as needed
- ✅ **Architectural Compliance**: DRY, KISS, Factory patterns maintained

---

## 📊 **Session Impact**

### 🎯 **Achievements:**
- **[Major Accomplishment]**: [Describe significant achievement]
- **[Performance]**: [Note performance improvements]
- **[Bug Fixes]**: [Document critical fixes]
- **[Compliance]**: [Architecture/security compliance achievements]

### 🔧 **Technical Benefits:**
- **[Maintainability]**: [Describe code maintainability improvements]
- **[Type Safety]**: [Note TypeScript/type safety enhancements]
- **[Consistency]**: [List consistency improvements across codebase]
- **[Performance]**: [Document performance optimizations]

### 🛡️ **Future Protection:**
- **[Anti-Pattern Prevention]**: [Document safeguards against bad patterns]
- **[Architectural Safeguards]**: [Note structural protections]
- **[Quality Improvements]**: [Describe quality assurance enhancements]

---

## 📋 **Next Steps**

### **Immediate Actions:**
- ✅ **[Action]**: [Completed immediate tasks or mark as pending]
- ✅ **[Verification]**: [Testing/validation completed]
- ✅ **[Documentation]**: [Documentation updates finished]

### **Future Enhancements:**
- 🔄 **[Enhancement]**: [Document planned architectural improvements]
- 🔄 **[Performance]**: [Note planned performance optimizations]
- 🔄 **[Features]**: [Describe upcoming feature development]

---

## 🎯 **Session Summary**

[Provide comprehensive summary of session work and impact, including technical details, architectural improvements, and business value delivered]

**Status: ✅ SESSION DOCUMENTED & CHANGES RECORDED**
EOF

echo "✅ $OPERATION changelog: $CHANGELOG_FILE"
echo ""
echo "📝 **Next Steps:**"
echo "1. Edit the generated changelog to add specific details"
echo "2. Fill in the template sections with actual changes"
echo "3. Commit the changelog with your other changes"
echo ""
echo "🚀 **Quick Commands:**"
echo "   code \"$CHANGELOG_FILE\"  # Open in VS Code"
echo "   git add \"$CHANGELOG_FILE\"  # Stage changelog"
echo ""
echo "💡 **Tips:**"
echo "- Use this command BEFORE compacting context"
echo "- Multiple developers can run this on same day (auto-numbered)"
echo "- Always fill in the template sections with real changes"