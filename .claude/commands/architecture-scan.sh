#!/bin/bash

# =============================================================================
# ARCHITECTURE VIOLATIONS SCANNER
# =============================================================================
# Automated comprehensive scan for architectural violations in the zeeguros codebase
# Generates detailed report with actionable findings
#
# Usage: ./architecture-scan.sh [--output-file report.md] [--verbose]
# 
# Created: September 2025
# Maintainer: Development Team
# =============================================================================

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
SRC_DIR="$PROJECT_ROOT/src"
REPORT_DIR="$PROJECT_ROOT/docs/reports/architecture-violations"
TIMESTAMP=$(date '+%d-%m-%Y-%H_%M_%S')
DEFAULT_OUTPUT="$REPORT_DIR/${TIMESTAMP}_architecture_violations_report.md"
TEMP_DIR="/tmp/arch-scan-$$"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Parse command line arguments
OUTPUT_FILE="$DEFAULT_OUTPUT"
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --output-file)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        --verbose|-v)
            VERBOSE=true
            shift
            ;;
        --help|-h)
            echo "Architecture Violations Scanner"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --output-file FILE    Output file path (default: $DEFAULT_OUTPUT)"
            echo "  Architecture scanner generates markdown reports in docs/reports/"
            echo "  --verbose, -v         Verbose output"
            echo "  --help, -h            Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

# Create temp directory
mkdir -p "$TEMP_DIR"
trap "rm -rf $TEMP_DIR" EXIT

# Logging function
log() {
    if [[ "$VERBOSE" == true ]]; then
        echo -e "${BLUE}[INFO]${NC} $1"
    fi
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# =============================================================================
# SCAN FUNCTIONS
# =============================================================================

# Count total TypeScript files
count_typescript_files() {
    find "$SRC_DIR" -name "*.ts" -o -name "*.tsx" 2>/dev/null | wc -l
}

# Scan for Screaming Architecture violations
scan_screaming_architecture() {
    log "Scanning Screaming Architecture compliance..."
    
    local violations=0
    local features_dir="$SRC_DIR/features"
    
    # Check if features directory exists
    if [[ ! -d "$features_dir" ]]; then
        echo "CRITICAL: No features directory found"
        return 1
    fi
    
    # Check for role-based directories in features (violation)
    local role_based_dirs=$(find "$features_dir" -maxdepth 1 -type d -name "*account-holder*" -o -name "*broker*" -o -name "*admin*" 2>/dev/null)
    if [[ -n "$role_based_dirs" ]]; then
        violations=$((violations + $(echo "$role_based_dirs" | wc -l)))
        echo "$role_based_dirs" > "$TEMP_DIR/screaming_violations.txt"
    fi
    
    # Check business domain organization
    local business_domains=$(find "$features_dir" -maxdepth 1 -type d ! -path "$features_dir" | wc -l)
    
    echo "$violations" > "$TEMP_DIR/screaming_count.txt"
    echo "$business_domains" > "$TEMP_DIR/business_domains_count.txt"
}

# Scan for console statement violations
scan_console_statements() {
    log "Scanning for console statement violations..."
    
    find "$SRC_DIR" -name "*.ts" -o -name "*.tsx" | \
        xargs grep -l "console\." 2>/dev/null | \
        grep -v "logger\.ts" > "$TEMP_DIR/console_violations.txt" || true
    
    local count=$(wc -l < "$TEMP_DIR/console_violations.txt" 2>/dev/null || echo "0")
    echo "$count" > "$TEMP_DIR/console_count.txt"
}

# Scan for hardcoded enum violations
scan_hardcoded_enums() {
    log "Scanning for hardcoded enum violations..."
    
    find "$SRC_DIR" -name "*.ts" -o -name "*.tsx" | \
        grep -v "/lib/zod/" | \
        xargs grep -l '"ACTIVE"\|"DRAFT"\|"BROKER"\|"ADMIN"\|"ACCOUNT_HOLDER"\|"EXPIRED"\|"CLOSED"\|"OPEN"\|"RENEW_SOON"\|"REJECTED"' 2>/dev/null \
        > "$TEMP_DIR/enum_violations.txt" || true
    
    local count=$(wc -l < "$TEMP_DIR/enum_violations.txt" 2>/dev/null || echo "0")
    echo "$count" > "$TEMP_DIR/enum_count.txt"
    
    # Get detailed enum violations per file
    if [[ $count -gt 0 ]]; then
        while IFS= read -r file; do
            # Skip auto-generated zod files
            if [[ "$file" =~ /lib/zod/ ]]; then
                continue
            fi
            echo "=== $file ===" >> "$TEMP_DIR/enum_details.txt"
            grep -n '"ACTIVE"\|"DRAFT"\|"BROKER"\|"ADMIN"\|"ACCOUNT_HOLDER"\|"EXPIRED"\|"CLOSED"\|"OPEN"\|"RENEW_SOON"\|"REJECTED"' "$file" 2>/dev/null | head -5 >> "$TEMP_DIR/enum_details.txt" || true
            echo "" >> "$TEMP_DIR/enum_details.txt"
        done < "$TEMP_DIR/enum_violations.txt"
    fi
}

# Scan for type safety violations
scan_type_safety() {
    log "Scanning for type safety violations..."
    
    # Create temporary file for all matches
    > "$TEMP_DIR/type_safety_all_matches.txt"
    
    # Find files with potential type safety issues, but filter out false positives
    find "$SRC_DIR" -name "*.ts" -o -name "*.tsx" | while read -r file; do
        # Look for actual problematic any usage, filtering out false positives
        grep -n ": any\|as any" "$file" 2>/dev/null | \
            grep -v "\/\*\|\/\/\|\*" | \
            while read -r match; do
                # Extract line number and check if previous line has eslint-disable
                line_num=$(echo "$match" | cut -d: -f1)
                prev_line_num=$((line_num - 1))
                
                # Check if previous line contains eslint-disable for any
                if [[ $prev_line_num -gt 0 ]]; then
                    prev_line=$(sed -n "${prev_line_num}p" "$file" 2>/dev/null)
                    if [[ "$prev_line" =~ eslint-disable.*any ]]; then
                        continue  # Skip this match - it's intentionally disabled
                    fi
                fi
                
                # This is a real violation
                echo "$file" >> "$TEMP_DIR/type_safety_all_matches.txt"
                break  # Only need to record the file once
            done || true
    done
    
    # Remove duplicates and create final violations file
    sort "$TEMP_DIR/type_safety_all_matches.txt" 2>/dev/null | uniq > "$TEMP_DIR/type_safety_violations.txt" || > "$TEMP_DIR/type_safety_violations.txt"
    
    local count=$(wc -l < "$TEMP_DIR/type_safety_violations.txt" 2>/dev/null || echo "0")
    echo "$count" > "$TEMP_DIR/type_safety_count.txt"
    
    # Get detailed type safety violations per file (only real violations)
    if [[ $count -gt 0 ]]; then
        while IFS= read -r file; do
            echo "=== $file ===" >> "$TEMP_DIR/type_safety_details.txt"
            # Show actual problematic lines (filtering out eslint-disabled ones)
            grep -n ": any\|as any" "$file" 2>/dev/null | \
                grep -v "\/\*\|\/\/\|\*" | \
                while read -r match; do
                    line_num=$(echo "$match" | cut -d: -f1)
                    prev_line_num=$((line_num - 1))
                    
                    # Check if previous line has eslint-disable
                    if [[ $prev_line_num -gt 0 ]]; then
                        prev_line=$(sed -n "${prev_line_num}p" "$file" 2>/dev/null)
                        if [[ "$prev_line" =~ eslint-disable.*any ]]; then
                            continue  # Skip this match
                        fi
                    fi
                    
                    # Show the violation
                    echo "$match" >> "$TEMP_DIR/type_safety_details.txt"
                done || true
            echo "" >> "$TEMP_DIR/type_safety_details.txt"
        done < "$TEMP_DIR/type_safety_violations.txt"
    fi
}

# Scan for manual type definitions
scan_manual_types() {
    log "Scanning for manual type definitions..."
    
    # Look for actual manual type definitions that should use Prisma enums
    # Focus on role/status types that duplicate Prisma enums
    find "$SRC_DIR" -name "*.ts" -o -name "*.tsx" | \
        grep -v "/lib/zod/" | \
        xargs grep -l "^[[:space:]]*type.*Role.*=.*\"BROKER\"\|\"ADMIN\"\|\"ACCOUNT_HOLDER\"\|^[[:space:]]*type.*Status.*=.*\"ACTIVE\"\|\"DRAFT\"\|\"EXPIRED\"\|\"RENEW_SOON\"" 2>/dev/null \
        > "$TEMP_DIR/manual_types_violations.txt" || true
    
    local count=$(wc -l < "$TEMP_DIR/manual_types_violations.txt" 2>/dev/null || echo "0")
    echo "$count" > "$TEMP_DIR/manual_types_count.txt"
    
    # Get detailed manual types violations per file
    if [[ $count -gt 0 ]]; then
        while IFS= read -r file; do
            echo "=== $file ===" >> "$TEMP_DIR/manual_types_details.txt"
            grep -n "^[[:space:]]*type.*Role.*=.*\"BROKER\"\|\"ADMIN\"\|\"ACCOUNT_HOLDER\"\|^[[:space:]]*type.*Status.*=.*\"ACTIVE\"\|\"DRAFT\"\|\"EXPIRED\"\|\"RENEW_SOON\"" "$file" 2>/dev/null | head -3 >> "$TEMP_DIR/manual_types_details.txt" || true
            echo "" >> "$TEMP_DIR/manual_types_details.txt"
        done < "$TEMP_DIR/manual_types_violations.txt"
    fi
}

# Scan for schema integrity
scan_schema_integrity() {
    log "Scanning schema integrity..."
    
    # Look for schemas that manually define Prisma enum values instead of using Prisma enums
    # Focus on schemas with hardcoded role/status values that should use Prisma enums
    find "$SRC_DIR" -name "*.ts" -o -name "*.tsx" | \
        grep -v "/lib/zod/" | \
        xargs grep -l "z\.object\|z\.enum\|Schema.*=.*z\." 2>/dev/null | \
        xargs grep -l "\"BROKER\"\|\"ADMIN\"\|\"ACCOUNT_HOLDER\"\|\"ACTIVE\"\|\"DRAFT\"\|\"EXPIRED\"" 2>/dev/null | \
        xargs grep -L "z\.nativeEnum\|@prisma/client" 2>/dev/null \
        > "$TEMP_DIR/potential_fake_schemas.txt" || true
    
    local count=$(wc -l < "$TEMP_DIR/potential_fake_schemas.txt" 2>/dev/null || echo "0")
    echo "$count" > "$TEMP_DIR/schema_violations_count.txt"
    
    # Get detailed schema violations per file
    if [[ $count -gt 0 ]]; then
        while IFS= read -r file; do
            echo "=== $file ===" >> "$TEMP_DIR/schema_violations_details.txt"
            grep -n "z\.object\|z\.enum\|Schema.*=.*z\." "$file" 2>/dev/null | head -3 >> "$TEMP_DIR/schema_violations_details.txt" || true
            echo "" >> "$TEMP_DIR/schema_violations_details.txt"
        done < "$TEMP_DIR/potential_fake_schemas.txt"
    fi
}

# Scan for API authentication violations
scan_api_auth_violations() {
    log "Scanning API authentication violations..."
    
    # Look for API routes that use user.role instead of role parameter
    # Exclude server-auth.ts which legitimately defines AuthenticatedUser interface
    find "$SRC_DIR/app/api" -name "route.ts" | \
        xargs grep -l "user\.role" 2>/dev/null | \
        grep -v "server-auth" \
        > "$TEMP_DIR/api_auth_violations.txt" || true
    
    local count=$(wc -l < "$TEMP_DIR/api_auth_violations.txt" 2>/dev/null || echo "0")
    echo "$count" > "$TEMP_DIR/api_auth_count.txt"
    
    # Get detailed API auth violations per file
    if [[ $count -gt 0 ]]; then
        while IFS= read -r file; do
            echo "=== $file ===" >> "$TEMP_DIR/api_auth_details.txt"
            grep -n "user\.role" "$file" 2>/dev/null | head -3 >> "$TEMP_DIR/api_auth_details.txt" || true
            echo "" >> "$TEMP_DIR/api_auth_details.txt"
        done < "$TEMP_DIR/api_auth_violations.txt"
    fi
}

# =============================================================================
# REPORT GENERATION
# =============================================================================

generate_markdown_report() {
    log "Generating markdown report..."
    
    local total_files=$(cat "$TEMP_DIR/total_files.txt")
    local console_violations=$(cat "$TEMP_DIR/console_count.txt")
    local enum_violations=$(cat "$TEMP_DIR/enum_count.txt")
    local type_violations=$(cat "$TEMP_DIR/type_safety_count.txt")
    local manual_types=$(cat "$TEMP_DIR/manual_types_count.txt")
    local schema_violations=$(cat "$TEMP_DIR/schema_violations_count.txt")
    local screaming_violations=$(cat "$TEMP_DIR/screaming_count.txt")
    local api_auth_violations=$(cat "$TEMP_DIR/api_auth_count.txt")
    
    # Calculate compliance score
    local total_violations=$((console_violations + enum_violations + type_violations + manual_types + schema_violations + screaming_violations + api_auth_violations))
    local compliance_score=0
    if [[ $total_violations -eq 0 ]]; then
        compliance_score=100
    else
        compliance_score=$(( (total_files - total_violations) * 100 / total_files ))
    fi
    
    cat > "$OUTPUT_FILE" << EOF
# 🚨 ARCHITECTURE VIOLATIONS REPORT

**Generated**: $(date '+%B %d, %Y at %H:%M')
**Scan Method**: Automated systematic analysis
**Files Analyzed**: $total_files TypeScript files
**Overall Compliance Score**: ${compliance_score}%

---

## 📊 EXECUTIVE SUMMARY

| Category | Files Affected | Severity | Status |
|----------|----------------|----------|---------|
| **🏢 Screaming Architecture** | $screaming_violations | $([ $screaming_violations -eq 0 ] && echo "✅ NONE" || echo "🚨 CRITICAL") | $([ $screaming_violations -eq 0 ] && echo "EXCELLENT" || echo "NEEDS ATTENTION") |
| **🔇 Console Statements** | $console_violations | $([ $console_violations -eq 0 ] && echo "✅ NONE" || echo "🚨 CRITICAL") | $([ $console_violations -eq 0 ] && echo "PERFECT" || echo "NEEDS FIX") |
| **🚨 Hardcoded Enums** | $enum_violations | $([ $enum_violations -eq 0 ] && echo "✅ NONE" || echo "🚨 CRITICAL") | $([ $enum_violations -eq 0 ] && echo "EXCELLENT" || echo "URGENT") |
| **🔐 API Auth Violations** | $api_auth_violations | $([ $api_auth_violations -eq 0 ] && echo "✅ NONE" || echo "🚨 CRITICAL") | $([ $api_auth_violations -eq 0 ] && echo "PERFECT" || echo "URGENT") |
| **🔧 Type Safety** | $type_violations | $([ $type_violations -le 5 ] && echo "⚠️ MINOR" || echo "🚨 MAJOR") | $([ $type_violations -le 5 ] && echo "ACCEPTABLE" || echo "NEEDS WORK") |
| **📄 Manual Types** | $manual_types | $([ $manual_types -le 3 ] && echo "⚠️ MINOR" || echo "🟡 MODERATE") | $([ $manual_types -le 3 ] && echo "ACCEPTABLE" || echo "REVIEW NEEDED") |
| **🏗️ Schema Integrity** | $schema_violations | $([ $schema_violations -eq 0 ] && echo "✅ NONE" || echo "🚨 CRITICAL") | $([ $schema_violations -eq 0 ] && echo "EXCELLENT" || echo "CRITICAL") |

**Total Violations**: $total_violations files across all categories

---

## 🎯 CRITICAL FINDINGS

EOF

    # Add console violations section
    if [[ $console_violations -gt 0 ]]; then
        cat >> "$OUTPUT_FILE" << EOF
### 🚨 Console Statement Violations ($console_violations files)

**IMMEDIATE ACTION REQUIRED**: Replace all console statements with structured logging.

**Files requiring fixes:**
EOF
        while IFS= read -r file; do
            echo "- \`$file\`" >> "$OUTPUT_FILE"
        done < "$TEMP_DIR/console_violations.txt"
        
        cat >> "$OUTPUT_FILE" << EOF

**Fix Template:**
\`\`\`typescript
// ❌ WRONG:
console.log("message");
console.error("error");

// ✅ CORRECT:
import { logger } from "@/lib/logger";
logger.info("message");
logger.error("error message", error, { context });
\`\`\`

EOF
    fi

    # Add enum violations section
    if [[ $enum_violations -gt 0 ]]; then
        cat >> "$OUTPUT_FILE" << EOF
### 🚨 Hardcoded Enum Violations ($enum_violations files)

**CRITICAL TYPE SAFETY ISSUE**: Replace hardcoded strings with Prisma enums.

**Files requiring fixes:**
EOF
        while IFS= read -r file; do
            echo "- \`$file\`" >> "$OUTPUT_FILE"
        done < "$TEMP_DIR/enum_violations.txt"
        
        cat >> "$OUTPUT_FILE" << EOF

**Fix Template:**
\`\`\`typescript
// ❌ WRONG:
if (user.role === "BROKER") { }
status === "ACTIVE"
userRole="ADMIN"

// ✅ CORRECT:
import { Role, PolicyStatus, AuctionState } from "@prisma/client";
if (user.role === Role.BROKER) { }
status === PolicyStatus.ACTIVE
userRole={Role.ADMIN}
\`\`\`

**Examples of violations found:**
\`\`\`
$(head -20 "$TEMP_DIR/enum_details.txt" 2>/dev/null || echo "No detailed examples available")
\`\`\`

EOF
    fi

    # Add API auth violations section
    if [[ $api_auth_violations -gt 0 ]]; then
        cat >> "$OUTPUT_FILE" << EOF
### 🚨 API Authentication Violations ($api_auth_violations files)

**CRITICAL AUTH ISSUE**: API routes must use \`role\` parameter, not \`user.role\`.

**Files requiring fixes:**
EOF
        while IFS= read -r file; do
            echo "- \`$file\`" >> "$OUTPUT_FILE"
        done < "$TEMP_DIR/api_auth_violations.txt"
        
        cat >> "$OUTPUT_FILE" << EOF

**Fix Template:**
\`\`\`typescript
// ❌ WRONG:
.handler(async ({ user, params }) => {
  if (user.role === Role.BROKER) { }
  logger.info("Action", { userRole: user.role });
})

// ✅ CORRECT:
.handler(async ({ user, role, params }) => {
  if (role === Role.BROKER) { }
  logger.info("Action", { userRole: role });
})
\`\`\`

**Examples of violations found:**
\`\`\`
$(head -15 "$TEMP_DIR/api_auth_details.txt" 2>/dev/null || echo "No detailed examples available")
\`\`\`

EOF
    fi

    # Add type safety violations section
    if [[ $type_violations -gt 0 ]]; then
        cat >> "$OUTPUT_FILE" << EOF
### ⚠️ Type Safety Violations ($type_violations files)

**TYPE SAFETY ISSUE**: Replace \`any\` types with proper TypeScript types.

**Files requiring fixes:**
EOF
        while IFS= read -r file; do
            echo "- \`$file\`" >> "$OUTPUT_FILE"
        done < "$TEMP_DIR/type_safety_violations.txt"
        
        cat >> "$OUTPUT_FILE" << EOF

**Fix Template:**
\`\`\`typescript
// ❌ WRONG:
const data: any = response;
const result = data as any;

// ✅ CORRECT:
const data: ResponseType = response;
const result = data as ResponseType;
\`\`\`

**Examples of violations found:**
\`\`\`
$(head -15 "$TEMP_DIR/type_safety_details.txt" 2>/dev/null || echo "No detailed examples available")
\`\`\`

EOF
    fi

    # Add manual types violations section
    if [[ $manual_types -gt 0 ]]; then
        cat >> "$OUTPUT_FILE" << EOF
### 🟡 Manual Type Definitions ($manual_types files)

**MAINTENANCE ISSUE**: Replace manual type definitions with generated Prisma types.

**Files requiring fixes:**
EOF
        while IFS= read -r file; do
            echo "- \`$file\`" >> "$OUTPUT_FILE"
        done < "$TEMP_DIR/manual_types_violations.txt"
        
        cat >> "$OUTPUT_FILE" << EOF

**Fix Template:**
\`\`\`typescript
// ❌ WRONG:
type UserRole = "BROKER" | "ADMIN" | "ACCOUNT_HOLDER";

// ✅ CORRECT:
import { Role } from "@prisma/client";
// Use Role enum directly
\`\`\`

**Examples of violations found:**
\`\`\`
$(head -15 "$TEMP_DIR/manual_types_details.txt" 2>/dev/null || echo "No detailed examples available")
\`\`\`

EOF
    fi

    # Add schema violations section
    if [[ $schema_violations -gt 0 ]]; then
        cat >> "$OUTPUT_FILE" << EOF
### 🚨 Schema Integrity Violations ($schema_violations files)

**CRITICAL ISSUE**: Files containing schema definitions without Prisma integration.

**Files requiring review:**
EOF
        while IFS= read -r file; do
            echo "- \`$file\`" >> "$OUTPUT_FILE"
        done < "$TEMP_DIR/potential_fake_schemas.txt"
        
        cat >> "$OUTPUT_FILE" << EOF

**Review Template:**
\`\`\`typescript
// ❌ POTENTIALLY PROBLEMATIC:
const userSchema = z.object({
  role: z.string(), // Manual definition
});

// ✅ PREFERRED:
import { Role } from "@prisma/client";
const userSchema = z.object({
  role: z.nativeEnum(Role), // Uses Prisma enum
});
\`\`\`

**Examples of schema references found:**
\`\`\`
$(head -20 "$TEMP_DIR/schema_violations_details.txt" 2>/dev/null || echo "No detailed examples available")
\`\`\`

EOF
    fi

    # Add success sections for compliant areas
    if [[ $console_violations -eq 0 ]]; then
        cat >> "$OUTPUT_FILE" << EOF
### ✅ Console Statements - PERFECT COMPLIANCE

**Status**: Zero violations found across $total_files files
**Achievement**: Proper structured logging implementation

EOF
    fi

    if [[ $screaming_violations -eq 0 ]]; then
        cat >> "$OUTPUT_FILE" << EOF
### ✅ Screaming Architecture - EXCELLENT COMPLIANCE

**Status**: Perfect business domain organization
**Achievement**: Zero role-based directories in business logic
**Business Domains**: $(cat "$TEMP_DIR/business_domains_count.txt" 2>/dev/null || echo "N/A") domains properly organized

EOF
    fi

    # Add recommendations
    cat >> "$OUTPUT_FILE" << EOF
---

## 🛠️ RECOMMENDED ACTIONS

### Immediate Priority (Critical Issues)
EOF

    local priority=1
    
    if [[ $console_violations -gt 0 ]]; then
        echo "$priority. **Fix Console Violations**: Replace $console_violations files using logger service" >> "$OUTPUT_FILE"
        priority=$((priority + 1))
    fi
    
    if [[ $api_auth_violations -gt 0 ]]; then
        echo "$priority. **Fix API Auth Violations**: Fix $api_auth_violations API routes to use role parameter" >> "$OUTPUT_FILE"
        priority=$((priority + 1))
    fi
    
    if [[ $enum_violations -gt 0 ]]; then
        echo "$priority. **Fix Enum Violations**: Replace hardcoded strings in $enum_violations files with Prisma enums" >> "$OUTPUT_FILE"
        priority=$((priority + 1))
    fi
    
    if [[ $schema_violations -gt 0 ]]; then
        echo "$priority. **Review Schema Integrity**: Investigate $schema_violations potential fake schemas" >> "$OUTPUT_FILE"
        priority=$((priority + 1))
    fi

    cat >> "$OUTPUT_FILE" << EOF

### Automation Opportunities
- Set up ESLint rules for console statement detection
- Add pre-commit hooks for enum validation
- Implement TypeScript strict mode enforcement
- Schedule regular architecture scans (monthly recommended)

### Future Monitoring
- Run this scan before major releases
- Include in CI/CD pipeline for pull request validation
- Track compliance score improvements over time

---

## 📝 SCAN METADATA

**Script Version**: 1.0
**Scan Duration**: $(date '+%H:%M:%S')
**Command Used**: \`$(basename "$0") --output-file "$OUTPUT_FILE"\`
**Next Recommended Scan**: $(date -v+1m '+%B %d, %Y')

---

*Report generated by automated architecture scanner*
*For questions or improvements, contact the development team*
EOF

    success "Report generated: $OUTPUT_FILE"
}


# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    echo "🏗️  Architecture Violations Scanner"
    echo "========================================="
    echo ""
    
    # Validate project structure
    if [[ ! -d "$SRC_DIR" ]]; then
        error "Source directory not found: $SRC_DIR"
        exit 1
    fi
    
    # Ensure reports directory exists
    mkdir -p "$REPORT_DIR"
    
    # Count total files
    local total_files=$(count_typescript_files)
    echo "$total_files" > "$TEMP_DIR/total_files.txt"
    
    log "Found $total_files TypeScript files to analyze"
    
    # Run all scans
    echo "🔍 Running comprehensive architecture scan..."
    scan_screaming_architecture
    scan_console_statements
    scan_hardcoded_enums
    scan_api_auth_violations
    scan_type_safety
    scan_manual_types
    scan_schema_integrity
    
    # Generate report
    echo ""
    echo "📄 Generating report..."
    generate_markdown_report
    
    # Summary
    local console_violations=$(cat "$TEMP_DIR/console_count.txt")
    local enum_violations=$(cat "$TEMP_DIR/enum_count.txt")
    local api_auth_violations=$(cat "$TEMP_DIR/api_auth_count.txt")
    local critical_violations=$((console_violations + enum_violations + api_auth_violations))
    
    echo ""
    echo "========================================="
    if [[ $critical_violations -eq 0 ]]; then
        success "🎉 EXCELLENT! No critical violations found"
        success "Your architecture is in great shape!"
    else
        warn "⚠️  Found $critical_violations critical violations"
        if [[ $api_auth_violations -gt 0 ]]; then
            warn "Priority: Fix API auth ($api_auth_violations files), enums ($enum_violations files), and console statements ($console_violations files)"
        else
            warn "Priority: Fix hardcoded enums ($enum_violations files) and console statements ($console_violations files)"
        fi
    fi
    echo "📊 Full report: $OUTPUT_FILE"
    echo ""
}

# Run main function
main "$@"