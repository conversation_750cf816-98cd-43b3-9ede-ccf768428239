{"permissions": {"allow": ["<PERSON><PERSON>(claude doctor)", "Bash(npm run type-check:*)", "Bash(npm run lint)", "Bash(.claude/commands/architecture-scan.sh:*)", "Bash(.claude/commands/generate-changelog.sh:*)", "Bash(npm run dev:*)", "mcp__supabase__list_tables", "mcp__supabase__execute_sql", "Bash(npm run:*)", "Bash(tree:*)", "Bash(./.claude/commands/generate-changelog.sh:*)", "Bash(rm:*)", "Bash(npx eslint:*)", "Bash(./.claude/commands/architecture-scan.sh:*)", "Bash(ls:*)", "Bash(npx tsc:*)", "Bash(find:*)", "<PERSON><PERSON>(pkill:*)", "mcp__supabase__list_migrations", "mcp__supabase__get_advisors", "Bash(xargs kill:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}