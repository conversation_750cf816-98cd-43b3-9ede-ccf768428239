# Zeeguros - Reverse Auction Platform for Insurance

![Build Status](https://img.shields.io/badge/build-passing-brightgreen)
[![Version](https://img.shields.io/badge/version-0.1.0-blue)](./package.json)
[![Architecture](https://img.shields.io/badge/architecture-screaming%2095%25-green)](./src/features/README.md)
[![TypeScript](https://img.shields.io/badge/typescript-100%25-blue)](./tsconfig.json)

Zeeguros is a reverse auction platform for insurance policies, where users upload their current policy and insurance brokers compete to offer better coverage or price.

## Description

Zeeguros is a comprehensive web application designed to modernize the insurance industry. It provides a robust set of tools for users to manage their car insurance policies, for brokers to manage their client portfolios, and for the platform to leverage cutting-edge AI for data automation. The core of the platform is built on a modern technology stack, featuring Next.js for the frontend, Supabase for authentication and database management, and Google's Gemini AI for intelligent data extraction from policy documents.

## 🚀 Key Features

*   **🤖 AI-Powered Onboarding**: Intelligent policy data extraction using Google Gemini API
*   **📋 Data Validation**: Comprehensive validation of extracted policy information
*   **📄 Policy Management**: Complete policy lifecycle management with status tracking
*   **📁 Document Management**: Secure file upload, storage, and download using Cloudflare R2
*   **🏛️ Insurance Auctions**: Reverse auction system where brokers compete for policies
*   **🔐 Secure Authentication**: Role-based access control with Supabase Auth and comprehensive RLS policies
*   **📊 User Dashboards**: Tailored interfaces for account holders, brokers, and administrators
*   **📱 Mobile Responsive**: Optimized mobile experience with consistent UI/UX across all pages
*   **⚡ Scalable Backend**: Built on Next.js 15+ with TypeScript and Prisma ORM
*   **☁️ Cloud Storage**: Cloudflare R2 for cost-effective, globally distributed file storage

## 🏗️ Architecture Overview

Zeeguros implements **TRUE Screaming Architecture** where the codebase structure immediately reveals that this is an **Insurance Marketplace Platform** focused on **policies**, **auctions**, and **client management**.

### 🎯 Business Domain Organization

The architecture **SCREAMS** business intent, not technical implementation:

```
src/features/                    # 🎯 BUSINESS DOMAINS (Primary Organization)
├── 🎯 auctions/                # ✅ Comprehensive auction marketplace logic
├── 🏛️ policies/                # ✅ Complete insurance policy management
├── 🔐 auth/                    # ✅ Authentication & user profile management
└── 🆘 support/                 # ✅ Unified customer support functionality

src/app/                        # 🖥️ USER EXPERIENCES (Role-Based Routes)
├── account-holder/             # Policy owner dashboard & auction creation
├── broker/                     # Insurance broker dashboard & auction participation
├── admin/                      # Platform administration & oversight
└── api/                        # ✅ DOMAIN-BASED Server-side APIs
    ├── auctions/               # Complete auction operations
    ├── policies/               # Full policy management
    ├── documents/              # Secure document access
    └── profiles/               # User profile management
```

### 📁 Standard Feature Structure

Every business domain follows the **same consistent structure**:

```
feature-name/
├── actions/                    # Server actions & form handling
├── components/                 # React components specific to this domain
├── config/                     # Feature-specific configuration
├── hooks/                      # Custom React hooks for this domain
├── schemas/                    # Zod validation schemas
├── services/                   # Business logic & external API calls  
├── types/                      # TypeScript type definitions
└── utils/                      # Pure utility functions
```

For detailed information about the feature structure, see [`src/features/README.md`](src/features/README.md).

**Core Architecture Benefits:**
- **Immediately Understandable**: New developers instantly see "Insurance Marketplace"
- **Business-Focused**: Structure reveals WHAT the system does, not HOW it's built
- **Maintainable**: Changes to policy logic happen in one place
- **Scalable**: Easy to add new domains (claims, renewals, etc.)
- **Role-Aware**: Components handle multiple user roles internally
- **Uncle Bob Compliant**: True Screaming Architecture implementation

## Business Logic

### Auction Working Hours

Auctions follow sophisticated working hours business logic for fair timing:

- **Working Hours**: Monday-Friday, 06:00-23:59 Madrid timezone (18h/day)
- **Duration**: 48 working hours (~2.67 business days)
- **Weekend Handling**: Automatically adjusts start times to next Monday 06:00
- **Automation**: Supabase cron jobs close expired auctions every 5 minutes

For detailed implementation and examples, see the [Architecture Document](docs/architecture.md#working-hours-business-logic).

### 📚 **Comprehensive Documentation**

This project maintains extensive documentation to support development and architectural decisions:

- **[Main Architecture Document](docs/architecture.md)**: Complete architectural blueprint and technical specifications
- **[Features Structure Guide](src/features/README.md)**: Detailed guide to business domain organization
- **[Architecture Details](docs/architecture/)**: Detailed architectural documentation including:
  - Enhancement scope and tech stack alignment
  - Data models and component architecture
  - API design and external integrations
  - Infrastructure, security, and testing strategies
- **[Development Plans](docs/plans/)**: Current development roadmaps and PRDs
  - [Screaming Architecture Refactor PRD](docs/plans/drafts/draft-6-screaming-architecture-refactor-prd.md): Latest architectural improvements
- **[Change History](docs/changelog/)**: Comprehensive version history and architectural evolution
- **[User Stories](docs/stories/)**: BMAD-driven development stories and requirements

### Architectural Principles

Zeeguros follows **Uncle Bob's Screaming Architecture** and **Domain-Driven Design** principles:

#### 🎯 TRUE Screaming Architecture
- **Business Domain Focus**: Structure reveals insurance marketplace purpose
- **Role-Aware Components**: Single component serves multiple roles internally
- **No Technical Layers**: Organization by business domain, not MVC patterns
- **Scalable Design**: Easy addition of new business domains

#### 🏗️ Implementation Strategy
```typescript
// ✅ CORRECT - Role-aware component in business domain
// src/features/policies/components/PolicyCard.tsx
export function PolicyCard({ policy, userRole }: Props) {
  switch(userRole) {
    case Role.ACCOUNT_HOLDER: return <OwnerView policy={policy} />;
    case Role.BROKER: return <BrokerView policy={policy} />;
    case Role.ADMIN: return <AdminView policy={policy} />;
  }
}

// ❌ WRONG - Role-based directories (violates Screaming Architecture)
// src/features/account-holder/components/PolicyCard.tsx
// src/features/broker/components/PolicyCard.tsx  
// src/features/admin/components/PolicyCard.tsx
```

### Database Architecture

Zeeguros uses a sophisticated PostgreSQL database supporting the complete insurance marketplace workflow. For detailed schema specifications and entity relationships, see the [Architecture Document](docs/architecture.md).


### Document Management

Zeeguros includes a comprehensive document management system for policy documents:

- **Secure Upload**: Policy documents stored in Cloudflare R2 with server-side processing
- **AI Integration**: Google Gemini API extracts policy data automatically
- **Access Control**: Role-based document access with authentication required
- **File Support**: PDF, JPG, and PNG files with validation and metadata tracking

## Recent Updates

### September 2025 - State Management & Architectural Excellence
- **📅 Auxiliary State Tables**: Comprehensive AuctionState and PolicyState models for complete audit trails
- **🎯 Business Domain Architecture**: Mature implementation with 4 core domains (auctions, policies, auth, support)
- **🎨 UI/UX Enhancements**: Consistent button hover states and visual hierarchy improvements
- **🏆 Winner Selection System**: Sophisticated algorithm with 70% price, 30% coverage quality weighting
- **🔄 State Service Layer**: Comprehensive state management services for business domain transitions
- **🔍 Architecture Compliance**: Enhanced scanning tools (`npm run arch-scan`) for quality assurance

### August 2025 - Service Layer Consolidation  
- **PolicyUploadService**: Unified file upload logic with AI validation and Cloudflare R2 integration
- **Generic Brevo Service**: Template-based email system with factory pattern implementation
- **Schema Architecture**: Centralized validation schemas with Prisma as single source of truth
- **API Factory Pattern**: All routes use createApiRoute() for consistent authentication and validation
- **Quality Assurance**: Mandatory ESLint workflow for all file changes

For detailed change history, see the [changelog directory](docs/changelog/).

### 🏛️ System Architecture Diagram

This diagram illustrates our **TRUE Screaming Architecture** with business domain organization and comprehensive quality assurance.

```mermaid
graph TD
    subgraph Browser["User's Browser"]
        A[Next.js Client Components]
        B[Role-Based Routes]
    end

    subgraph Server["Zeeguros Server - Screaming Architecture"]
        C[createApiRoute Factory]
        D[Prisma Client - Single Source of Truth]
        E[Generic Services Layer]
        F[centralized schemas @/lib/schemas]
        G[Google Gemini API]
        H[Generic Brevo Service]
    end
    
    subgraph BusinessDomains["Business Domains - src/features/"]
        I[🏛️ Policies Domain]
        J[🎯 Auctions Domain]
        K[👤 Profiles Domain]
        L[👥 Clients Domain]
    end

    subgraph Infrastructure["Infrastructure - Supabase"]
        M[PostgreSQL Database]
        N[Supabase Auth]
        O[JWT Validation]
    end

    subgraph QualityAssurance["Quality Assurance System"]
        P[ESLint - Mandatory on File Changes]
        Q[TypeScript Strict Mode]
        R[Prisma Generate]
    end

    A -->|User Interaction| B
    B -->|API Request| C
    C -->|Validation| F
    C -->|DB Operations| D
    C -->|Generic Services| E
    E -->|AI Processing| G
    E -->|Template Emails| H
    D -->|SQL Query| M
    
    B -->|Import Components| I
    B -->|Import Components| J
    B -->|Import Components| K
    B -->|Import Components| L
    
    N -->|Validates JWT| O
    O -->|Auth Check| C
    
    M -.->|RLS Policies| D
    
    P -.->|Quality Check| C
    P -.->|Quality Check| E
    Q -.->|Type Safety| D
    R -.->|Schema Sync| D

    classDef clientStyle fill:#cce5ff,stroke:#333,stroke-width:2px
    classDef serverStyle fill:#d4f0f0,stroke:#333,stroke-width:2px
    classDef domainStyle fill:#f0f8f0,stroke:#2d5a2d,stroke-width:2px
    classDef infraStyle fill:#FDEDEC,stroke:#333,stroke-width:2px
    classDef authStyle fill:#e6e6fa,stroke:#333,stroke-width:2px
    classDef qaStyle fill:#e8f5e8,stroke:#2d5a2d,stroke-width:2px
    
    class A,B clientStyle
    class C,D,E,F,G,H serverStyle
    class I,J,K,L domainStyle
    class M infraStyle
    class N,O authStyle
    class P,Q,R qaStyle
```

### AI-Powered Policy Processing

Zeeguros uses Google Gemini API to automatically extract policy data from uploaded documents:

1. **Document Upload**: Users upload policy documents (PDF, JPG, PNG)
2. **AI Processing**: Google Gemini extracts structured data from documents
3. **Validation**: Multi-layer validation ensures data accuracy and security
4. **Form Population**: Extracted data populates policy creation forms automatically

This reduces manual data entry by ~90% and improves accuracy while maintaining security through server-side processing.



### 🛠️ Technology Stack

#### **Core Technologies**
*   **Next.js 15+**: React framework with App Router and TypeScript
*   **Supabase**: Authentication, PostgreSQL database, and Row-Level Security
*   **Prisma ORM**: Type-safe database operations
*   **Tailwind CSS + Radix UI**: Modern UI with shadcn/ui components
*   **Google Gemini API**: AI-powered document analysis
*   **Cloudflare R2**: Secure document storage

#### **Development Tools**
*   **ESLint**: Code linting with custom rules
*   **Prettier**: Code formatting
*   **TypeScript**: Static type checking
*   **Prisma Studio**: Database management interface

## Database Schema

The database schema is defined and managed using Prisma. Here are the core models:

*   **`User`**: Stores user authentication information and role (`ACCOUNT_HOLDER`, `BROKER`, or `ADMIN`).
*   **`AccountHolderProfile` / `BrokerProfile` / `AdminProfile`**: Store role-specific data, linked to a `User`.
*   **`Policy`**: Contains all details of an insurance policy, linking together the customer, broker, asset, and coverages.
*   **`Asset`**: Stores detailed information about the insured asset.
*   **`InsuredParty`**: Represents individuals covered by a policy (e.g., policyholder, owner, driver) and links to a generic `Person` model.
*   **`Coverage`**: Defines the specific guarantees and financial details (limits, deductibles) of a policy.
*   **`Auction`**: Manages the insurance auction data, linked to a user and a asset.
*   **`AuctionBid`**: Stores bids made by brokers on auctions.

For the complete and detailed schema, please see [`prisma/schema.prisma`](prisma/schema.prisma).

## Prerequisites

Before you begin, ensure you have the following installed:

*   **Node.js** (v18.17.0 or higher)
*   **npm** or your preferred package manager
*   **Git**
*   A [Supabase](https://supabase.com/) project for database and authentication
*   A [Google Gemini API Key](https://aistudio.google.com/app/apikey)
*   **Cloudflare R2** account for document storage

## Installation and Setup

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-username/zee-next-app.git
    cd zee-next-app
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    Create a `.env.local` file by copying the example file:
    ```bash
    cp .env.example .env.local
    ```
    Then, fill in the required values in `.env.local`:
    *   `NEXT_PUBLIC_SITE_URL`: The public URL of your application (e.g., `http://localhost:3000`)
    *   `DATABASE_URL`: Your Supabase database connection string (with pooling)
    *   `DIRECT_URL`: Your Supabase direct database connection string (for migrations)
    *   `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
    *   `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
    *   `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key
    *   `SUPABASE_ACCESS_TOKEN`: Your Supabase access token
    *   `GEMINI_API_KEY`: Your Google Gemini API key
    *   `GEMINI_MODEL`: The Gemini model to use (e.g., `gemini-2.5-flash-lite-preview-06-17`)
    *   `BREVO_API_KEY`: Your Brevo API key for email notifications
    *   `R2_ACCOUNT_ID`: Your Cloudflare R2 Account ID
    *   `R2_ACCESS_KEY_ID`: Your Cloudflare R2 Access Key ID
    *   `R2_SECRET_ACCESS_KEY`: Your Cloudflare R2 Secret Access Key
    *   `R2_BUCKET_NAME`: Your Cloudflare R2 bucket name

4.  **Run database migrations:**
    ```bash
    npx prisma migrate dev
    ```

5.  **Seed the database:**
    ```bash
    npm run db:seed
    ```
    
    **Note**: If you encounter issues with the `SIGNED_POLICY` status during seeding, this is a known issue documented in the [August 11, 2025 changelog](docs/changelog/2025-08/2025-08-11-changelog-42.md).

## Getting Started

To get a local copy up and running, follow these simple steps.

### Database Setup

If you are starting from a clean database, or if you need to reset your local environment, run the following commands in order:

1.  **Remove existing migrations:**
    ```bash
    rm -rf prisma/migrations
    ```
2.  **Reset the database:** This command will drop the database, apply all migrations, and ensure your schema is up to date.
    ```bash
    npm run migrate:reset
    ```

3.  **Create the first migration:**
    ```bash
    npm run migrate:dev -- --name "first_schema"
    ```

4.  **Seed the database:** This will populate the database with essential test data, including user accounts for each role.
    ```bash
    npm run db:seed
    ```

### Complete Database Rebuild

For a **complete database rebuild from scratch** (recommended for development and testing), use the master rebuild command:

```bash
npm run db:rebuild
```

This single command performs the following operations in sequence:

1. **`npm run migrate:reset -- --force`**: Drops all data and resets Prisma migrations
2. **`npm run db:setup-infrastructure`**: Sets up Supabase infrastructure:
   - **Extensions**: Enables `pg_cron` and `pg_net` for automation
   - **Functions**: Creates notification logging, statistics, and auction management functions
   - **Cron Jobs**: Schedules auction expiration and notification jobs (every 5 minutes)
3. **`npm run db:setup-migrations`**: Applies additional SQL migrations:
   - **Auction Expiration Logic**: Timezone-aware auction closure with working hours support
   - **Manual Functions**: Testing and emergency auction closure functions
4. **`npm run db:setup-config`**: Configures database settings (may require manual setup)
5. **`npm run db:deploy-functions`**: Deploys Supabase Edge Functions (requires Docker)
6. **`npm run db:apply-policies`**: Applies Row-Level Security (RLS) policies
7. **`npm run db:seed`**: Populates database with comprehensive test data

**Use this command when:**
- Setting up a new development environment
- Resetting your local database completely
- Testing database schema changes
- Preparing for deployment testing

**Note**: The Edge Function deployment step requires Docker to be running. If Docker is not available, the other steps will still complete successfully.


### Test User Credentials

The seed script creates the following test users:

*   **Account Holder:** `<EMAIL>` / `Abcdef7*`
*   **Broker:** `<EMAIL>` / `Abcdef7*`
*   **Admin:** `<EMAIL>` / `Abcdef7*`

## Running the Project

To run the development server:

```bash
npm run dev
```

The application will be available at `http://localhost:3000`.

### Available Scripts

*   `npm run dev`: Starts the development server with Turbopack
*   `npm run build`: Builds the application for production
*   `npm run start`: Starts a production server
*   `npm run lint`: Lints the codebase with custom ESLint rules
*   `npm run migrate:dev`: Runs database migrations for development
*   `npm run db:seed`: Seeds the database with comprehensive test data
*   `npm run db:studio`: Opens Prisma Studio to view and manage your data
*   `npm run db:rebuild`: **Complete database rebuild from scratch** - Resets database, applies Prisma migrations, sets up infrastructure (extensions, functions, cron jobs), deploys Edge Functions, applies RLS policies, and seeds comprehensive test data
*   `npm run db:migrate`: Runs database migrations

### Architecture Quality Assurance
*   `npm run arch-scan`: **Architecture violations scanner** - Comprehensive automated scan for architectural compliance, code quality, and technical debt detection (generates markdown report in `docs/reports/`)
*   `npm run arch-scan:verbose`: Detailed architecture scan with verbose output for debugging

## API Overview

Zeeguros implements a **Factory Pattern API Architecture** with server-side security and consistent patterns across all endpoints. For detailed API specifications and security requirements, see the [Architecture Document](docs/architecture.md).

## Contributing

Contributions are welcome! Please follow the standard GitHub flow:

1.  Fork the repository.
2.  Create a new branch (`git checkout -b feature/your-feature`).
3.  Make your changes and commit them (`git commit -m 'Add some feature'`).
4.  Push to the branch (`git push origin feature/your-feature`).
5.  Open a Pull Request.

For detailed development guidelines, architectural requirements, and coding standards, see the [Development Documentation](CLAUDE.md).

## 📈 Project Status & Roadmap

### **Current Status (September 2025)**

Zeeguros has achieved **TRUE Screaming Architecture** implementation with significant milestones:

- **✅ Core Infrastructure**: Fully implemented with Next.js 15+, TypeScript, and Prisma
- **✅ Authentication & Security**: Complete role-based access control with comprehensive RLS policies
- **✅ Document Management**: Secure file handling with Cloudflare R2 integration
- **✅ AI Integration**: Google Gemini API for intelligent policy data extraction
- **✅ Email Notifications**: Template-based email system with Brevo integration
- **✅ Mobile Responsiveness**: Optimized UI/UX across all device sizes
- **✅ Database Architecture**: Robust schema with proper relationships and constraints
- **✅ Policy Lifecycle**: Complete workflow from DRAFT to EXPIRED status
- **✅ Screaming Architecture**: 95% business domain organization in features layer
- **✅ Standardized Structure**: All business domains follow consistent pattern

### **Upcoming Features**

- **🔗 Complete API Migration**: Finish migrating remaining role-based APIs to business domains
- **💰Stripe Payment Integration**: Secure payment processing for broker bidding
- **📊 Advanced Analytics**: Comprehensive reporting and insights dashboard
- **🌐 Multi-language Support**: Expanded localization beyond Spanish

### **Documentation**

For detailed technical requirements and implementation guidelines, see the comprehensive documentation:

- **[Features Structure Guide](src/features/README.md)**: Complete business domain organization guide
- **[Architecture Overview](docs/architecture.md)**: Complete system architecture and design principles
- **[Product Requirements](docs/plans/drafts/draft-6-screaming-architecture-refactor-prd.md)**: Current development focus and requirements
- **[Change History](docs/changelog/)**: Detailed changelog organized by month
- **[API Documentation](docs/api/)**: Complete API reference and examples

---

**Built with ❤️ by the Zeeguros team** | **Last updated: September 7, 2025**

## License

All rights reserved by ZEEGUROS PLATFORM S.L.